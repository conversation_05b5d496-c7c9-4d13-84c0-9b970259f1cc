# CRM对账单系统需求分析与业务流程

## 📋 项目概述

基于客户需求，开发完整的对账单管理系统，包含对账单创建、开票申请、回款管理三大核心业务流程。

## 🎯 核心需求分析

### 1. 对账单管理
- **基于订单创建**：从现有订单明细选择创建对账单
- **灵活选择**：可选择、删除订单明细项
- **预收款集成**：结合客户预收款进行对账
- **手动添加**：支持手动添加对账明细项
- **批量操作**：支持多订单合并对账

### 2. 开票申请
- **订单合并开票**：多个订单可合并申请开票
- **对账单转换**：基于对账单流程创建开票申请
- **财务审批**：财务部门审批开票申请
- **发票管理**：参照现有格式管理发票信息

### 3. 回款管理
- **单笔回款**：针对单个订单记录回款
- **批量回款**：多个对账单合并回款
- **金额分配**：回款金额自动分配到相关订单
- **状态更新**：订单回款金额实时更新

## 🔄 业务流程设计

### 主要业务流程

```mermaid
graph TD
    A[订单管理] --> B[创建对账单]
    B --> C{选择对账方式}
    C -->|基于订单| D[选择订单明细]
    C -->|手动创建| E[手动添加明细]
    D --> F[结合预收款]
    E --> F
    F --> G[生成对账单]
    G --> H[对账单审核]
    H --> I{审核结果}
    I -->|通过| J[开票申请]
    I -->|驳回| K[修改对账单]
    K --> H
    J --> L[财务审批]
    L --> M{审批结果}
    M -->|通过| N[开具发票]
    M -->|驳回| O[修改申请]
    O --> L
    N --> P[回款管理]
    P --> Q[记录回款]
    Q --> R[更新订单状态]
```

### 详细业务流程

#### 1. 对账单创建流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant S as 系统
    participant DB as 数据库
    
    U->>S: 进入对账单管理
    S->>DB: 查询待对账订单
    DB-->>S: 返回订单列表
    S-->>U: 显示订单选择界面
    
    U->>S: 选择订单明细
    S->>S: 计算对账金额
    U->>S: 添加手动明细(可选)
    S->>S: 汇总对账信息
    
    U->>S: 关联预收款(可选)
    S->>DB: 查询客户预收款
    DB-->>S: 返回预收款信息
    S->>S: 计算净对账金额
    
    U->>S: 提交对账单
    S->>DB: 保存对账单
    DB-->>S: 返回保存结果
    S-->>U: 显示创建成功
```

#### 2. 开票申请流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant S as 系统
    participant F as 财务
    participant DB as 数据库
    
    U->>S: 基于对账单申请开票
    S->>DB: 查询对账单信息
    DB-->>S: 返回对账单详情
    
    U->>S: 选择合并订单(可选)
    S->>S: 计算开票金额
    U->>S: 填写开票信息
    U->>S: 提交开票申请
    
    S->>DB: 保存开票申请
    S->>F: 发送审批通知
    F->>S: 审批开票申请
    
    alt 审批通过
        S->>DB: 更新申请状态
        S->>U: 发送通过通知
        F->>S: 开具发票
        S->>DB: 记录发票信息
    else 审批驳回
        S->>DB: 更新申请状态
        S->>U: 发送驳回通知
        U->>S: 修改申请重新提交
    end
```

#### 3. 回款管理流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant S as 系统
    participant DB as 数据库
    
    U->>S: 进入回款管理
    S->>DB: 查询待回款订单/对账单
    DB-->>S: 返回待回款列表
    S-->>U: 显示回款选择界面
    
    alt 单笔订单回款
        U->>S: 选择单个订单
        U->>S: 输入回款金额
        S->>S: 验证回款金额
        S->>DB: 更新订单回款状态
    else 批量回款
        U->>S: 选择多个对账单
        U->>S: 输入总回款金额
        S->>S: 按比例分配回款
        S->>DB: 批量更新订单状态
    end
    
    S->>DB: 记录回款历史
    DB-->>S: 返回操作结果
    S-->>U: 显示回款成功
```

## 🗂️ 数据流图

### 核心数据实体关系
```mermaid
erDiagram
    ORDER ||--o{ RECONCILIATION_DETAIL : contains
    CUSTOMER ||--o{ ORDER : has
    CUSTOMER ||--o{ PREPAYMENT : has
    RECONCILIATION ||--o{ RECONCILIATION_DETAIL : contains
    RECONCILIATION ||--o{ INVOICE_APPLICATION : generates
    INVOICE_APPLICATION ||--o{ INVOICE : creates
    RECONCILIATION ||--o{ PAYMENT_RECORD : receives
    ORDER ||--o{ PAYMENT_RECORD : updates
    
    ORDER {
        int order_id PK
        int customer_id FK
        string order_no
        decimal total_amount
        decimal paid_amount
        string status
        datetime create_time
    }
    
    CUSTOMER {
        int customer_id PK
        string customer_name
        string contact_info
        decimal prepayment_balance
    }
    
    RECONCILIATION {
        int reconciliation_id PK
        int customer_id FK
        string reconciliation_no
        decimal total_amount
        decimal prepayment_amount
        decimal net_amount
        string status
        datetime create_time
    }
    
    RECONCILIATION_DETAIL {
        int detail_id PK
        int reconciliation_id FK
        int order_id FK
        string item_name
        decimal amount
        string type
    }
    
    INVOICE_APPLICATION {
        int application_id PK
        int reconciliation_id FK
        decimal invoice_amount
        string application_status
        string approval_notes
        datetime apply_time
    }
    
    INVOICE {
        int invoice_id PK
        int application_id FK
        string invoice_no
        decimal invoice_amount
        datetime issue_date
    }
    
    PAYMENT_RECORD {
        int payment_id PK
        int reconciliation_id FK
        int order_id FK
        decimal payment_amount
        datetime payment_date
        string payment_method
    }
    
    PREPAYMENT {
        int prepayment_id PK
        int customer_id FK
        decimal amount
        decimal used_amount
        decimal balance
        string status
    }
```

## 📊 状态流转图

### 对账单状态流转
```mermaid
stateDiagram-v2
    [*] --> 草稿: 创建对账单
    草稿 --> 待审核: 提交审核
    草稿 --> [*]: 删除
    待审核 --> 已审核: 审核通过
    待审核 --> 草稿: 审核驳回
    已审核 --> 已开票: 申请开票
    已审核 --> 已回款: 直接回款
    已开票 --> 已回款: 收到回款
    已回款 --> 已完成: 全部回款
    已完成 --> [*]: 归档
```

### 开票申请状态流转
```mermaid
stateDiagram-v2
    [*] --> 待审批: 提交申请
    待审批 --> 已审批: 财务审批通过
    待审批 --> 已驳回: 财务审批驳回
    已驳回 --> 待审批: 修改后重新提交
    已审批 --> 已开票: 开具发票
    已开票 --> [*]: 完成
```

## 🎯 功能模块设计

### 1. 对账单模块
- **对账单列表**：展示所有对账单，支持筛选和搜索
- **创建对账单**：基于订单或手动创建
- **对账单详情**：查看对账单完整信息
- **对账单编辑**：修改草稿状态的对账单
- **审核管理**：审核流程和状态管理

### 2. 开票申请模块
- **申请列表**：展示所有开票申请
- **创建申请**：基于对账单创建开票申请
- **审批流程**：财务审批工作流
- **发票管理**：发票信息管理和查询

### 3. 回款管理模块
- **回款记录**：所有回款记录展示
- **单笔回款**：针对单个订单回款
- **批量回款**：多对账单合并回款
- **回款统计**：回款数据统计分析

## 🔧 技术实现要点

### 1. 金额计算逻辑
- **对账金额** = 订单金额 + 手动添加金额 - 预收款金额
- **开票金额** = 对账净金额 + 税额
- **回款分配** = 按订单金额比例自动分配

### 2. 并发控制
- **乐观锁**：对账单和订单状态更新
- **事务管理**：确保数据一致性
- **幂等性**：防止重复操作

### 3. 性能优化
- **分页查询**：大数据量列表分页
- **索引优化**：关键字段建立索引
- **缓存机制**：常用数据缓存

## 📝 接口设计规范

### RESTful API 设计
```
GET    /api/reconciliation          # 获取对账单列表
POST   /api/reconciliation          # 创建对账单
GET    /api/reconciliation/{id}     # 获取对账单详情
PUT    /api/reconciliation/{id}     # 更新对账单
DELETE /api/reconciliation/{id}     # 删除对账单

GET    /api/invoice-application     # 获取开票申请列表
POST   /api/invoice-application     # 创建开票申请
PUT    /api/invoice-application/{id}/approve  # 审批开票申请

GET    /api/payment-record          # 获取回款记录
POST   /api/payment-record          # 记录回款
```

## 🎨 UI/UX 设计原则

### 1. 界面设计原则
- **清晰直观**：信息层次分明，操作路径清晰
- **一致性**：保持与现有系统风格一致
- **响应式**：适配不同设备和屏幕尺寸
- **易用性**：操作简单，减少学习成本

### 2. 交互设计
- **即时反馈**：操作后立即给出反馈
- **错误处理**：友好的错误提示和处理
- **确认机制**：重要操作需要确认
- **快捷操作**：提供常用操作的快捷方式

## 📋 开发计划

### 阶段一：基础架构 (2天)
- 数据库表设计和创建
- 基础实体类和Mapper
- 基础Service和Controller框架

### 阶段二：对账单功能 (3天)
- 对账单CRUD操作
- 订单选择和明细管理
- 预收款集成和计算

### 阶段三：开票申请功能 (2天)
- 开票申请流程
- 审批工作流
- 发票管理

### 阶段四：回款管理功能 (2天)
- 回款记录和分配
- 批量回款处理
- 统计报表

### 阶段五：前端界面 (3天)
- 响应式界面设计
- 交互功能实现
- 测试和优化

## 🔍 测试策略

### 1. 单元测试
- Service层业务逻辑测试
- 金额计算逻辑测试
- 状态流转测试

### 2. 集成测试
- 端到端业务流程测试
- 数据库事务测试
- 接口集成测试

### 3. 用户体验测试
- 界面易用性测试
- 操作流程测试
- 性能压力测试
