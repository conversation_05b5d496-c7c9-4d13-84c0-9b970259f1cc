<template>
  <el-drawer
    v-model="visible"
    title="对账单详情"
    direction="rtl"
    size="60%"
    @close="$emit('update:visible', false)"
  >
    <div v-if="reconciliation" class="detail-container">
      <!-- 基本信息 -->
      <el-card class="mb16">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="对账单编号">{{ reconciliation.reconciliationNo }}</el-descriptions-item>
          <el-descriptions-item label="客户名称">{{ reconciliation.customerName }}</el-descriptions-item>
          <el-descriptions-item label="对账日期">{{ reconciliation.reconciliationDate }}</el-descriptions-item>
          <el-descriptions-item label="对账周期">{{ reconciliation.reconciliationPeriod }}</el-descriptions-item>
          <el-descriptions-item label="总金额">
            <el-tag type="success" size="large">￥{{ reconciliation.totalAmount }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(reconciliation.status)">{{ getStatusText(reconciliation.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ reconciliation.createBy }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ reconciliation.createTime }}</el-descriptions-item>
        </el-descriptions>
        <div v-if="reconciliation.remark" class="mt16">
          <el-text class="text-label">备注：</el-text>
          <el-text>{{ reconciliation.remark }}</el-text>
        </div>
      </el-card>

      <!-- 对账明细 -->
      <el-card>
        <template #header>
          <div class="card-header">
            <span>对账明细</span>
            <el-tag type="info" size="small">共 {{ reconciliation.details?.length || 0 }} 项</el-tag>
          </div>
        </template>
        <el-table :data="reconciliation.details" style="width: 100%">
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column label="订单编号" prop="orderNo" />
          <el-table-column label="订单金额" prop="amount" align="right">
            <template #default="scope">
              <el-text type="primary">￥{{ scope.row.amount }}</el-text>
            </template>
          </el-table-column>
          <el-table-column label="明细类型" prop="detailType">
            <template #default="scope">
              <el-tag size="small" :type="scope.row.detailType === 'order' ? 'primary' : 'warning'">
                {{ scope.row.detailType === 'order' ? '订单' : '手动添加' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark" />
        </el-table>
        <div v-if="!reconciliation.details || reconciliation.details.length === 0" class="empty-state">
          <el-empty description="暂无明细数据" />
        </div>
      </el-card>

      <!-- 流程信息（如果有） -->
      <el-card v-if="reconciliation.processInstanceId" class="mt16">
        <template #header>
          <div class="card-header">
            <span>流程信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="流程实例ID">{{ reconciliation.processInstanceId }}</el-descriptions-item>
          <el-descriptions-item label="流程状态">
            <el-tag :type="getProcessStatusType(reconciliation.processStatus)">
              {{ getProcessStatusText(reconciliation.processStatus) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <template #footer>
      <div style="flex: auto">
        <el-button @click="$emit('update:visible', false)">关闭</el-button>
        <el-button v-if="reconciliation?.status === 'draft'" type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Reconciliation } from '../types';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  reconciliation: {
    type: Object as () => Reconciliation | null,
    default: null,
  },
});

const emit = defineEmits(['update:visible', 'edit']);

// 状态类型映射
function getStatusType(status: string) {
  const statusMap: Record<string, 'info' | 'warning' | 'success' | 'danger'> = {
    'draft': 'info',
    'submitted': 'warning',
    'approved': 'success',
    'rejected': 'danger',
  };
  return statusMap[status] || 'info';
}

// 状态文本映射
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    'draft': '草稿',
    'submitted': '待审核',
    'approved': '已审核',
    'rejected': '已驳回',
  };
  return statusMap[status] || status;
}

// 流程状态类型映射
function getProcessStatusType(processStatus?: string) {
  const statusMap: Record<string, 'info' | 'warning' | 'success' | 'danger'> = {
    'not_started': 'info',
    'running': 'warning',
    'suspended': 'warning',
    'completed': 'success',
    'terminated': 'danger',
  };
  return statusMap[processStatus || ''] || 'info';
}

// 流程状态文本映射
function getProcessStatusText(processStatus?: string) {
  const statusMap: Record<string, string> = {
    'not_started': '未启动',
    'running': '运行中',
    'suspended': '已挂起',
    'completed': '已完成',
    'terminated': '已终止',
  };
  return statusMap[processStatus || ''] || processStatus || '未知';
}

// 编辑按钮
function handleEdit() {
  emit('edit', props.reconciliation);
  emit('update:visible', false);
}
</script>

<style scoped>
.detail-container {
  padding: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb16 {
  margin-bottom: 16px;
}

.mt16 {
  margin-top: 16px;
}

.text-label {
  font-weight: bold;
  margin-right: 8px;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}
</style>
