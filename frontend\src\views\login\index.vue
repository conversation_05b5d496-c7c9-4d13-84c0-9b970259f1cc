<template>
  <div class="login-container">
    <div class="login-content">
      <div class="login-left">
        <h1 class="system-name">CRM系统</h1>
        <p class="system-desc">客户关系管理系统</p>
      </div>
      <div class="login-right">
        <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form">
          <h2 class="welcome-text">欢迎登录</h2>
          
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              link
              tabindex="1"
              :prefix-icon="User"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              placeholder="请输入密码"
              :type="passwordVisible ? 'text' : 'password'"
              tabindex="2"
              :prefix-icon="Lock"
              @keyup.enter="handleLogin"
            >
              <template #suffix>
                <el-icon class="show-pwd" @click="passwordVisible = !passwordVisible">
                  <View v-if="passwordVisible" />
                  <Hide v-else />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="code" v-if="captchaEnabled" class="captcha-item">
            <el-input
              v-model="loginForm.code"
              placeholder="请输入验证码"
              style="width: 100%"
              tabindex="3"
              :prefix-icon="CircleCheck"
              @keyup.enter="handleLogin"
            />
            <div class="captcha-img" @click="getCode">
              <img :src="codeUrl" alt="验证码" />
            </div>
          </el-form-item>

          <div class="form-footer">
            <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
            <el-link type="primary">忘记密码？</el-link>
          </div>

          <el-button 
            :loading="loading" 
            type="primary" 
            class="login-button" 
            @click="handleLogin"
          >
            登录
          </el-button>
          
          <div class="other-login-options">
            <div class="divider">
              <span class="divider-text">其他登录方式</span>
            </div>
            <div class="social-login-buttons">
              <el-button
                class="wecom-login-btn"
                type="success"
                circle
                @click.prevent="handleWecomLogin"
              >
                <el-icon><ChatDotRound /></el-icon>
              </el-button>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getCodeImg, getWecomQrCodeUrl } from '@/api/login';
import { useUserStore } from '@/store/modules/user';
import type { WecomResponse } from '@/types';
import { ChatDotRound, CircleCheck, Hide, Lock, User, View } from '@element-plus/icons-vue';
import type { FormInstance } from 'element-plus';
import { ElMessage } from 'element-plus';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import type { CodeImgResponse, LoginForm } from './types';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

const loginForm = ref<LoginForm>({
  username: '',
  password: '',
  code: '',
  uuid: '',
  rememberMe: false
}); // 添加分号

const loginRules = {
  username: [{ required: true, trigger: 'blur', message: '请输入用户名' }],
  password: [{ required: true, trigger: 'blur', message: '请输入密码' }],
  code: [{ required: true, trigger: 'blur', message: '请输入验证码' }]
};

const loginFormRef = ref<FormInstance>();
const loading = ref(false);
const passwordVisible = ref(false);
const captchaEnabled = ref(true);
const codeUrl = ref('');

const getCode = async () => {
  const res = await getCodeImg() as unknown as CodeImgResponse;
  if (res.code === 200) {
    codeUrl.value = 'data:image/gif;base64,' + res.img;
    loginForm.value.uuid = res.uuid || '';
    captchaEnabled.value = res.captchaEnabled || false;
  }
};

const handleLogin = async () => {
  if (!loginFormRef.value) return;
  
  try {
    await loginFormRef.value.validate();
    loading.value = true;
    
    const success = await userStore.login(loginForm.value);
    // console.log("success ", JSON.stringify(success, null, 2));
    if (success) {
      const redirect = route.query.redirect as string;
      router.replace(redirect || '/');
    } else {
      if (captchaEnabled.value) {
        getCode();
      }
    }
  } catch (error: any) {
    console.error('登录失败:', error);
    if (captchaEnabled.value) {
      getCode();
    }
  } finally {
    loading.value = false;
  }
};

// 处理企业微信登录
const handleWecomLogin = async () => {
  try {
    const res = await getWecomQrCodeUrl() as WecomResponse<string>;
    console.log("res ", res);
    if (res.code === 200) {
      window.location.href = res.data;
    } else {
      ElMessage.error(res.msg || '获取企业微信登录URL失败');
    }
  } catch (error) {
    console.error('获取企业微信登录URL失败:', error);
    ElMessage.error('获取企业微信登录URL失败，请稍后重试');
  }
};

onMounted(() => {
  if (captchaEnabled.value) {
    getCode();
  }
});
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as vars;

.login-container {
  min-height: 100vh;
  width: 100%;
  background: vars.$gradient-background-login;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.login-content {
  display: flex;
  width: 1000px;
  height: 600px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  overflow: hidden;
  animation: fadeIn 0.6s ease-in-out;
}

.login-left {
  flex: 1;
  background: vars.$linear-gradient-background-main;
  padding: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: 
      radial-gradient(ellipse at 30% 40%, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.3) 20%, transparent 50%),
      radial-gradient(circle at 70% 60%, rgba(255,255,255,0.6) 0%, rgba(255,255,255,0.2) 15%, transparent 40%);
    animation: rotate 10s linear infinite;
    transform-origin: center center;
    will-change: transform;
    z-index: 0;
  }

  .system-name {
    font-size: 42px;
    font-weight: 500;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 10px rgba(255,255,255,0.2);
    position: relative;
    z-index: 1;
  }

  .system-desc {
    font-size: 20px;
    font-weight: 300;
    opacity: 0.95;
    text-shadow:  -8px 6px 14px rgb(0 0 0 / 42%);

    position: relative;
    z-index: 1;
  }
}

.login-right {
  flex: 1;
  padding: 50px;
  display: flex;
  align-items: center;
  background: #fff;
}

.login-form {
  width: 100%;
  max-width: 380px;
  margin: 0 auto;

  .welcome-text {
    font-size: vars.$font-size-extra-large;
    color: vars.$color-text-primary;
    margin-bottom: 40px;
    text-align: center;
    font-weight: 600;
  }

  :deep(.el-input__wrapper) {
    background-color: vars.$background-color-base;
    border-radius: vars.$border-radius-base * 2;
    box-shadow: none;
    transition: all 0.3s ease;

    &:hover, &.is-focus {
      background-color: #fff;
      box-shadow: 0 0 0 1px vars.$color-primary;
    }
  }

  :deep(.el-input__inner) {
    height: 45px;
  }

  :deep(.el-form-item) {
    margin-bottom: 25px;
  }
}

.captcha-item {
  position: relative;
  
  .captcha-img {
    position: absolute;
    right: 1px;
    top: 1px;
    height: calc(100% - 2px);
    width: 100px;
    cursor: pointer;
    overflow: hidden;
    
    img {
      height: 100%;
      width: 100%;
      border-radius: 0 8px 8px 0;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }
}

.form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 25px 0;

  :deep(.el-checkbox__label) {
    color: vars.$color-text-regular;
  }

  :deep(.el-link) {
    font-size: 14px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateX(3px);
    }
  }
}

.login-button {
  width: 100%;
  height: 45px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, vars.$color-primary 0%, #36D1DC 100%);
  border: none;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(vars.$color-primary, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.other-login-options {
  margin-top: 25px;
  
  .divider {
    display: flex;
    align-items: center;
    margin: 15px 0;
    
    &::before,
    &::after {
      content: '';
      flex: 1;
      height: 1px;
      background-color: #e0e0e0;
    }
    
    .divider-text {
      padding: 0 15px;
      font-size: 14px;
      color: #909399;
    }
  }
  
  .social-login-buttons {
    display: flex;
    justify-content: center;
    margin-top: 15px;
    
    .wecom-login-btn {
      font-size: 20px;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(#67c23a, 0.3);
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>