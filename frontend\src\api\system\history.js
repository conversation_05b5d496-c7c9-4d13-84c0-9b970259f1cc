import request from '@/utils/request'

// 查询审批历史列表
export function listHistory(query) {
  return request({
    url: '/system/history/list',
    method: 'get',
    params: query
  })
}

// 查询审批历史详细
export function getHistory(historyId) {
  return request({
    url: '/system/history/' + historyId,
    method: 'get'
  })
}

// 新增审批历史
export function addHistory(data) {
  return request({
    url: '/system/history',
    method: 'post',
    data: data
  })
}

// 修改审批历史
export function updateHistory(data) {
  return request({
    url: '/system/history',
    method: 'put',
    data: data
  })
}

// 删除审批历史
export function delHistory(historyId) {
  return request({
    url: '/system/history/' + historyId,
    method: 'delete'
  })
}
