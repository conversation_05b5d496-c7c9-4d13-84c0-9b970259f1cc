package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CrmBusinessContractUserRelationsMapper;
import com.ruoyi.system.domain.CrmBusinessContractUserRelations;
import com.ruoyi.system.service.ICrmBusinessContractUserRelationsService;

/**
 * 合同用户关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class CrmBusinessContractUserRelationsServiceImpl implements ICrmBusinessContractUserRelationsService 
{
    @Autowired
    private CrmBusinessContractUserRelationsMapper crmBusinessContractUserRelationsMapper;

    /**
     * 查询合同用户关系
     * 
     * @param id 合同用户关系主键
     * @return 合同用户关系
     */
    @Override
    public CrmBusinessContractUserRelations selectCrmBusinessContractUserRelationsById(Long id)
    {
        return crmBusinessContractUserRelationsMapper.selectCrmBusinessContractUserRelationsById(id);
    }

    /**
     * 查询合同用户关系列表
     * 
     * @param crmBusinessContractUserRelations 合同用户关系
     * @return 合同用户关系
     */
    @Override
    public List<CrmBusinessContractUserRelations> selectCrmBusinessContractUserRelationsList(CrmBusinessContractUserRelations crmBusinessContractUserRelations)
    {
        return crmBusinessContractUserRelationsMapper.selectCrmBusinessContractUserRelationsList(crmBusinessContractUserRelations);
    }

    /**
     * 新增合同用户关系
     * 
     * @param crmBusinessContractUserRelations 合同用户关系
     * @return 结果
     */
    @Override
    public int insertCrmBusinessContractUserRelations(CrmBusinessContractUserRelations crmBusinessContractUserRelations)
    {
        return crmBusinessContractUserRelationsMapper.insertCrmBusinessContractUserRelations(crmBusinessContractUserRelations);
    }

    /**
     * 修改合同用户关系
     * 
     * @param crmBusinessContractUserRelations 合同用户关系
     * @return 结果
     */
    @Override
    public int updateCrmBusinessContractUserRelations(CrmBusinessContractUserRelations crmBusinessContractUserRelations)
    {
        return crmBusinessContractUserRelationsMapper.updateCrmBusinessContractUserRelations(crmBusinessContractUserRelations);
    }

    /**
     * 批量删除合同用户关系
     * 
     * @param ids 需要删除的合同用户关系主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessContractUserRelationsByIds(Long[] ids)
    {
        return crmBusinessContractUserRelationsMapper.deleteCrmBusinessContractUserRelationsByIds(ids);
    }

    /**
     * 删除合同用户关系信息
     * 
     * @param id 合同用户关系主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessContractUserRelationsById(Long id)
    {
        return crmBusinessContractUserRelationsMapper.deleteCrmBusinessContractUserRelationsById(id);
    }
}
