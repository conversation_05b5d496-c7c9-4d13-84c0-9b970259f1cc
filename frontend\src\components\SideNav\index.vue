<template>
    <div class="side-nav" :class="{ 'nav-collapsed': isCollapse }">
        <div class="nav-header" @click="toggleCollapse">
            <h3 v-if="!isCollapse">{{ title }}</h3>
            <el-icon><component :is="isCollapse ? 'Expand' : 'Fold'" /></el-icon>
        </div>

        <div class="nav-list">
            <div 
                v-for="item in menuItems"
                :key="item.key"
                :class="['nav-item', { active: modelValue === item.key }]"
                @click="handleTabChange(item.key)"
            >
                <el-icon><component :is="item.icon" /></el-icon>
                <span v-if="!isCollapse" class="nav-label">{{ item.label }}</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Expand, Fold } from '@element-plus/icons-vue';
import { ref } from 'vue';

interface MenuItem {
    key: string;
    label: string;
    icon: string;
}

interface Props {
    title: string;
    menuItems: MenuItem[];
    modelValue: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: string): void;
}>();

const isCollapse = ref(false);

const toggleCollapse = () => {
    isCollapse.value = !isCollapse.value;
};

const handleTabChange = (key: string) => {
    emit('update:modelValue', key);
};
</script>

<style scoped>
.side-nav {
    width: 180px;
    border-right: 1px solid #ebeef5;
    height: 100%;
    transition: width 0.3s;
    background-color: #f6f8fa;
}

.nav-collapsed {
    width: 64px;
}

.nav-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    height: 50px;
    cursor: pointer;
    border-bottom: 1px solid #ebeef5;
}

.nav-header h3 {
    margin: 0;
    font-size: 15px;
    font-weight: 500;
    color: #606266;
}

.nav-list {
    padding: 6px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0 16px;
    height: 40px;
    margin: 4px 0;
    cursor: pointer;
    font-size: 14px;
    color: #606266;
    transition: all 0.3s;
    position: relative;
    
    &:hover {
        background-color: #ecf5ff;
        color: #409eff;
    }
    
    &.active {
        background-color: #ecf5ff;
        color: #409eff;
        
        &::after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: #409eff;
        }
    }

    .el-icon {
        font-size: 16px;
        margin-right: 8px;
        color: inherit;
    }
}

.nav-label {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style> 