<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CrmBusinessApApprovalHistoryMapper">
    
    <resultMap type="CrmBusinessApApprovalHistory" id="CrmBusinessApApprovalHistoryResult">
        <result property="historyId"    column="history_id"    />
        <result property="instanceNodeId"    column="instance_node_id"    />
        <result property="userId"    column="user_id"    />
        <result property="actionType"    column="action_type"    />
        <result property="actionTimestamp"    column="action_timestamp"    />
        <result property="actionComments"    column="action_comments"    />
        <result property="applicationId"    column="application_id"    />
        <result property="approvalId"    column="approval_id"    />
        <result property="previousHistoryId"    column="previous_history_id"    />
        <result property="previousInstanceNodeId"    column="previous_instance_node_id"    />
        <result property="previousApplicant"    column="previous_applicant"    />
        <result property="previousApproverType"    column="previous_approver_type"    />
        <result property="selectedTeam"    column="selected_team"    />
        <result property="selectedTeamMember"    column="selected_team_member"    />
        <result property="selectUser"    column="select_user"    />
    </resultMap>

    <sql id="selectCrmBusinessApApprovalHistoryVo">
        select history_id, instance_node_id, user_id, action_type, action_timestamp, action_comments, application_id, approval_id, previous_history_id, previous_instance_node_id, previous_applicant, previous_approver_type, selected_team, selected_team_member, select_user from crm_business_ap_approval_history
    </sql>

    <select id="selectCrmBusinessApApprovalHistoryList" parameterType="CrmBusinessApApprovalHistory" resultMap="CrmBusinessApApprovalHistoryResult">
        <include refid="selectCrmBusinessApApprovalHistoryVo"/>
        <where>  
            <if test="instanceNodeId != null "> and instance_node_id = #{instanceNodeId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="actionType != null  and actionType != ''"> and action_type = #{actionType}</if>
            <if test="actionTimestamp != null "> and action_timestamp = #{actionTimestamp}</if>
            <if test="actionComments != null  and actionComments != ''"> and action_comments = #{actionComments}</if>
            <if test="applicationId != null "> and application_id = #{applicationId}</if>
            <if test="approvalId != null "> and approval_id = #{approvalId}</if>
            <if test="previousHistoryId != null "> and previous_history_id = #{previousHistoryId}</if>
            <if test="previousInstanceNodeId != null "> and previous_instance_node_id = #{previousInstanceNodeId}</if>
            <if test="previousApplicant != null "> and previous_applicant = #{previousApplicant}</if>
            <if test="previousApproverType != null  and previousApproverType != ''"> and previous_approver_type = #{previousApproverType}</if>
            <if test="selectedTeam != null  and selectedTeam != ''"> and selected_team = #{selectedTeam}</if>
            <if test="selectedTeamMember != null  and selectedTeamMember != ''"> and selected_team_member = #{selectedTeamMember}</if>
            <if test="selectUser != null  and selectUser != ''"> and select_user = #{selectUser}</if>
        </where>
    </select>
    
    <select id="selectCrmBusinessApApprovalHistoryByHistoryId" parameterType="Long" resultMap="CrmBusinessApApprovalHistoryResult">
        <include refid="selectCrmBusinessApApprovalHistoryVo"/>
        where history_id = #{historyId}
    </select>

    <insert id="insertCrmBusinessApApprovalHistory" parameterType="CrmBusinessApApprovalHistory" useGeneratedKeys="true" keyProperty="historyId">
        insert into crm_business_ap_approval_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="instanceNodeId != null">instance_node_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="actionType != null">action_type,</if>
            <if test="actionTimestamp != null">action_timestamp,</if>
            <if test="actionComments != null">action_comments,</if>
            <if test="applicationId != null">application_id,</if>
            <if test="approvalId != null">approval_id,</if>
            <if test="previousHistoryId != null">previous_history_id,</if>
            <if test="previousInstanceNodeId != null">previous_instance_node_id,</if>
            <if test="previousApplicant != null">previous_applicant,</if>
            <if test="previousApproverType != null">previous_approver_type,</if>
            <if test="selectedTeam != null">selected_team,</if>
            <if test="selectedTeamMember != null">selected_team_member,</if>
            <if test="selectUser != null">select_user,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="instanceNodeId != null">#{instanceNodeId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="actionType != null">#{actionType},</if>
            <if test="actionTimestamp != null">#{actionTimestamp},</if>
            <if test="actionComments != null">#{actionComments},</if>
            <if test="applicationId != null">#{applicationId},</if>
            <if test="approvalId != null">#{approvalId},</if>
            <if test="previousHistoryId != null">#{previousHistoryId},</if>
            <if test="previousInstanceNodeId != null">#{previousInstanceNodeId},</if>
            <if test="previousApplicant != null">#{previousApplicant},</if>
            <if test="previousApproverType != null">#{previousApproverType},</if>
            <if test="selectedTeam != null">#{selectedTeam},</if>
            <if test="selectedTeamMember != null">#{selectedTeamMember},</if>
            <if test="selectUser != null">#{selectUser},</if>
         </trim>
    </insert>

    <update id="updateCrmBusinessApApprovalHistory" parameterType="CrmBusinessApApprovalHistory">
        update crm_business_ap_approval_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="instanceNodeId != null">instance_node_id = #{instanceNodeId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="actionType != null">action_type = #{actionType},</if>
            <if test="actionTimestamp != null">action_timestamp = #{actionTimestamp},</if>
            <if test="actionComments != null">action_comments = #{actionComments},</if>
            <if test="applicationId != null">application_id = #{applicationId},</if>
            <if test="approvalId != null">approval_id = #{approvalId},</if>
            <if test="previousHistoryId != null">previous_history_id = #{previousHistoryId},</if>
            <if test="previousInstanceNodeId != null">previous_instance_node_id = #{previousInstanceNodeId},</if>
            <if test="previousApplicant != null">previous_applicant = #{previousApplicant},</if>
            <if test="previousApproverType != null">previous_approver_type = #{previousApproverType},</if>
            <if test="selectedTeam != null">selected_team = #{selectedTeam},</if>
            <if test="selectedTeamMember != null">selected_team_member = #{selectedTeamMember},</if>
            <if test="selectUser != null">select_user = #{selectUser},</if>
        </trim>
        where history_id = #{historyId}
    </update>

    <delete id="deleteCrmBusinessApApprovalHistoryByHistoryId" parameterType="Long">
        delete from crm_business_ap_approval_history where history_id = #{historyId}
    </delete>

    <delete id="deleteCrmBusinessApApprovalHistoryByHistoryIds" parameterType="String">
        delete from crm_business_ap_approval_history where history_id in 
        <foreach item="historyId" collection="array" open="(" separator="," close=")">
            #{historyId}
        </foreach>
    </delete>
</mapper>