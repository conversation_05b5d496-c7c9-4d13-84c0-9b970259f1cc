package com.ruoyi.common.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发票对象 crm_invoices
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmInvoice extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 发票编号 */
    private String invoiceNo;

    /** 发票抬头 */
    private String invoiceTitle;

    /** 发票类型：special-增值税专用发票,ordinary-增值税普通发票,electronic-电子发票 */
    private String invoiceType;

    /** 客户ID */
    private Long customerId;

    /** 客户名称（冗余字段） */
    private String customerName;

    /** 联系人ID */
    private Long contactId;

    /** 联系人姓名（冗余字段） */
    private String contactName;

    /** 关联报价单ID */
    private Long quotationId;

    /** 关联合同ID */
    private Long contractId;

    /** 合同编号（冗余字段） */
    private String contractNo;

    /** 负责人ID */
    private String responsiblePersonId;

    /** 纳税人识别号 */
    private String taxpayerId;

    /** 税率 */
    private BigDecimal taxRate;

    /** 不含税金额 */
    private BigDecimal amountExcludingTax;

    /** 税额 */
    private BigDecimal taxAmount;

    /** 含税金额 */
    private BigDecimal amountIncludingTax;

    /** 开户银行 */
    private String bankName;

    /** 银行账号 */
    private String bankAccount;

    /** 公司地址 */
    private String companyAddress;

    /** 公司电话 */
    private String companyPhone;

    /** 发票状态：draft-草稿,submitted-已提交,approved-已审批,rejected-已驳回,issued-已开票,cancelled-已作废,returned-已退票 */
    private String status;

    /** 审批状态：pending-待审批,approved-审批通过,rejected-审批驳回 */
    private String approvalStatus;

    /** 流程实例ID */
    private String processInstanceId;

    /** 流程状态：running-运行中,completed-完成,terminated-终止 */
    private String processStatus;

    /** 当前任务ID */
    private String currentTaskId;

    /** 当前任务名称 */
    private String currentTaskName;

    /** 当前处理人 */
    private String currentAssignee;

    /** 流程开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processStartTime;

    /** 流程结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processEndTime;

    /** 开票日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date issueDate;

    /** 到期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dueDate;

    /** 作废日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date cancelDate;

    /** 作废原因 */
    private String cancelReason;

    /** 币种 */
    private String currency;

    /** 汇率 */
    private BigDecimal exchangeRate;

    /** 备注 */
    private String remarks;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 发票明细列表 */
    private List<CrmInvoiceItem> invoiceItems;

    /** 发票附件列表 */
    private List<CrmInvoiceAttachment> invoiceAttachments;
}