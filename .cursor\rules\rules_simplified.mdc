---
description: 
globs: 
alwaysApply: true
---
# 项目结构规范

## 项目说明
- `ruoyi - crm`：若依CRM模块，含所有CRM后端代码
- `frontend`：前端项目，基于Vue3 + TypeScript + Element Plus

## 目录规范
### 后端目录结构 (ruoyi - crm)
```
ruoyi - crm/
├── src/main/java/com/crm/
│   ├── controller/
│   ├── service/
│   ├── domain/
│   │   ├── entity/
│   │   └── dto/
│   └── mapper/
└── src/main/resources/
    └── mapper/
```

### 前端目录结构 (frontend)
```
frontend/
├── src/
│   ├── api/
│   │   └── crm/
│   │       ├── customer/
│   │       ├── contact/
│   │       └── leads/
│   ├── views/
│   └── components/
```

## 开发规范
### 后端开发规范
1. CRM代码放`ruoyi - crm`模块
2. 用MyBatis - Plus操作数据库
3. 统一响应格式`AjaxResult`
4. 业务异常用`BusinessException`

### 前端开发规范
1. API接口按功能模块分类存放
2. 用TypeScript定义接口类型
3. 组件用Vue3 Composition API
4. 页面组件放views，通用组件放components

## 命名规范
1. 后端：
   - Controller类名以Controller结尾
   - Service类名以Service结尾
   - 实体类名与表名对应
   - DTO类名以DTO结尾
2. 前端：
   - API文件名小写，按功能命名
   - 组件文件名PascalCase
   - API函数名camelCase
   - TypeScript类型名PascalCase

## 注意事项
1. 不随意创建新项目模块
2. 保持代码结构清晰，遵循目录结构
3. 代码变更在对应模块进行
4. API接口按功能模块分类存放
