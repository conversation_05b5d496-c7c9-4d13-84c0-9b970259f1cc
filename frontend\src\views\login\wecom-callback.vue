<template>
  <div class="wecom-callback">
    <div class="loading-container" v-if="loading">
      <el-icon class="loading-icon" :size="24"><Loading /></el-icon>
      <p class="loading-text">正在登录中...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store/modules/user';
import { setToken } from '@/utils/auth';
import { wecomLogin } from '@/api/wechatwork/login';
import { Loading } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const router = useRouter();
const route = useRoute();
const loading = ref(true);

onMounted(async () => {
  try {
    // 从URL获取企业微信的授权code和state
    const { code, state } = route.query;
    if (!code) {
      throw new Error('未获取到授权码');
    }

    // 调用企业微信登录接口
    const res = await wecomLogin(code, state);

    if (res.code === 200) {
      // 保存token
      setToken(res.data.token);
      // 获取用户信息
      const userStore = useUserStore();
      await userStore.getInfo();
      // 跳转到首页
      router.replace('/');
    } else {
      throw new Error(res.msg || '登录失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '登录失败');
    router.replace('/login');
  } finally {
    loading.value = false;
  }
});
</script>

<style lang="scss" scoped>
.wecom-callback {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.loading-container {
  text-align: center;

  .loading-icon {
    animation: rotate 1s linear infinite;
    color: #409eff;
  }

  .loading-text {
    margin-top: 16px;
    color: #606266;
    font-size: 14px;
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>