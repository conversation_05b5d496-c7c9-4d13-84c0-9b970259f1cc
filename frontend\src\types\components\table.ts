import type { ButtonType } from 'element-plus';

// 表格操作按钮配置接口
export interface TableOperationButton {
    label: string;
    type?: ButtonType;
    icon?: string;
    handler: string | ((row: any) => void);
    show?: boolean | ((row: any) => boolean);
    link?: boolean;
    disabled?: boolean | ((row: any) => boolean);
}

// 表格操作配置接口
export interface TableOperationsConfig {
    width?: number | string;
    fixed?: boolean | 'left' | 'right';
    buttons: TableOperationButton[];
}

// 表格操作事件接口
export interface OperationEvent {
    handler: string | ((row: any) => void);
    row: any;
}

// 表格列配置接口
export interface TableColumn {
    type?: 'selection' | 'index' | 'expand';
    prop?: string;
    label?: string;
    width?: number | string;
    sortable?: boolean;
    fixed?: boolean | 'left' | 'right';
} 