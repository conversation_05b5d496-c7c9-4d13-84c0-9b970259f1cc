package com.ruoyi.system.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.domain.entity.CrmBusinessApApprovalHistory;
import com.ruoyi.system.service.ICrmBusinessApApprovalHistoryService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 审批历史Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Api(tags = "审批历史管理")
@RestController
@RequestMapping("/system/history")
public class CrmBusinessApApprovalHistoryController extends BaseController {
    @Autowired
    private ICrmBusinessApApprovalHistoryService crmBusinessApApprovalHistoryService;

    /**
     * 查询审批历史列表
     */
    @ApiOperation("查询审批历史列表")
    @PreAuthorize("@ss.hasPermi('system:history:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusinessApApprovalHistory crmBusinessApApprovalHistory) {
        startPage();
        List<CrmBusinessApApprovalHistory> list = crmBusinessApApprovalHistoryService
                .selectCrmBusinessApApprovalHistoryList(crmBusinessApApprovalHistory);
        return getDataTable(list);
    }

    /**
     * 导出审批历史列表
     */
    @ApiOperation("导出审批历史列表")
    @PreAuthorize("@ss.hasPermi('system:history:export')")
    @Log(title = "审批历史", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmBusinessApApprovalHistory crmBusinessApApprovalHistory) {
        List<CrmBusinessApApprovalHistory> list = crmBusinessApApprovalHistoryService
                .selectCrmBusinessApApprovalHistoryList(crmBusinessApApprovalHistory);
        ExcelUtil<CrmBusinessApApprovalHistory> util = new ExcelUtil<CrmBusinessApApprovalHistory>(
                CrmBusinessApApprovalHistory.class);
        util.exportExcel(response, list, "审批历史数据");
    }

    /**
     * 获取审批历史详细信息
     */
    @ApiOperation("获取审批历史详细信息")
    @PreAuthorize("@ss.hasPermi('system:history:query')")
    @GetMapping(value = "/{historyId}")
    public AjaxResult getInfo(@ApiParam("审批历史ID") @PathVariable("historyId") Long historyId) {
        return success(crmBusinessApApprovalHistoryService.selectCrmBusinessApApprovalHistoryByHistoryId(historyId));
    }

    /**
     * 新增审批历史
     */
    @ApiOperation("新增审批历史")
    @PreAuthorize("@ss.hasPermi('system:history:add')")
    @Log(title = "审批历史", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("审批历史信息") @RequestBody CrmBusinessApApprovalHistory crmBusinessApApprovalHistory) {
        return toAjax(
                crmBusinessApApprovalHistoryService.insertCrmBusinessApApprovalHistory(crmBusinessApApprovalHistory));
    }

    /**
     * 修改审批历史
     */
    @ApiOperation("修改审批历史")
    @PreAuthorize("@ss.hasPermi('system:history:edit')")
    @Log(title = "审批历史", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("审批历史信息") @RequestBody CrmBusinessApApprovalHistory crmBusinessApApprovalHistory) {
        return toAjax(
                crmBusinessApApprovalHistoryService.updateCrmBusinessApApprovalHistory(crmBusinessApApprovalHistory));
    }

    /**
     * 删除审批历史
     */
    @ApiOperation("删除审批历史")
    @PreAuthorize("@ss.hasPermi('system:history:remove')")
    @Log(title = "审批历史", businessType = BusinessType.DELETE)
    @DeleteMapping("/{historyIds}")
    public AjaxResult remove(@ApiParam("审批历史ID数组") @PathVariable Long[] historyIds) {
        return toAjax(crmBusinessApApprovalHistoryService.deleteCrmBusinessApApprovalHistoryByHistoryIds(historyIds));
    }
}
