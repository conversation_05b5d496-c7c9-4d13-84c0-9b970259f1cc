import request from '@/utils/request'

// 查询合同用户关系列表
export function listRelations(query) {
  return request({
    url: '/system/relations/list',
    method: 'get',
    params: query
  })
}

// 查询合同用户关系详细
export function getRelations(id) {
  return request({
    url: '/system/relations/' + id,
    method: 'get'
  })
}

// 新增合同用户关系
export function addRelations(data) {
  return request({
    url: '/system/relations',
    method: 'post',
    data: data
  })
}

// 修改合同用户关系
export function updateRelations(data) {
  return request({
    url: '/system/relations',
    method: 'put',
    data: data
  })
}

// 删除合同用户关系
export function delRelations(id) {
  return request({
    url: '/system/relations/' + id,
    method: 'delete'
  })
}
