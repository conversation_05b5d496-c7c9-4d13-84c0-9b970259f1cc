<template>
  <div>
    <div>测试</div>
    <div class="upload-section">
      <slot name="header">
        <div class="section-header">
          <h2>上传图纸以及选择加工参数</h2>
          <p class="sub-title">请上传您的3D模型文件，我们将为您提供精准报价</p>
        </div>
      </slot>

      <p class="warning-text">
        尊敬的客户，严禁利用本平台下单生产违禁产品，如管制刀具、枪支弹药等。一经发现，立即封号并上报公安局。感谢您的支持与合作！
      </p>

      <el-upload class="upload-area" drag action="#" multiple :limit="20" :on-exceed="handleExceed"
        :file-list="fileList" :on-preview="handlePreview" :on-remove="handleRemove" :before-upload="beforeUpload"
        :http-request="httpRequest" :on-success="handleUploadSuccess">

        <div class="upload-content">
          <el-icon class="upload-icon">
            <Upload />
          </el-icon>
          <div class="upload-text">拖拽或者上传3D图纸文件或压缩包</div>
          <el-button type="primary" class="upload-button">选择3D图纸文件</el-button>
        </div>

        <div class="upload-tips">
          <p>速加将对您的文件绝对保密，保护您的知识产权</p>
          <p>支持格式：STEP(.step, .stp)，STL(.stl)</p>
          <p>可压缩包（zip、rar）直接上传</p>
        </div>

        <div class="upload-limit">
          单次上传文件 ≤20 个，单个文件大小<100M </div>
      </el-upload>
    </div>
  </div>
</template>

<script setup>
// 初始空脚本

import { Upload } from '@element-plus/icons-vue'
import { ElButton, ElIcon, ElUpload } from 'element-plus'
import { onBeforeMount, onBeforeUpdate, onMounted, onUpdated } from 'vue'

onBeforeMount(() => {
  console.log('test beforeMount')
})

onMounted(() => {
  console.log('test mounted')
})

onBeforeUpdate(() => {
  console.log('test beforeUpdate')
})

onUpdated(() => {
  console.log('test updated')
})

const props = defineProps({
  fileList: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits([
  'exceed',
  'preview',
  'remove',
  'before-upload',
  'upload-success'
])

const handleExceed = (files, fileList) => {
  emit('exceed', files, fileList)
}

const handlePreview = (file) => {
  emit('preview', file)
}

const handleRemove = (file, fileList) => {
  emit('remove', file, fileList)
}

const beforeUpload = (file) => {
  emit('before-upload', file)
}

const httpRequest = (options) => {
  emit('http-request', options)
}

const handleUploadSuccess = (response, file, fileList) => {
  emit('upload-success', response, file, fileList)
}
</script>

<style>
.upload-section {
  padding: 20px;
  border: 2px solid blue;
}

.section-header {
  text-align: center;
  margin-bottom: 30px;
}

.section-header h2 {
  font-size: 24px;
  color: #303133;
  margin-bottom: 10px;
}

.section-header .sub-title {
  font-size: 14px;
  color: #909399;
}

.warning-text {
  color: #f02b2b;
  background-color: #feeaea;
  text-align: center;
  margin-bottom: 20px;
  padding: 10px;
}

.upload-area {
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  background: #fafafa;
  transition: all 0.3s;
}

.upload-area:hover {
  border-color: #409EFF;
  background: #f5f7fa;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.upload-icon {
  font-size: 48px;
  color: #909399;
}

.upload-text {
  font-size: 16px;
  color: #606266;
}

.upload-button {
  padding: 12px 24px;
  font-size: 14px;
}

.upload-tips {
  margin-top: 20px;
  color: #909399;
  font-size: 13px;
  line-height: 1.8;
}

.upload-limit {
  margin-top: 15px;
  color: #909399;
  font-size: 12px;
}
</style>
