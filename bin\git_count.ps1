# 获取当前日期范围（00:00:00 到 23:59:59）
$todayStart = (Get-Date).Date
$todayEnd = $todayStart.AddDays(1).AddSeconds(-1)

# 获取 Git 用户名（用于过滤当前用户的提交）
$gitUser = git config user.email

# 执行 Git 命令获取今日提交的统计信息
$stats = git log --author="$gitUser" --since="$todayStart" --until="$todayEnd" --pretty=tformat: --numstat

# 初始化计数器
$totalAdded = 0
$totalDeleted = 0

# 处理每行统计结果
foreach ($line in $stats -split "`n") {
    if ($line -match '^(\d+)\s+(\d+)\s+') {
        $totalAdded += [int]$matches[1]
        $totalDeleted += [int]$matches[2]
    }
}

# 输出结果
Write-Host "今日提交统计 ($(Get-Date -Format 'yyyy-MM-dd'))" -ForegroundColor Cyan
Write-Host "------------------------------------"
Write-Host "添加行数: $totalAdded" -ForegroundColor Green
Write-Host "删除行数: $totalDeleted" -ForegroundColor Red
Write-Host "净变化量: $($totalAdded - $totalDeleted)" -ForegroundColor Yellow
Write-Host "------------------------------------"