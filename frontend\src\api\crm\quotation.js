import request from '@/utils/request'

// 查询报价单列表
export function listQuotation(query) {
  return request({
    url: '/crm/quotation/list',
    method: 'get',
    params: query
  })
}

// 查询报价单详细
export function getQuotation(id) {
  return request({
    url: '/crm/quotation/' + id,
    method: 'get'
  })
}

// 根据报价单编号查询报价单
export function getQuotationByNo(quotationNo) {
  return request({
    url: '/crm/quotation/quotationNo/' + quotationNo,
    method: 'get'
  })
}

// 新增报价单
export function addQuotation(data) {
  return request({
    url: '/crm/quotation',
    method: 'post',
    data: data
  })
}

// 修改报价单
export function updateQuotation(data) {
  return request({
    url: '/crm/quotation',
    method: 'put',
    data: data
  })
}

// 删除报价单
export function delQuotation(ids) {
  return request({
    url: '/crm/quotation/' + ids,
    method: 'delete'
  })
}

// 提交报价单审批
export function submitQuotationApproval(id) {
  return request({
    url: '/crm/quotation/submit/' + id,
    method: 'post'
  })
}

// 审批报价单
export function approveQuotation(data) {
  return request({
    url: '/crm/quotation/approve',
    method: 'post',
    data: data
  })
}

// 撤销报价单审批
export function cancelQuotationApproval(id, data) {
  return request({
    url: '/crm/quotation/cancel/' + id,
    method: 'post',
    data: data
  })
}

// 生成报价单编号
export function generateQuotationNo() {
  return request({
    url: '/crm/quotation/generateNo',
    method: 'get'
  })
}