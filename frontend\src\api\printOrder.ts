import { SimpleApiResponse } from '@/types/api/responseTypes';
import request from '@/utils/request';

// 定义订单创建的请求参数
export interface CreateOrderParams {
    // 报价信息
    quoteData: {
        quoteNo: string; // 询价单号
        items: OrderItem[]; // 订单项
        totalAmount: number; // 总金额
    };
    // 客户信息
    customerInfo: {
        customerId?: string; // 客户ID
        customerName: string; // 客户名称
        contactPerson?: string; // 联系人
        contactPhone?: string; // 联系电话
        deliveryAddress?: string; // 收货地址
    };
    // 文件信息
    files?: File[]; // 上传的模型文件
}

// 订单项定义
export interface OrderItem {
    modelName: string; // 模型名称
    modelInfo: {
        dimensions: string; // 尺寸
        volume: string; // 体积
        surfaceArea: string; // 表面积
    };
    material: string; // 材料
    materialId: string; // 材料ID
    quantity: number; // 数量
    unitPrice: number; // 单价
    totalPrice: number; // 总价
    processOptions?: string[]; // 后处理选项
    fileData?: File; // 模型文件
}

// 订单创建响应
export interface CreateOrderResponse {
    orderId: string; // 订单ID
    orderNo: string; // 订单编号
    status: string; // 订单状态
    createTime: string; // 创建时间
    fileUrls?: string[]; // OSS文件URLs
}

/**
 * 创建3D打印订单
 * @param params 订单参数
 * @returns 订单信息
 */
export function createPrintOrder(params: CreateOrderParams): Promise<SimpleApiResponse<CreateOrderResponse>> {
    // 构建FormData以支持文件上传
    const formData = new FormData();
    
    // 添加订单数据
    formData.append('quoteData', JSON.stringify(params.quoteData));
    formData.append('customerInfo', JSON.stringify(params.customerInfo));
    
    // 添加文件
    if (params.files && params.files.length > 0) {
        params.files.forEach((file, index) => {
            formData.append(`files`, file);
        });
    }
    
    return request({
        url: '/crm/order/create',
        method: 'post',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
}

/**
 * 获取订单详情
 * @param orderId 订单ID
 * @returns 订单详情
 */
export function getOrderDetail(orderId: string): Promise<SimpleApiResponse<CreateOrderResponse>> {
    return request({
        url: `/crm/order/${orderId}`,
        method: 'get',
    });
}

/**
 * 获取订单列表
 * @param params 查询参数
 * @returns 订单列表
 */
export function getOrderList(params: {
    pageNum?: number;
    pageSize?: number;
    status?: string;
    customerName?: string;
    startDate?: string;
    endDate?: string;
}) {
    return request({
        url: '/crm/order/list',
        method: 'get',
        params,
    });
}