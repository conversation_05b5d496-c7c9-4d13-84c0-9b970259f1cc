<template>
  <div class="data-summary-grid">
    <div class="summary-section">
      <div class="section-title">客户汇总</div>
      <div class="summary-items">
        <div class="summary-item" v-for="item in customerSummary" :key="item.key">
          <div class="item-label">{{ item.label }}</div>
          <div class="item-value">{{ item.value }}</div>
        </div>
      </div>
    </div>
    
    <div class="summary-section">
      <div class="section-title">商机汇总</div>
      <div class="summary-items">
        <div class="summary-item" v-for="item in opportunitySummary" :key="item.key">
          <div class="item-label">{{ item.label }}</div>
          <div class="item-value">{{ item.value }}</div>
        </div>
      </div>
    </div>
    
    <div class="summary-section">
      <div class="section-title">合同汇总</div>
      <div class="summary-items">
        <div class="summary-item" v-for="item in contractSummary" :key="item.key">
          <div class="item-label">{{ item.label }}</div>
          <div class="item-value" :class="item.highlight ? 'highlight' : ''">{{ item.value }}</div>
        </div>
      </div>
    </div>
    
    <div class="summary-section">
      <div class="section-title">跟进汇总</div>
      <div class="summary-items">
        <div class="summary-item" v-for="item in followupSummary" :key="item.key">
          <div class="item-label">{{ item.label }}</div>
          <div class="item-value">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

// 客户汇总数据
const customerSummary = ref([
  { key: 'newCustomers', label: '新增客户', value: '0 人' },
  { key: 'convertedCustomers', label: '转成交客户', value: '0 人' },
  { key: 'publicCustomers', label: '放入公海客户', value: '0 人' },
  { key: 'publicPoolCustomers', label: '公海池领取', value: '0 人' }
])

// 商机汇总数据
const opportunitySummary = ref([
  { key: 'newOpportunities', label: '新增商机', value: '0 个' },
  { key: 'wonOpportunities', label: '赢单商机', value: '0 个' },
  { key: 'lostOpportunities', label: '输单商机', value: '0 个' },
  { key: 'opportunityAmount', label: '商机总金额', value: '0 元' }
])

// 合同汇总数据
const contractSummary = ref([
  { key: 'signedContracts', label: '签约合同', value: '0 个' },
  { key: 'pendingContracts', label: '即将到期', value: '0 个', highlight: true },
  { key: 'expiredContracts', label: '已到期', value: '0 个', highlight: true },
  { key: 'contractAmount', label: '合同金额', value: '0 元' }
])

// 跟进汇总数据
const followupSummary = ref([
  { key: 'followupCustomers', label: '跟进客户数', value: '0人' },
  { key: 'newFollowups', label: '新增跟进客户', value: '0人' },
  { key: 'totalFollowups', label: '回款', value: '0元' },
  { key: 'expectedPayment', label: '预计回款', value: '0元' }
])
</script>

<style lang="scss" scoped>
.data-summary-grid {
  padding: 24px;
  
  .summary-section {
    margin-bottom: 32px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #1f2329;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #1677ff;
      display: inline-block;
    }
    
    .summary-items {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      
      @media (max-width: 480px) {
        grid-template-columns: 1fr;
      }
      
      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #fafbfc;
        border-radius: 6px;
        border: 1px solid #f0f0f0;
        transition: all 0.3s ease;
        
        &:hover {
          background: #f0f7ff;
          border-color: #d6e4ff;
          transform: translateY(-1px);
        }
        
        .item-label {
          font-size: 13px;
          color: #646a73;
          white-space: nowrap;
        }
        
        .item-value {
          font-size: 14px;
          font-weight: 600;
          color: #1f2329;
          
          &.highlight {
            color: #f5222d;
          }
        }
      }
    }
  }
}

// 为不同section设置不同的主题色
.summary-section {
  &:nth-child(1) .section-title {
    border-bottom-color: #1677ff;
  }
  
  &:nth-child(2) .section-title {
    border-bottom-color: #52c41a;
  }
  
  &:nth-child(3) .section-title {
    border-bottom-color: #faad14;
  }
  
  &:nth-child(4) .section-title {
    border-bottom-color: #722ed1;
  }
}
</style> 