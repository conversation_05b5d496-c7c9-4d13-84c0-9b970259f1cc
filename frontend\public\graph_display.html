<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图形展示页面</title>
    <!-- 引入 Mermaid.js 库 -->
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ startOnLoad: true });
    </script>
    <style>
        body {
            font-family: sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #fff;
            padding: 20px 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            width: 90%;
            max-width: 1400px;
        }
        h1, h2 {
            color: #333;
        }
        .mermaid {
            margin-top: 20px;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>任务处理时的数据流转图</h1>
        <div class="mermaid">
            sequenceDiagram
                participant User as 用户
                participant BusinessService as 业务服务
                participant ActivitiEngine as Activiti引擎
                box "Activiti数据库"
                    participant ACT_RU_TASK as 运行时任务表
                    participant ACT_HI_TASKINST as 历史任务表
                end
                participant Listener as 状态监听器
                box "业务数据库"
                    participant crm_reconciliation as 对账单主表
                    participant crm_reconciliation_process_history as 业务历史表
                end

                User->>BusinessService: 提交审批 (taskId, 'approved', '同意')

                BusinessService->>ActivitiEngine: taskService.complete(taskId, {result:'approved'})
                
                note over ActivitiEngine: 1. 引擎处理任务完成请求

                ActivitiEngine->>ACT_RU_TASK: DELETE task where id=taskId
                note right of ACT_RU_TASK: 任务从活跃状态移除

                ActivitiEngine->>ACT_HI_TASKINST: INSERT completed task (id=taskId, endTime=now())
                note right of ACT_HI_TASKINST: 任务进入历史记录

                note over ActivitiEngine: 2. 流程流转到下一环节

                ActivitiEngine->>Listener: 触发execute()方法 (在BPMN的流转线或节点上)
                
                Listener->>BusinessService: updateReconciliationStatus(reconciliationId, 'approved')
                
                BusinessService->>crm_reconciliation: UPDATE crm_reconciliation SET status='approved', current_task_id=NULL WHERE id=reconciliationId
                note left of crm_reconciliation: 更新业务状态

                BusinessService->>crm_reconciliation_process_history: INSERT new record ('审批通过', ...)
                note left of crm_reconciliation_process_history: 记录业务操作历史

                BusinessService-->>User: 返回成功响应
        </div>
        <p>这是一个使用 Mermaid.js 渲染的序列图，展示了任务处理时系统各部分的交互和数据变化。</p>
        
        <hr style="margin: 40px 0;">

        <div style="text-align: left; margin-top: 20px;">
            <h2>Task 节点可"挂载"的元素详解</h2>
            <p>Task 节点（在 BPMN 中通常是 <code>userTask</code>）远不止是一个流程图上的方框，它是一个可以配置丰富信息和行为的功能强大的"插座"。以下是可"挂载"在 Task 节点上的主要元素：</p>

            <h3>1. 办理人信息 (Who)</h3>
            <p>这决定了<strong>谁</strong>有权处理这个任务。</p>
            <ul>
                <li><strong><code>assignee</code> (办理人):</strong> 直接将任务分配给一个特定的用户 ID。例如: <code>activiti:assignee="${userId}"</code>。</li>
                <li><strong><code>candidateUsers</code> (候选用户):</strong> 将任务分配给一个用户列表，列表中的任何人都可以"拾取"(Claim)并办理。例如: <code>activiti:candidateUsers="userA,userB,${managerId}"</code>。</li>
                <li><strong><code>candidateGroups</code> (候选组):</strong> 将任务分配给一个或多个用户组（角色）。例如: <code>activiti:candidateGroups="financial_department"</code>。</li>
            </ul>

            <h3>2. 回调函数 (When & What - Task Listeners)</h3>
            <p>任务监听器 (<code>TaskListener</code>) 允许您在任务生命周期的特定事件发生时，自动执行一段 Java 代码或表达式。</p>
            <ul>
                <li><strong>可挂载的事件 (<code>event</code>):</strong>
                    <ul>
                        <li><strong><code>create</code>:</strong> 任务实例被创建并分配后触发。常用于发送待办通知。</li>
                        <li><strong><code>assignment</code>:</strong> 任务办理人被设置或改变时触发。</li>
                        <li><strong><code>complete</code>:</strong> 任务完成时触发，但在流程流转前。常用于更新业务表状态。</li>
                        <li><strong><code>delete</code>:</strong> 任务被删除时触发。</li>
                    </ul>
                </li>
                <li><strong>挂载方式:</strong>
                    <ul>
                        <li><strong>Java 类:</strong> <code>activiti:class="com.example.MyTaskListener"</code></li>
                        <li><strong>表达式:</strong> <code>activiti:expression="${myBean.handleComplete(task)}"</code></li>
                        <li><strong>委托表达式:</strong> <code>activiti:delegateExpression="${myTaskListenerBean}"</code></li>
                    </ul>
                </li>
            </ul>

            <h3>3. 数据与上下文 (Data)</h3>
            <p>任务执行时需要的数据，通过流程变量 (Variables) 来提供。</p>
            <ul>
                <li><strong>流程变量 (Process Variables):</strong> 贯穿整个流程实例的"全局"变量。例如对账单 ID <code>reconciliationId</code>、总金额 <code>totalAmount</code>。</li>
                <li><strong>任务局部变量 (Task-Local Variables):</strong> 生命周期仅限于当前任务实例的"局部"变量，任务完成后不传递。</li>
            </ul>
            
            <h3>4. 其他重要元素</h3>
            <ul>
                <li><strong><code>formKey</code> (表单标识):</strong> 用于将任务与前端的特定表单或组件关联起来。</li>
                <li><strong><code>dueDate</code> (到期日)</strong> 和 <strong><code>priority</code> (优先级):</strong> 为任务设置处理时限和优先级。</li>
            </ul>
        </div>
    </div>
</body>
</html> 