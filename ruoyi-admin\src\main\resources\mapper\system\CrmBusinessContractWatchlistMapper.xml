<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CrmBusinessContractWatchlistMapper">
    
    <resultMap type="CrmBusinessContractWatchlist" id="CrmBusinessContractWatchlistResult">
        <result property="id"    column="id"    />
        <result property="personId"    column="person_id"    />
        <result property="contractId"    column="contract_id"    />
        <result property="watchStatus"    column="watch_status"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectCrmBusinessContractWatchlistVo">
        select id, person_id, contract_id, watch_status, created_at, updated_at from crm_business_contract_watchlist
    </sql>

    <select id="selectCrmBusinessContractWatchlistList" parameterType="CrmBusinessContractWatchlist" resultMap="CrmBusinessContractWatchlistResult">
        <include refid="selectCrmBusinessContractWatchlistVo"/>
        <where>  
            <if test="personId != null "> and person_id = #{personId}</if>
            <if test="contractId != null "> and contract_id = #{contractId}</if>
            <if test="watchStatus != null  and watchStatus != ''"> and watch_status = #{watchStatus}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectCrmBusinessContractWatchlistById" parameterType="Long" resultMap="CrmBusinessContractWatchlistResult">
        <include refid="selectCrmBusinessContractWatchlistVo"/>
        where id = #{id}
    </select>

    <insert id="insertCrmBusinessContractWatchlist" parameterType="CrmBusinessContractWatchlist" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_contract_watchlist
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="personId != null">person_id,</if>
            <if test="contractId != null">contract_id,</if>
            <if test="watchStatus != null and watchStatus != ''">watch_status,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="personId != null">#{personId},</if>
            <if test="contractId != null">#{contractId},</if>
            <if test="watchStatus != null and watchStatus != ''">#{watchStatus},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateCrmBusinessContractWatchlist" parameterType="CrmBusinessContractWatchlist">
        update crm_business_contract_watchlist
        <trim prefix="SET" suffixOverrides=",">
            <if test="personId != null">person_id = #{personId},</if>
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="watchStatus != null and watchStatus != ''">watch_status = #{watchStatus},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmBusinessContractWatchlistById" parameterType="Long">
        delete from crm_business_contract_watchlist where id = #{id}
    </delete>

    <delete id="deleteCrmBusinessContractWatchlistByIds" parameterType="String">
        delete from crm_business_contract_watchlist where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>