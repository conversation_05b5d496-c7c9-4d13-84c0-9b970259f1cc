import * as THREE from 'three'
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader'

// 支持的文件类型
export const SUPPORTED_3D_FORMATS = ['stl', 'obj', 'fbx', 'gltf', 'glb']

interface ProcessedFile {
  url: string
  thumbnail: string
  isSupported: boolean
  message?: string
}

export class FileProcessor {
  static async process(file: File): Promise<ProcessedFile> {
    const extension = file.name.split('.').pop()?.toLowerCase()
    
    if (!extension || !SUPPORTED_3D_FORMATS.includes(extension)) {
      return {
        url: URL.createObjectURL(file),
        thumbnail: '',
        isSupported: false,
        message: `不支持的文件格式。请使用以下格式之一：${SUPPORTED_3D_FORMATS.join(', ')}`
      }
    }

    try {
      const thumbnail = await FileProcessor.generateThumbnail(file, extension)
      return {
        url: URL.createObjectURL(file),
        thumbnail,
        isSupported: true
      }
    } catch (error) {
      console.error('生成缩略图失败:', error)
      return {
        url: URL.createObjectURL(file),
        thumbnail: '',
        isSupported: false,
        message: '生成缩略图失败，请检查文件是否损坏'
      }
    }
  }

  static async extractModelInfo(file: File, extension: string): Promise<{
    dimensions?: string
    volume?: string
    surfaceArea?: string
  }> {
    if (extension === 'stl') {
      const geometry = await FileProcessor.loadSTL(file)
      const box = new THREE.Box3().setFromObject(new THREE.Mesh(geometry))
      const size = box.getSize(new THREE.Vector3())
      
      return {
        dimensions: `${size.x.toFixed(2)} x ${size.y.toFixed(2)} x ${size.z.toFixed(2)}`,
        volume: (size.x * size.y * size.z).toFixed(2),
        surfaceArea: FileProcessor.calculateSurfaceArea(geometry).toFixed(2)
      }
    }
    return {}
  }

  private static async loadSTL(file: File): Promise<THREE.BufferGeometry> {
    return new Promise((resolve, reject) => {
      const loader = new STLLoader()
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const geometry = loader.parse(e.target?.result as ArrayBuffer)
          resolve(geometry)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = reject
      reader.readAsArrayBuffer(file)
    })
  }

  private static calculateSurfaceArea(geometry: THREE.BufferGeometry): number {
    const position = geometry.attributes.position
    const index = geometry.index
    let area = 0

    if (index) {
      for (let i = 0; i < index.count; i += 3) {
        const a = index.getX(i)
        const b = index.getX(i + 1)
        const c = index.getX(i + 2)
        
        const vA = new THREE.Vector3().fromBufferAttribute(position, a)
        const vB = new THREE.Vector3().fromBufferAttribute(position, b)
        const vC = new THREE.Vector3().fromBufferAttribute(position, c)
        
        area += FileProcessor.triangleArea(vA, vB, vC)
      }
    } else {
      for (let i = 0; i < position.count; i += 3) {
        const vA = new THREE.Vector3().fromBufferAttribute(position, i)
        const vB = new THREE.Vector3().fromBufferAttribute(position, i + 1)
        const vC = new THREE.Vector3().fromBufferAttribute(position, i + 2)
        
        area += FileProcessor.triangleArea(vA, vB, vC)
      }
    }

    return area
  }

  private static triangleArea(a: THREE.Vector3, b: THREE.Vector3, c: THREE.Vector3): number {
    const ab = new THREE.Vector3().subVectors(b, a)
    const ac = new THREE.Vector3().subVectors(c, a)
    const cross = new THREE.Vector3().crossVectors(ab, ac)
    return cross.length() / 2
  }

  private static async generateThumbnail(file: File, extension: string): Promise<string> {
    // 创建Three.js场景
    const scene = new THREE.Scene()
    const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000)
    const renderer = new THREE.WebGLRenderer({ antialias: true })
    renderer.setSize(200, 200)
    
    // 添加灯光
    const light = new THREE.DirectionalLight(0xffffff, 1)
    light.position.set(0, 1, 1)
    scene.add(light)
    scene.add(new THREE.AmbientLight(0x404040))

    // 加载模型
    const loader = new STLLoader()
    const geometry = await new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = async (e) => {
        try {
          const result = loader.parse(e.target?.result as ArrayBuffer)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = reject
      reader.readAsArrayBuffer(file)
    })

    // 创建网格
    const material = new THREE.MeshPhongMaterial({ color: 0xcccccc })
    const mesh = new THREE.Mesh(geometry as THREE.BufferGeometry, material)
    
    // 自动调整相机位置以适应模型大小
    const box = new THREE.Box3().setFromObject(mesh)
    const center = box.getCenter(new THREE.Vector3())
    const size = box.getSize(new THREE.Vector3())
    const maxDim = Math.max(size.x, size.y, size.z)
    camera.position.set(center.x + maxDim, center.y + maxDim, center.z + maxDim)
    camera.lookAt(center)
    
    scene.add(mesh)
    
    // 渲染缩略图
    renderer.render(scene, camera)
    return renderer.domElement.toDataURL('image/png')
  }
}
