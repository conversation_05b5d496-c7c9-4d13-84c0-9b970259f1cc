package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmBusiness;

/**
 * 业务Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface CrmBusinessMapper 
{
    /**
     * 查询业务
     * 
     * @param id 业务主键
     * @return 业务
     */
    public CrmBusiness selectCrmBusinessById(Long id);

    /**
     * 查询业务列表
     * 
     * @param crmBusiness 业务
     * @return 业务集合
     */
    public List<CrmBusiness> selectCrmBusinessList(CrmBusiness crmBusiness);

    /**
     * 新增业务
     * 
     * @param crmBusiness 业务
     * @return 结果
     */
    public int insertCrmBusiness(CrmBusiness crmBusiness);

    /**
     * 修改业务
     * 
     * @param crmBusiness 业务
     * @return 结果
     */
    public int updateCrmBusiness(CrmBusiness crmBusiness);

    /**
     * 删除业务
     * 
     * @param id 业务主键
     * @return 结果
     */
    public int deleteCrmBusinessById(Long id);

    /**
     * 批量删除业务
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCrmBusinessByIds(Long[] ids);
}
