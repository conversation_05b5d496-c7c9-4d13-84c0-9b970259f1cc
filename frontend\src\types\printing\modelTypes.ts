// 打印模型相关类型定义
export interface PrintModel {
    id: string;
    name: string;
    type: string;
    imageUrl: string;
    price: number;
    techSpecs: any;
}

export interface Product {
    id?: string;
    name?: string;
    type?: string;
    price?: number;
    techSpecs?: any;
    imageUrls?: string[];
    advantages?: string;
    disadvantages?: string;
    applicationAreas?: string;
    updatedAt?: string;
    productLink?: string;
    materialProperties?: string; // 新增材料属性字段
}

// 打印模型查询参数
export interface PrintModelListParams {
    pageNum?: number;
    pageSize?: number;
    type?: string;
    [key: string]: any;
}