# Activiti 引擎：为审批系统注入强大动力

## 一、Activiti 是什么？

Activiti 是一个开源的、轻量级的、基于 Java 的工作流和业务流程管理（BPM）平台。它遵循 BPMN 2.0（Business Process Model and Notation）标准，这是一个业界广泛接受的图形化标准，用于精确地描述业务流程。

将 Activiti 集成到我们的审批系统中，意味着我们不再需要手动去实现流程的流转、状态管理、任务分配等逻辑，而是可以利用一个成熟、稳定、功能强大的"流程引擎"来驱动我们的业务。这能极大地增强我们审批系统的灵活性和可扩展性，使其能够应对更复杂的企业流程需求。

---

## 二、Activiti 核心架构

Activiti 的核心是其流程引擎（ProcessEngine）。该引擎可以作为一个独立的服务器运行，也可以嵌入到任何 Java 应用中（比如我们的 CRM 系统）。引擎提供了多个服务接口，用于操作和执行业务流程。

```mermaid
graph TD
    subgraph "Your Application (CRM)"
        A["审批 Controller/Service"]
    end

    subgraph Activiti ProcessEngine
        B[ProcessEngine]
        direction LR
        subgraph Services API
            C[RepositoryService]
            D[RuntimeService]
            E[TaskService]
            F[HistoryService]
            G[ManagementService]
        end
    end

    subgraph Database
        H[(Activiti Tables)]
    end

    A --> B
    B --> C & D & E & F & G
    C & D & E & F & G --> H

    %% Descriptions
    classDef service fill:#2874a6,stroke:#333,stroke-width:2px;
    class C,D,E,F,G service;
```

**核心服务接口说明:**

-   **`RepositoryService`**: **仓库服务**。管理流程的"定义"，即我们的流程模板。负责部署、查询、删除流程定义（BPMN 文件）。这类似于我们现有设计中的 `crm_approval_definition` 管理，但功能更强大。
-   **`RuntimeService`**: **运行时服务**。用于启动一个新的流程"实例"，查询和管理正在运行中的流程实例。这对应我们设计中的 `crm_approval_instance` 管理。
-   **`TaskService`**: **任务服务**。处理流程中的"任务"，特别是人工任务（UserTask）。例如，查询用户的待办任务、完成任务（批准/驳回）、签收任务等。这对应我们设计中的 `crm_approval_task` 管理。
-   **`HistoryService`**: **历史服务**。提供对所有历史数据的查询，包括已完成的流程实例、任务、变量等。这对于流程审计和性能分析至关重要。
-   **`ManagementService`**: **管理服务**。提供对引擎的管理和维护功能，通常在系统管理和监控场景下使用。

---

## 三、BPMN 2.0：流程的通用语言

Activiti 使用 BPMN 2.0 标准来定义流程。这意味着我们可以使用可视化的设计器来"画"出我们的审批流，然后导出一个标准的 XML 文件，交由 Activiti 引擎执行。这比我们当前设计中用 JSON `process_config` 来定义流程要直观和标准得多。

### BPMN 核心元素示例

```mermaid
graph TD
    A("开始事件") --> B{"互斥网关<br/>金额 > 10000?"};
    B -- 是 --> C["部门经理审批"];
    B -- 否 --> D["直接主管审批"];
    C --> E{"并行网关"};
    D --> E;
    E --> F["财务审批"];
    E --> G["抄送CEO"];
    F --> H(("结束事件"));
    G --> H;

    classDef event fill:#27ae60,stroke:#333,stroke-width:2px;
    classDef task fill:#3498db,stroke:#333,stroke-width:2px;
    classDef gateway fill:#5d6d7e,stroke:#333,stroke-width:2px;
    class A,H event;
    class C,D,F,G task;
    class B,E gateway;
```

**关键元素解释:**

-   **事件 (Events)**: 表示流程中发生的事情。如 `开始事件`、`结束事件`、`定时事件` (如超时未处理自动同意)、`消息事件` (如接收到外部系统消息后触发流程)。
-   **任务 (Tasks)**: 表示需要完成的工作。
    -   `用户任务 (UserTask)`: 需要人工处理的节点，如"部门经理审批"。
    -   `服务任务 (ServiceTask)`: 系统自动执行的任务，如"调用财务接口扣款"。
    -   `脚本任务 (ScriptTask)`: 执行一段脚本。
-   **网关 (Gateways)**: 控制流程的走向（分支与合并）。
    -   `互斥网关 (Exclusive Gateway)`: 只有一条路径会被选择，如"金额 > 10000?"。
    -   `并行网关 (Parallel Gateway)`: 所有路径都会同时执行，如同时需要"财务审批"和"抄送CEO"。
    -   `包容网关 (Inclusive Gateway)`: 可以选择一条或多条路径执行。
-   **流 (Flows)**: `顺序流` (带箭头的线) 连接以上元素，定义执行顺序。

---

## 四、如何用 Activiti 增强我们的审批系统？

基于 Activiti 的强大功能，我们可以对现有设计进行如下升级：

1.  **用 BPMN 替换 `process_config`**
    -   **现状**: 使用自定义的 JSON (`process_config`) 来描述流程节点。
    -   **升级**: 引入一个可视化的 BPMN 设计器（有很多开源或商业的选择），让管理员通过拖拽方式设计流程。保存的不再是 JSON，而是一个标准的 BPMN 2.0 XML 文件。`RepositoryService` 将负责部署这个 XML。

2.  **实现更复杂的流程逻辑**
    -   **现状**: 只能支持简单的线性审批流。
    -   **升级**: 利用 Activiti 的**网关**，轻松实现条件判断、并行审批（会签）、多选一（或签）等复杂逻辑。例如：
        -   采购申请，金额小于 1000 元，主管审批；大于 1000元，经理审批。
        -   入职流程，需要人事部、行政部同时处理准备工作。

3.  **动态与灵活的任务分配**
    -   **现状**: `approver` 配置支持指定角色、用户等，但扩展性有限。
    -   **升级**: Activiti 支持更丰富的任务分配策略：
        -   **用户/用户组**: 直接分配给某个用户或用户组（角色）。
        -   **流程变量**: 根据流程中某个变量的值来动态决定审批人。例如，表单中选择了"华东区"，则自动分配给"华东区销售总监"角色。
        -   **Java 代理**: 编写一段 Java 代码，根据复杂的业务逻辑（如查询数据库、调用外部接口）来计算出审批人。

4.  **自动化与集成**
    -   **现状**: 流程与业务逻辑耦合在 `ApprovalServiceImpl` 中。
    -   **升级**:
        -   使用 **`服务任务 (ServiceTask)`**。当流程走到某个节点时，可以自动调用一段 Java 代码或一个外部接口。例如，审批通过后，自动调用合同服务的接口，将合同状态更新为"已生效"。
        -   使用 **`监听器 (Listeners)`**。可以在任务创建、任务完成、流程开始、流程结束等各个生命周期点挂载自定义逻辑，实现日志记录、发送通知、更新业务数据等，实现真正的解耦。

5.  **增强的流程控制与监控**
    -   **现状**: 只能简单的撤销。
    -   **升级**: Activiti 允许更细粒度的控制，如：
        -   **驳回至任意节点**: 可以将流程驳回到之前的任一环节。
        -   **任务转办/委派**: 审批人可以将任务临时交给其他人处理。
        -   **超时处理**: 使用 **`定时事件`**，如果一个任务在规定时间内未被处理，可以自动催办、自动跳过或转交给上级。
        -   利用 `HistoryService`，可以轻松构建出详细的审批历史、流程图高亮、耗时分析等高级功能。

---

## 五、结论

我们当前的审批系统设计是一个很好的开端，它定义了核心的业务实体和API。但是，它的"流程引擎"部分是需要我们自己编码实现的，功能相对基础。

**引入 Activiti，相当于给我们换上了一个工业级的、标准化的"发动机"**。我们不再需要关心流程如何流转、状态如何管理、任务如何分配这些底层细节，而是可以专注于：

1.  **用BPMN设计器"画"出业务流程。**
2.  **在关键节点通过监听器或服务任务"挂载"我们的业务逻辑。**

这将大大提升开发效率、系统的稳定性和未来的扩展性，使我们能够轻松应对从简单到复杂的各种企业审批场景。建议下一步可以调研如何在项目中集成 Activiti 引擎，并构建一个简单的 PoC（概念验证）流程。
