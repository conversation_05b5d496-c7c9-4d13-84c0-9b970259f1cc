<template>
  <div class="team-business-objects">
    <!-- 团队信息展示 -->
    <div class="team-info-section" v-if="teamInfo">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>团队信息</span>
          </div>
        </template>
        <div class="team-details">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="团队名称">
              <el-tag type="primary" size="large">{{ teamInfo.teamName }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="团队负责人">
              <el-tag type="success">{{ teamInfo.leaderName }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="成员数量">
              <el-tag type="info">{{ teamMembers.length }}人</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="团队描述" :span="2">
              {{ teamInfo.description || '暂无描述' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDate(teamInfo.createTime) }}
            </el-descriptions-item>
          </el-descriptions>
          
          <!-- 团队成员列表 -->
          <div class="team-members" v-if="teamMembers.length > 0">
            <h4>团队成员</h4>
            <div class="members-list">
              <el-tag 
                v-for="member in teamMembers" 
                :key="member.userId"
                :type="member.roleType === 'owner' ? 'danger' : member.roleType === 'admin' ? 'warning' : 'primary'"
                size="small"
                class="member-tag"
              >
                {{ member.nickName || member.userName }}
                <span class="role-badge">({{ getRoleLabel(member.roleType) }})</span>
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 业务类型选择 -->
    <div class="business-type-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>业务对象绑定管理</span>
          </div>
        </template>
        <div class="type-selector">
          <el-form :model="queryForm" :inline="true">
            <el-form-item label="业务类型">
              <el-select 
                v-model="queryForm.bizType" 
                placeholder="请选择业务类型" 
                @change="handleBizTypeChange"
                style="width: 200px;"
              >
                <el-option label="联系人" value="CONTACT" />
                <el-option label="客户" value="CUSTOMER" />
                <el-option label="线索" value="LEAD" />
                <el-option label="商机" value="OPPORTUNITY" />
                <el-option label="合同" value="CONTRACT" />
              </el-select>
            </el-form-item>
            <el-form-item label="搜索">
              <el-input
                v-model="queryForm.keyword"
                placeholder="请输入名称搜索"
                clearable
                @keyup.enter="loadBusinessObjects"
                style="width: 200px;"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadBusinessObjects">搜索</el-button>
              <el-button @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 业务对象列表 -->
    <div class="objects-section" v-if="queryForm.bizType">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>{{ getBizTypeLabel(queryForm.bizType) }}列表 ({{ total }})</span>
            <div class="header-actions">
              <el-button 
                type="primary" 
                size="small" 
                @click="handleBatchBind"
                :disabled="selectedObjects.length === 0"
                v-if="teamId"
              >
                <el-icon><Link /></el-icon>
                批量绑定 ({{ selectedObjects.length }})
              </el-button>
              <el-button type="primary" size="small" @click="refreshData">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <!-- 表格视图 -->
        <div class="table-view">
          <el-table 
            :data="businessObjects" 
            v-loading="loading" 
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" v-if="teamId" />
            <el-table-column prop="bizName" label="名称" min-width="200">
              <template #default="{ row }">
                <el-button link @click="viewObjectDetail(row)">
                  <strong>{{ row.bizName || `${getBizTypeLabel(queryForm.bizType)} ${row.bizId}` }}</strong>
                </el-button>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180" v-if="queryForm.bizType === 'CONTACT'">
              <template #default="{ row }">
                {{ formatDate(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="联系电话" width="150" v-if="queryForm.bizType === 'CONTACT'">
              <template #default="{ row }">
                {{ row.phone || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="company" label="公司" width="200" v-if="queryForm.bizType === 'CONTACT'">
              <template #default="{ row }">
                {{ row.company || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="customerName" label="客户名称" width="200" v-if="queryForm.bizType === 'CUSTOMER'">
              <template #default="{ row }">
                {{ row.customerName || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="industry" label="行业" width="150" v-if="queryForm.bizType === 'CUSTOMER'">
              <template #default="{ row }">
                {{ row.industry || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="source" label="来源" width="120" v-if="queryForm.bizType === 'LEAD'">
              <template #default="{ row }">
                {{ row.source || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100" v-if="queryForm.bizType === 'LEAD'">
              <template #default="{ row }">
                <el-tag size="small" :type="row.status === 'active' ? 'success' : 'info'">
                  {{ row.status || '-' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="联系电话" width="150" v-if="queryForm.bizType === 'LEAD'">
              <template #default="{ row }">
                {{ row.phone || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="stage" label="阶段" width="120" v-if="queryForm.bizType === 'OPPORTUNITY'">
              <template #default="{ row }">
                <el-tag size="small" :type="getStageTagType(row.stage)">
                  {{ row.stage || '-' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="预期价值" width="120" v-if="queryForm.bizType === 'OPPORTUNITY'">
              <template #default="{ row }">
                {{ row.value ? `¥${row.value}` : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="customerName" label="客户名称" width="180" v-if="queryForm.bizType === 'OPPORTUNITY'">
              <template #default="{ row }">
                {{ row.customerName || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="contractStatus" label="合同状态" width="120" v-if="queryForm.bizType === 'CONTRACT'">
              <template #default="{ row }">
                <el-tag size="small" :type="getContractStatusTagType(row.contractStatus)">
                  {{ row.contractStatus || '-' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="contractAmount" label="合同金额" width="120" v-if="queryForm.bizType === 'CONTRACT'">
              <template #default="{ row }">
                {{ row.contractAmount ? `¥${row.contractAmount}` : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="customerName" label="客户名称" width="180" v-if="queryForm.bizType === 'CONTRACT'">
              <template #default="{ row }">
                {{ row.customerName || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="teamInfo" label="关联团队" width="200">
              <template #default="{ row }">
                <div v-if="row.teamInfo">
                  <el-tag type="success" size="small">
                    {{ row.teamInfo.teamName }}
                  </el-tag>
                  <div style="font-size: 12px; color: #999; margin-top: 2px;">
                    负责人：{{ row.teamInfo.leaderName }}
                  </div>
                </div>
                <el-tag v-else type="info" size="small">未分配</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180" fixed="right">
              <template #default="{ row }">
                <el-button 
                  v-if="!teamId"
                  link 
                  size="small" 
                  type="primary"
                  @click="openTeamAssignDialog(row)"
                >
                  {{ row.teamInfo ? '更换团队' : '分配团队' }}
                </el-button>
                <el-button
                  v-if="teamId && !row.teamInfo"
                  link
                  size="small"
                  type="primary"
                  @click="handleSingleBind(row)"
                >
                  绑定到团队
                </el-button>
                <el-button
                  v-if="teamId && row.teamInfo && row.teamInfo.teamId === teamId"
                  link
                  size="small"
                  style="color: #f56c6c;"
                  @click="handleSingleUnbind(row)"
                >
                  取消绑定
                </el-button>
                <el-button
                  v-if="!teamId && row.teamInfo"
                  link
                  size="small"
                  style="color: #f56c6c;"
                  @click="unassignTeam(row)"
                >
                  取消分配
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 空状态 -->
        <div v-if="businessObjects.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无业务对象">
            <el-button type="primary" @click="loadBusinessObjects">重新加载</el-button>
          </el-empty>
        </div>
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination-section" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 团队分配对话框 -->
    <el-dialog
      v-model="teamAssignDialogVisible"
      title="分配团队"
      width="800px"
      append-to-body
      destroy-on-close
    >
      <div class="team-assign-dialog-content">
        <div class="business-info">
          <h4>业务对象信息</h4>
          <div class="info-row">
            <span class="label">类型：</span>
            <el-tag :type="getBizTypeTagType(queryForm.bizType)" size="small">
              {{ getBizTypeLabel(queryForm.bizType) }}
            </el-tag>
          </div>
          <div class="info-row">
            <span class="label">名称：</span>
            <span>{{ currentBizObject?.bizName || `${getBizTypeLabel(queryForm.bizType)} ${currentBizObject?.bizId}` }}</span>
          </div>
          <div class="info-row" v-if="currentBizObject?.teamInfo">
            <span class="label">当前团队：</span>
            <el-tag type="success" size="small">{{ currentBizObject.teamInfo.teamName }}</el-tag>
          </div>
        </div>

        <el-divider />

        <div class="team-selector">
          <h4>选择团队</h4>
          <div class="team-search">
            <el-input
              v-model="teamSearchKeyword"
              placeholder="搜索团队名称"
              clearable
              @input="handleTeamSearch"
              style="width: 300px;"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          
          <div class="team-list">
            <el-table
              :data="filteredTeams"
              v-loading="teamLoading"
              @selection-change="handleTeamSelectionChange"
              height="300px"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="teamName" label="团队名称" width="200" />
              <el-table-column prop="leaderName" label="负责人" width="120" />
              <el-table-column prop="description" label="描述" show-overflow-tooltip />
              <el-table-column prop="memberCount" label="成员数量" width="100">
                <template #default="{ row }">
                  <el-tag size="small">{{ row.memberCount || 0 }}人</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 团队信息预览 -->
        <div v-if="selectedTeam" class="team-preview">
          <el-divider />
          <h4>团队信息预览</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="团队名称">{{ selectedTeam.teamName }}</el-descriptions-item>
            <el-descriptions-item label="负责人">{{ selectedTeam.leaderName }}</el-descriptions-item>
            <el-descriptions-item label="成员数量">{{ selectedTeam.memberCount || 0 }}人</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDate(selectedTeam.createTime) }}</el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">{{ selectedTeam.description || '暂无描述' }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <template #footer>
        <el-button @click="teamAssignDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleTeamAssign" :disabled="!selectedTeam">
          确定分配
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { listTeam, getTeam } from '@/api/crm/team'
import { listContacts } from '@/views/ContactManagement/api/index'
import { listCustomers } from '@/api/crm/customer/index'
import { listLeads } from '@/api/crm/leads/index'
import { listOpportunities } from '@/views/BusinessOpportunityManagement/api/index'
import { listContracts } from '@/views/ContractManagement/api/index'
import { assignTeamToBiz, unassignTeamFromBiz, getTeamByBiz, batchAssignTeamToBiz, getTeamMembersByTeamId } from '@/api/team-relation'
import { Search, Refresh, Link } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, reactive, ref, watch } from 'vue'

// 定义接口
interface BusinessObject {
  bizId: number
  bizName: string
  bizType: string
  createTime?: string
  phone?: string
  company?: string
  customerName?: string
  industry?: string
  teamInfo?: {
    teamId: number
    teamName: string
    leaderName: string
  }
  [key: string]: any
}

interface Team {
  teamId: number
  teamName: string
  leaderName: string
  description?: string
  memberCount?: number
  createTime?: string
  [key: string]: any
}

interface TeamMember {
  userId: number
  userName: string
  nickName: string
  roleType: 'owner' | 'admin' | 'member'
  joinTime?: string
}

interface Props {
  teamId?: number
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const teamLoading = ref(false)
const businessObjects = ref<BusinessObject[]>([])
const teams = ref<Team[]>([])

// 团队相关数据
const teamInfo = ref<Team | null>(null)
const teamMembers = ref<TeamMember[]>([])

// 查询表单
const queryForm = reactive({
  bizType: '',
  keyword: '',
  filterType: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 选择相关
const selectedObjects = ref<BusinessObject[]>([])

// 团队分配对话框相关
const teamAssignDialogVisible = ref(false)
const currentBizObject = ref<BusinessObject | null>(null)
const selectedTeam = ref<Team | null>(null)
const teamSearchKeyword = ref('')

// 计算属性
const filteredTeams = computed(() => {
  if (!teamSearchKeyword.value) return teams.value
  const keyword = teamSearchKeyword.value.toLowerCase()
  return teams.value.filter(team => 
    team.teamName.toLowerCase().includes(keyword) ||
    (team.leaderName && team.leaderName.toLowerCase().includes(keyword))
  )
})

// 方法
const loadBusinessObjects = async () => {
  if (!queryForm.bizType) return
  
  loading.value = true
  try {
    let response

    // 根据业务类型调用不同的API
    switch (queryForm.bizType) {
      case 'CONTACT':
        // 联系人API使用特定的参数结构
        response = await listContacts({
          pageNum: currentPage.value,
          pageSize: pageSize.value,
          searchKeyword: queryForm.keyword,
          filterType: queryForm.filterType || 'all'
        })
        break
      case 'CUSTOMER':
        // 客户API使用标准的列表参数结构
        response = await listCustomers({
          pageNum: currentPage.value,
          pageSize: pageSize.value,
          customerName: queryForm.keyword || ''
        })
        break
      case 'LEAD':
        // 线索API使用特定的参数结构
        response = await listLeads({
          pageNum: currentPage.value,
          pageSize: pageSize.value,
          searchKeyword: queryForm.keyword,
          filterType: queryForm.filterType
        })
        break
      case 'OPPORTUNITY':
        // 商机API使用特定的参数结构
        response = await listOpportunities({
          pageNum: currentPage.value,
          pageSize: pageSize.value,
          searchKeyword: queryForm.keyword,
          filterType: queryForm.filterType || 'all'
        })
        break
      case 'CONTRACT':
        // 合同API使用特定的参数结构
        response = await listContracts({
          pageNum: currentPage.value,
          pageSize: pageSize.value,
          searchKeyword: queryForm.keyword,
          filterType: queryForm.filterType || 'all'
        })
        break
      default:
        ElMessage.warning('暂不支持该业务类型')
        return
    }

    // 处理响应数据
    let rawData = []
    // 所有业务类型都使用标准的rows/total结构
    rawData = response.rows || []
    total.value = response.total || 0

    // 转换为统一格式并获取团队信息
    const processedData = await Promise.all(
      rawData.map(async (item: any) => {
        const bizObject: BusinessObject = {
          bizId: item.id,
          bizType: queryForm.bizType,
          bizName: getBizName(item, queryForm.bizType),
          createTime: item.createTime || item.createdTime,
          phone: item.phone,
          company: item.company,
          customerName: item.customerName || item.name,
          industry: item.industry,
          ...item
        }

        // 获取团队信息
        try {
          const teamResponse = await getTeamByBiz(bizObject.bizId, bizObject.bizType)
          if (teamResponse.data) {
            bizObject.teamInfo = {
              teamId: teamResponse.data.teamId,
              teamName: teamResponse.data.teamName,
              leaderName: teamResponse.data.leaderName
            }
          }
        } catch (error) {
          // 如果没有团队信息，不处理错误
        }

        return bizObject
      })
    )

    businessObjects.value = processedData
  } catch (error) {
    console.error('加载业务对象失败:', error)
    ElMessage.error('加载业务对象失败')
  } finally {
    loading.value = false
  }
}

const loadTeams = async () => {
  teamLoading.value = true
  try {
    const response = await listTeam()
    // 映射团队数据字段
    teams.value = (response.rows || []).map((team: any) => ({
      teamId: team.id || team.teamId,
      teamName: team.teamName,
      leaderName: team.leaderName,
      description: team.description,
      createTime: team.createTime,
      memberCount: team.memberCount || 0,
      ...team
    }))
  } catch (error) {
    console.error('加载团队列表失败:', error)
    ElMessage.error('加载团队列表失败')
  } finally {
    teamLoading.value = false
  }
}

const getBizName = (item: any, bizType: string) => {
  switch (bizType) {
    case 'CONTACT':
      return item.contactName || item.name || `联系人${item.id}`
    case 'CUSTOMER':
      return item.customerName || item.name || `客户${item.id}`
    case 'LEAD':
      return item.name || item.customerName || `线索${item.id}`
    case 'OPPORTUNITY':
      return item.opportunityName || item.name || item.title || `商机${item.id}`
    case 'CONTRACT':
      return item.contractName || item.name || item.title || `合同${item.id}`
    default:
      return `${getBizTypeLabel(bizType)}${item.id}`
  }
}

const handleBizTypeChange = () => {
  // 重置分页
  currentPage.value = 1
  businessObjects.value = []
  total.value = 0
  
  // 加载数据
  if (queryForm.bizType) {
    loadBusinessObjects()
  }
}

const resetQuery = () => {
  queryForm.bizType = ''
  queryForm.keyword = ''
  queryForm.filterType = ''
  currentPage.value = 1
  businessObjects.value = []
  total.value = 0
}

const refreshData = () => {
  loadBusinessObjects()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadBusinessObjects()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadBusinessObjects()
}

const viewObjectDetail = (obj: BusinessObject) => {
  console.log('查看业务对象详情:', obj)
  ElMessage.info('跳转到详情页面功能开发中...')
}

const openTeamAssignDialog = (obj: BusinessObject) => {
  currentBizObject.value = obj
  selectedTeam.value = null
  teamSearchKeyword.value = ''
  teamAssignDialogVisible.value = true
  loadTeams()
}

const handleTeamSearch = () => {
  // 搜索逻辑在计算属性中处理
}

const handleTeamSelectionChange = (selection: Team[]) => {
  selectedTeam.value = selection.length > 0 ? selection[0] : null
}

const handleTeamAssign = async () => {
  if (!currentBizObject.value || !selectedTeam.value) return

  try {
    await assignTeamToBiz(
      selectedTeam.value.teamId || selectedTeam.value.id,
      currentBizObject.value.bizId,
      currentBizObject.value.bizType
    )
    
    ElMessage.success('分配团队成功')
    teamAssignDialogVisible.value = false
    
    // 刷新列表
    await loadBusinessObjects()
  } catch (error) {
    console.error('分配团队失败:', error)
    ElMessage.error('分配团队失败')
  }
}

const unassignTeam = async (obj: BusinessObject) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消 "${obj.bizName}" 的团队分配吗？`,
      '确认取消分配',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await unassignTeamFromBiz(obj.bizId, obj.bizType)
    ElMessage.success('取消分配成功')
    
    // 刷新列表
    await loadBusinessObjects()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消分配失败:', error)
      ElMessage.error('取消分配失败')
    }
  }
}

// 辅助方法
const getBizTypeTagType = (bizType: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    CONTACT: 'primary',
    LEAD: 'warning',
    CUSTOMER: 'success',
    OPPORTUNITY: 'info',
    CONTRACT: 'danger',
    VISIT_PLAN: 'info'
  }
  return typeMap[bizType] || 'info'
}

const getBizTypeLabel = (bizType: string) => {
  const labelMap: Record<string, string> = {
    CONTACT: '联系人',
    LEAD: '线索',
    CUSTOMER: '客户',
    OPPORTUNITY: '商机',
    CONTRACT: '合同',
    VISIT_PLAN: '拜访计划'
  }
  return labelMap[bizType] || '未知'
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 新增方法
const loadTeamInfo = async () => {
  if (!props.teamId) return
  
  try {
    const [teamResponse, membersResponse] = await Promise.all([
      getTeam(props.teamId),
      getTeamMembersByTeamId(props.teamId)
    ])
    
    teamInfo.value = teamResponse.data
    teamMembers.value = membersResponse.data?.rows || membersResponse.data || []
  } catch (error) {
    console.error('加载团队信息失败:', error)
  }
}

const handleSelectionChange = (selection: BusinessObject[]) => {
  selectedObjects.value = selection
}

const handleSingleBind = async (obj: BusinessObject) => {
  if (!props.teamId) return
  
  try {
    await assignTeamToBiz(props.teamId, obj.bizId, obj.bizType)
    ElMessage.success('绑定成功')
    await loadBusinessObjects()
  } catch (error) {
    console.error('绑定失败:', error)
    ElMessage.error('绑定失败')
  }
}

const handleSingleUnbind = async (obj: BusinessObject) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消 "${obj.bizName}" 的团队绑定吗？`,
      '确认取消绑定',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await unassignTeamFromBiz(obj.bizId, obj.bizType)
    ElMessage.success('取消绑定成功')
    await loadBusinessObjects()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消绑定失败:', error)
      ElMessage.error('取消绑定失败')
    }
  }
}

const handleBatchBind = async () => {
  if (!props.teamId || selectedObjects.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要将选中的 ${selectedObjects.value.length} 个业务对象绑定到当前团队吗？`,
      '确认批量绑定',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const bizIds = selectedObjects.value.map(obj => obj.bizId)
    await batchAssignTeamToBiz(props.teamId, bizIds, queryForm.bizType)
    
    ElMessage.success('批量绑定成功')
    selectedObjects.value = []
    await loadBusinessObjects()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量绑定失败:', error)
      ElMessage.error('批量绑定失败')
    }
  }
}

const getRoleLabel = (roleType: string) => {
  const roleMap: Record<string, string> = {
    owner: '负责人',
    admin: '管理员',
    member: '成员'
  }
  return roleMap[roleType] || '成员'
}

const getStageTagType = (stage: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const stageMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'initial': 'info',
    'qualified': 'primary',
    'proposal': 'warning',
    'negotiation': 'warning',
    'closed-won': 'success',
    'closed-lost': 'danger'
  }
  return stageMap[stage] || 'info'
}

const getContractStatusTagType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'draft': 'info',
    'pending': 'warning',
    'active': 'success',
    'completed': 'primary',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

// 生命周期
onMounted(() => {
  if (props.teamId) {
    loadTeamInfo()
  }
})

// 监听团队ID变化
watch(() => props.teamId, (newTeamId) => {
  if (newTeamId) {
    loadTeamInfo()
  }
})
</script>

<style scoped>
.team-business-objects {
  padding: 20px;
}

/* 团队信息展示区域 */
.team-info-section {
  margin-bottom: 20px;
}

.team-details {
  margin-top: 16px;
}

.team-members {
  margin-top: 20px;
}

.team-members h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.members-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.member-tag {
  margin-right: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.role-badge {
  font-size: 12px;
  color: #909399;
  font-weight: normal;
}

/* 业务类型选择区域 */
.business-type-section {
  margin-bottom: 20px;
}

.type-selector {
  padding: 16px;
}

/* 对象列表 */
.objects-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 表格视图 */
.table-view {
  margin-top: 16px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 0;
}

/* 分页 */
.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 团队分配对话框 */
.team-assign-dialog-content {
  max-height: 600px;
  overflow-y: auto;
}

.business-info {
  margin-bottom: 20px;
}

.business-info h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-row .label {
  min-width: 80px;
  font-weight: 500;
  color: #606266;
}

.team-selector h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.team-search {
  margin-bottom: 16px;
}

.team-list {
  margin-bottom: 16px;
}

.team-preview {
  margin-top: 16px;
}

.team-preview h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa;
  font-weight: 600;
}

.el-table td {
  padding: 12px 0;
}

/* 按钮样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 标签样式 */
.el-tag {
  margin-right: 8px;
}

/* 卡片样式 */
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

/* 对话框样式 */
.el-dialog {
  border-radius: 8px;
}

.el-dialog__header {
  padding: 20px 20px 0;
}

.el-dialog__body {
  padding: 20px;
}

.el-dialog__footer {
  padding: 0 20px 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .team-business-objects {
    padding: 12px;
  }

  .type-selector .el-form {
    flex-direction: column;
  }

  .type-selector .el-form-item {
    margin-bottom: 16px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .business-info,
  .team-selector {
    margin-bottom: 16px;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-row .label {
    min-width: auto;
    margin-bottom: 4px;
  }

  .team-search {
    width: 100%;
  }

  .team-search .el-input {
    width: 100% !important;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 表格行悬停效果 */
.el-table tbody tr:hover {
  background-color: #f5f7fa;
}

/* 加载状态 */
.el-loading-mask {
  border-radius: 8px;
}

/* 分页样式 */
.el-pagination {
  justify-content: center;
}

/* 描述列表样式 */
.el-descriptions {
  margin-top: 16px;
}

.el-descriptions__label {
  font-weight: 600;
  color: #606266;
}

.el-descriptions__content {
  color: #303133;
}
</style>
