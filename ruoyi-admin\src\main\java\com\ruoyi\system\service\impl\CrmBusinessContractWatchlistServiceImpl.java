package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CrmBusinessContractWatchlistMapper;
import com.ruoyi.common.domain.entity.CrmBusinessContractWatchlist;
import com.ruoyi.system.service.ICrmBusinessContractWatchlistService;

/**
 * 合同关注Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class CrmBusinessContractWatchlistServiceImpl implements ICrmBusinessContractWatchlistService 
{
    @Autowired
    private CrmBusinessContractWatchlistMapper crmBusinessContractWatchlistMapper;

    /**
     * 查询合同关注
     * 
     * @param id 合同关注主键
     * @return 合同关注
     */
    @Override
    public CrmBusinessContractWatchlist selectCrmBusinessContractWatchlistById(Long id)
    {
        return crmBusinessContractWatchlistMapper.selectCrmBusinessContractWatchlistById(id);
    }

    /**
     * 查询合同关注列表
     * 
     * @param crmBusinessContractWatchlist 合同关注
     * @return 合同关注
     */
    @Override
    public List<CrmBusinessContractWatchlist> selectCrmBusinessContractWatchlistList(CrmBusinessContractWatchlist crmBusinessContractWatchlist)
    {
        return crmBusinessContractWatchlistMapper.selectCrmBusinessContractWatchlistList(crmBusinessContractWatchlist);
    }

    /**
     * 新增合同关注
     * 
     * @param crmBusinessContractWatchlist 合同关注
     * @return 结果
     */
    @Override
    public int insertCrmBusinessContractWatchlist(CrmBusinessContractWatchlist crmBusinessContractWatchlist)
    {
        return crmBusinessContractWatchlistMapper.insertCrmBusinessContractWatchlist(crmBusinessContractWatchlist);
    }

    /**
     * 修改合同关注
     * 
     * @param crmBusinessContractWatchlist 合同关注
     * @return 结果
     */
    @Override
    public int updateCrmBusinessContractWatchlist(CrmBusinessContractWatchlist crmBusinessContractWatchlist)
    {
        return crmBusinessContractWatchlistMapper.updateCrmBusinessContractWatchlist(crmBusinessContractWatchlist);
    }

    /**
     * 批量删除合同关注
     * 
     * @param ids 需要删除的合同关注主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessContractWatchlistByIds(Long[] ids)
    {
        return crmBusinessContractWatchlistMapper.deleteCrmBusinessContractWatchlistByIds(ids);
    }

    /**
     * 删除合同关注信息
     * 
     * @param id 合同关注主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessContractWatchlistById(Long id)
    {
        return crmBusinessContractWatchlistMapper.deleteCrmBusinessContractWatchlistById(id);
    }
}
