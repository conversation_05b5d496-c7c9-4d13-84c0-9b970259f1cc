import { getInfo, GetInfoResult, login, logout, type LoginResult } from '@/api/login';
import { getToken, removeToken, setToken } from '@/utils/auth';
import { ElMessage } from 'element-plus';
import { defineStore } from 'pinia';

interface ApiResponse<T> {
  code: number;
  msg?: string;
  data?: T;
}

interface UserState {
  token: string | null;
  name: string;
  avatar: string;
  roles: string[];
  permissions: string[];
  userId: number | null;
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: [],
    userId: null
  }),
  
  actions: {
    // 登录
    async login(userInfo: { username: string; password: string; code: string; uuid: string }) {
      const { username, password, code, uuid } = userInfo;
      try {
        const res: LoginResult = await login(username, password, code, uuid);
        console.log("result response", JSON.stringify(res, null, 2));
        
        if (res.code === 200) {
          setToken(res.token);
          this.token = res.token;
          ElMessage.success('登录成功');
          return true;
        } else {
          ElMessage.error(res.msg || '登录失败');
          return false;
        }
      } catch (error: any) {
        ElMessage.error(error.message || '登录失败');
        return false;
      }
    },

    // 获取用户信息
    async getInfo() {
      try {
        const res: GetInfoResult = await getInfo();
        // console.log("getInfo response", JSON.stringify(res, null, 2));
        
        if (res.code === 200) {
          const { user } = res;
          if (!res.roles || res.roles.length <= 0) {
            throw new Error('用户没有任何权限！');
          }
          this.roles = res.roles;
          this.name = user.nickName;
          this.avatar = user.avatar;
          this.permissions = res.permissions;
          this.userId = user.userId;
          return res;
        } else {
          throw new Error(res.msg || '获取用户信息失败');
        }
      } catch (error: any) {
        ElMessage.error(error.message || '获取用户信息失败');
        throw error;
      }
    },

    // 退出登录
    async logout() {
      try {
        await logout();
        this.token = null;
        this.roles = [];
        this.permissions = [];
        this.userId = null;
        removeToken();
        ElMessage.success('退出成功');
      } catch (error: any) {
        ElMessage.error(error.message || '退出失败');
        console.error('退出失败:', error);
      }
    },

    // 重置 token
    resetToken() {
      this.token = null;
      this.roles = [];
      this.permissions = [];
      this.userId = null;
      removeToken();
    }
  }
}); 