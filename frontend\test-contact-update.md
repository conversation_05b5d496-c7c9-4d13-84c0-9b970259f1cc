# 联系人详情更新功能测试指南

## 问题分析
通过代码审查，发现联系人详情更新存在以下问题：

### 1. 字段映射问题
- 前端 `phone` 字段映射到后端 `mobile` 字段
- 后端实体类缺少前端需要的扩展字段

### 2. 数据流程问题
- ContactDetailsTab.vue 的 saveData() 只发送 emit 事件
- 父组件 index.vue 的 handleContactUpdate() 接收事件并调用后端API
- 数据格式转换存在不匹配

## 修复方案

### 1. 扩展后端实体类 ✅
在 `CrmContacts.java` 中添加了以下字段：
```java
private String telephone;      // 固定电话
private Date birthday;         // 生日
private String department;     // 部门
private String decisionRole;   // 决策角色
private String contactLevel;   // 联系人级别
private String status;         // 状态
```

### 2. 修复前端数据映射 ✅
- ContactDetailsTab.vue 的 saveData() 方法中正确映射字段
- index.vue 的 handleContactUpdate() 方法支持扩展字段
- 确保前端 phone 字段映射到后端 mobile 字段

### 3. API 数据格式统一 ✅
- 前端发送的数据格式与后端实体类匹配
- 保持向后兼容性

## 测试步骤

### 1. 打开联系人详情
1. 在联系人列表中点击某个联系人
2. 打开联系人详情抽屉
3. 点击"详情"标签页

### 2. 编辑联系人信息
1. 点击"编辑"按钮
2. 修改各个字段的信息
3. 点击"保存"按钮

### 3. 验证更新结果
1. 检查是否显示"保存成功"消息
2. 验证修改的信息是否正确保存
3. 刷新页面确认数据持久化

## 预期结果
- 编辑模式下可以修改所有字段
- 保存时调用正确的后端API
- 数据正确映射到后端字段
- 界面实时更新显示最新数据

## 注意事项
1. 确保后端数据库表结构包含新增字段
2. 如果数据库表结构不匹配，需要执行数据库迁移
3. 测试时注意观察浏览器控制台的日志信息
