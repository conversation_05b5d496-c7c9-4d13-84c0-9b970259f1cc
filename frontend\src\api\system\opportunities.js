import request from '@/utils/request'

// 查询商机列表
export function listOpportunities(query) {
  return request({
    url: '/system/opportunities/list',
    method: 'get',
    params: query
  })
}

// 查询商机详细
export function getOpportunities(id) {
  return request({
    url: '/system/opportunities/' + id,
    method: 'get'
  })
}

// 新增商机
export function addOpportunities(data) {
  return request({
    url: '/system/opportunities',
    method: 'post',
    data: data
  })
}

// 修改商机
export function updateOpportunities(data) {
  return request({
    url: '/system/opportunities',
    method: 'put',
    data: data
  })
}

// 删除商机
export function delOpportunities(id) {
  return request({
    url: '/system/opportunities/' + id,
    method: 'delete'
  })
}
