package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CrmBusinessPaymentPlansMapper;
import com.ruoyi.system.domain.CrmBusinessPaymentPlans;
import com.ruoyi.system.service.ICrmBusinessPaymentPlansService;

/**
 * 回款计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class CrmBusinessPaymentPlansServiceImpl implements ICrmBusinessPaymentPlansService 
{
    @Autowired
    private CrmBusinessPaymentPlansMapper crmBusinessPaymentPlansMapper;

    /**
     * 查询回款计划
     * 
     * @param id 回款计划主键
     * @return 回款计划
     */
    @Override
    public CrmBusinessPaymentPlans selectCrmBusinessPaymentPlansById(Long id)
    {
        return crmBusinessPaymentPlansMapper.selectCrmBusinessPaymentPlansById(id);
    }

    /**
     * 查询回款计划列表
     * 
     * @param crmBusinessPaymentPlans 回款计划
     * @return 回款计划
     */
    @Override
    public List<CrmBusinessPaymentPlans> selectCrmBusinessPaymentPlansList(CrmBusinessPaymentPlans crmBusinessPaymentPlans)
    {
        return crmBusinessPaymentPlansMapper.selectCrmBusinessPaymentPlansList(crmBusinessPaymentPlans);
    }

    /**
     * 新增回款计划
     * 
     * @param crmBusinessPaymentPlans 回款计划
     * @return 结果
     */
    @Override
    public int insertCrmBusinessPaymentPlans(CrmBusinessPaymentPlans crmBusinessPaymentPlans)
    {
        return crmBusinessPaymentPlansMapper.insertCrmBusinessPaymentPlans(crmBusinessPaymentPlans);
    }

    /**
     * 修改回款计划
     * 
     * @param crmBusinessPaymentPlans 回款计划
     * @return 结果
     */
    @Override
    public int updateCrmBusinessPaymentPlans(CrmBusinessPaymentPlans crmBusinessPaymentPlans)
    {
        return crmBusinessPaymentPlansMapper.updateCrmBusinessPaymentPlans(crmBusinessPaymentPlans);
    }

    /**
     * 批量删除回款计划
     * 
     * @param ids 需要删除的回款计划主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessPaymentPlansByIds(Long[] ids)
    {
        return crmBusinessPaymentPlansMapper.deleteCrmBusinessPaymentPlansByIds(ids);
    }

    /**
     * 删除回款计划信息
     * 
     * @param id 回款计划主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessPaymentPlansById(Long id)
    {
        return crmBusinessPaymentPlansMapper.deleteCrmBusinessPaymentPlansById(id);
    }
}
