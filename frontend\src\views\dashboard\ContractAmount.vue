<template>
    <div class="chart-container">
        <div ref="chartcontractAmount" class="contract-chart"></div>
        <el-table :data="rankingData" style="width: 100%; margin-top: 20px;">
            <el-table-column prop="rank" label="公司总排名" width="120" />
            <el-table-column prop="signer" label="签订人" width="150" />
            <el-table-column prop="department" label="部门" width="150" />
            <el-table-column prop="contractAmount" label="合同金额" width="150" />
            <el-table-column prop="doctorContractAmount" label="医生和合同金额" width="180" />
            <el-table-column prop="unapprovedContractAmount" label="未审核合同金额" width="180" />
        </el-table>
    </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    data() {
        return {
            selectedMetric: 'ContractAmount',
            chart: null,
            rankingData: [
                {
                    rank: 1,
                    signer: '张三',
                    department: '销售',
                    contractAmount: 500000,
                    doctorContractAmount: 200000,
                    unapprovedContractAmount: 100000
                },
                {
                    rank: 2,
                    signer: '李四',
                    department: '市场',
                    contractAmount: 300000,
                    doctorContractAmount: 150000,
                    unapprovedContractAmount: 50000
                },
            ],
            chartData: {
                months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                contracts: Array.from({ length: 12 }, () => Math.random() * 100),
                goals: Array.from({ length: 12 }, () => Math.random() * 100),
                completionRates: Array.from({ length: 12 }, () => (Math.random() * 100).toFixed(2)),
            },
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart();
            window.addEventListener('resize', this.handleResize);
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.chart) {
            this.chart.dispose();
            this.chart = null;
        }
    },
    methods: {
        handleResize() {
            if (this.chart) {
                this.chart.resize();
            }
        },
        initChart() {
            if (this.chart) {
                this.chart.dispose();
            }

            const chartDom = this.$refs.chartcontractAmount;
            if (!chartDom) return;

            this.chart = echarts.init(chartDom);
            const options = {
                title: {
                    text: '合同金额趋势',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    data: this.chartData.months,
                },
                yAxis: {
                    type: 'value',
                    name: '金额（元）'
                },
                series: [
                    {
                        name: '合同金额',
                        data: this.chartData.contracts,
                        type: 'line',
                    },
                    {
                        name: '目标',
                        data: this.chartData.goals,
                        type: 'line',
                    },
                    {
                        name: '完成率',
                        data: this.chartData.completionRates,
                        type: 'line',
                    },
                ],
            };
            this.chart.setOption(options);
        },
    },
};
</script>

<style scoped>
.chart-container {
    width: 100%;
    height: 100%;
}
.contract-chart {
    width: 100%;
    height: 400px;
    margin-top: 20px;
}
</style>
