import type { FormConfig, FormField } from '@/types';

// 职位选项
const positionOptions = [
    { label: '总经理', value: '总经理' },
    { label: '副总经理', value: '副总经理' },
    { label: '销售总监', value: '销售总监' },
    { label: '销售经理', value: '销售经理' },
    { label: '采购总监', value: '采购总监' },
    { label: '采购经理', value: '采购经理' },
    { label: '技术总监', value: '技术总监' },
    { label: '技术经理', value: '技术经理' },
    { label: '财务总监', value: '财务总监' },
    { label: '财务经理', value: '财务经理' },
    { label: '人力资源总监', value: '人力资源总监' },
    { label: '人力资源经理', value: '人力资源经理' },
    { label: '部门经理', value: '部门经理' },
    { label: '部门主管', value: '部门主管' },
    { label: '项目经理', value: '项目经理' },
    { label: '业务员', value: '业务员' },
    { label: '其他', value: '其他' }
];

// 新建联系人表单配置
export const newContactFormConfig: FormConfig = {
    layout: {
        labelPosition: 'right',
        labelWidth: '120px',
        size: 'default',
        gutter: 20
    },
    fields: [
        // 第一行：姓名和性别
        {
            field: 'name',
            label: '联系人姓名',
            type: 'input',
            colSpan: 12,
            required: true,
            clearable: true,
            maxLength: 50,
            showWordLimit: true,
            placeholder: '请输入联系人姓名',
            prefixIcon: 'User',
            rules: [
                { required: true, message: '请输入联系人姓名', trigger: 'blur' },
                { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
            ]
        },
        {
            field: 'gender',
            label: '性别',
            type: 'radio',
            colSpan: 12,
            options: [
                { label: '男', value: '1' },
                { label: '女', value: '2' }
            ]
        },
        
        // 第二行：职位和部门
        {
            field: 'position',
            label: '职位',
            type: 'select',
            colSpan: 12,
            clearable: true,
            filterable: true,
            allowCreate: true,
            placeholder: '请选择或输入职位',
            prefixIcon: 'Briefcase',
            options: positionOptions
        },
        {
            field: 'department',
            label: '部门',
            type: 'input',
            colSpan: 12,
            clearable: true,
            placeholder: '请输入部门',
            prefixIcon: 'OfficeBuilding'
        },
        
        // 第三行：手机号码和电话号码
        {
            field: 'mobile',
            label: '手机号码',
            type: 'input',
            colSpan: 12,
            required: true,
            clearable: true,
            placeholder: '请输入手机号码',
            prefixIcon: 'Iphone',
            rules: [
                { required: true, message: '请输入手机号码', trigger: 'blur' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
            ]
        },
        {
            field: 'telephone',
            label: '固定电话',
            type: 'input',
            colSpan: 12,
            clearable: true,
            placeholder: '请输入固定电话',
            prefixIcon: 'Phone'
        },
        
        // 第四行：电子邮箱和生日
        {
            field: 'email',
            label: '电子邮箱',
            type: 'input',
            colSpan: 12,
            clearable: true,
            placeholder: '请输入电子邮箱',
            prefixIcon: 'Message',
            rules: [
                { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
            ]
        },
        {
            field: 'birthday',
            label: '生日',
            type: 'date',
            colSpan: 12,
            placeholder: '请选择生日'
        },
        
        // 第五行：所属客户（非必填）
        {
            field: 'customerId',
            label: '所属客户',
            type: 'select',
            colSpan: 12,
            required: false,  // 改为非必填
            clearable: true,
            filterable: true,
            placeholder: '请选择所属客户（可选）',
            options: [] // 需要动态获取客户列表
        },
        {
            field: 'isKeyDecisionMaker',
            label: '关键决策人',
            type: 'switch',
            colSpan: 12,
            activeValue: '1',
            inactiveValue: '0'
        },
        
        // 第六行：联系人级别和决策角色
        {
            field: 'contactLevel',
            label: '联系人级别',
            type: 'select',
            colSpan: 12,
            clearable: true,
            placeholder: '请选择联系人级别',
            options: [
                { label: '重要', value: 'important' },
                { label: '普通', value: 'normal' },
                { label: '低级', value: 'low' }
            ]
        },
        {
            field: 'decisionRole',
            label: '决策角色',
            type: 'select',
            colSpan: 12,
            clearable: true,
            placeholder: '请选择决策角色',
            options: [
                { label: '决策者', value: 'decision_maker' },
                { label: '影响者', value: 'influencer' },
                { label: '执行者', value: 'executor' },
                { label: '使用者', value: 'user' }
            ]
        },
        
        // 第七行：负责人和状态
        {
            field: 'responsiblePersonId',
            label: '负责人',
            type: 'select',
            colSpan: 12,
            required: true,
            clearable: true,
            filterable: true,
            placeholder: '请选择负责人',
            options: [] // 需要从API获取用户列表
        },
        {
            field: 'status',
            label: '状态',
            type: 'radio',
            colSpan: 12,
            options: [
                { label: '有效', value: '0' },
                { label: '无效', value: '1' }
            ]
        },
        
        // 第八行：地址
        {
            field: 'address',
            label: '地址',
            type: 'input',
            colSpan: 12,
            clearable: true,
            placeholder: '请输入地址',
            prefixIcon: 'Location'
        },
        {
            field: 'detailedAddress',
            label: '详细地址',
            type: 'input',
            colSpan: 12,
            clearable: true,
            placeholder: '请输入详细地址'
        },
        
        // 第九行：直属上级和下次联系时间
        {
            field: 'directSuperior',
            label: '直属上级',
            type: 'input',
            colSpan: 12,
            clearable: true,
            placeholder: '请输入直属上级'
        },
        {
            field: 'nextContactTime',
            label: '下次联系时间',
            type: 'datetime',
            colSpan: 12,
            placeholder: '请选择下次联系时间'
        },
        
        // 第十行：分配团队
        {
            field: 'teamId',
            label: '分配团队',
            type: 'select',
            colSpan: 12,
            clearable: true,
            filterable: true,
            placeholder: '请选择团队（可选）',
            options: [] // 需要动态获取团队列表
        },
        
        // 最后：备注
        {
            field: 'remarks',
            label: '备注',
            type: 'textarea',
            colSpan: 24,
            clearable: true,
            placeholder: '请输入备注信息',
            maxLength: 500,
            showWordLimit: true,
            rows: 4
        }
    ] as FormField[]
}; 