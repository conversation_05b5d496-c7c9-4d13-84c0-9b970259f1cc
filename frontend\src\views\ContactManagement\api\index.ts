import type { ApiResponse, ContactData, QueryParams } from '@/types';
import request from '@/utils/request';

export interface ContactForm {
    id?: number;
    name: string;
    position: string;
    phone: string;
    mobile?: string;
    email: string;
    customerId: number;
    customerName?: string; // 添加客户名称字段
    responsiblePerson: number;  // 保持兼容性，前端使用这个字段
    responsiblePersonId?: string; // 后端期望的字段名
    remarks?: string;
    // 新增字段支持
    gender?: string;
    birthday?: string;
    telephone?: string;
    address?: string;
    detailedAddress?: string;
    department?: string;
    decisionRole?: string;
    contactLevel?: string;
    nextContactTime?: string;
    status?: string;
    isKeyDecisionMaker?: string;
    directSuperior?: string;
}

// 查询联系人列表
export function listContacts(query: QueryParams): Promise<ApiResponse<ContactData>> {
    return request({
        url: '/front/crm/contacts/list',
        method: 'get',
        params: query
    });
}

// 查询联系人详细
export function getContact(id: number): Promise<ApiResponse<ContactData>> {
    return request({
        url: `/front/crm/contacts/${id}`,
        method: 'get'
    });
}

// 新增联系人
export function addContact(data: ContactForm): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/contacts',
        method: 'post',
        data: data
    });
}

// 修改联系人
export function updateContact(data: ContactForm): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/contacts',
        method: 'put',
        data: data
    });
}

// 删除联系人
export function deleteContact(id: number): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/${id}`,
        method: 'delete'
    });
}

// 导出联系人
export function exportContacts(query: QueryParams): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/contacts/export',
        method: 'get',
        params: query
    });
}

// 关注联系人
export function followContact(contactId: number): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/follow/${contactId}`,
        method: 'post'
    });
}

// 取消关注联系人
export function unfollowContact(contactId: number): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/follow/${contactId}`,
        method: 'delete'
    });
}

// 查询关注状态
export function getFollowStatus(contactId: number): Promise<ApiResponse<boolean>> {
    return request({
        url: `/front/crm/contacts/follow/status/${contactId}`,
        method: 'get'
    });
}

// 批量关注联系人
export function batchFollowContacts(contactIds: number[]): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/contacts/follow/batch',
        method: 'post',
        data: contactIds
    });
}

// 批量取消关注联系人
export function batchUnfollowContacts(contactIds: number[]): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/contacts/follow/batch',
        method: 'delete',
        data: contactIds
    });
}

// 获取客户信息
export function getCustomerInfo(customerId: number): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/customers/${customerId}`,
        method: 'get'
    });
}

// 获取联系人活动记录
export function getContactActivities(contactId: number, params?: any): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/${contactId}/activities`,
        method: 'get',
        params
    });
}

// 创建联系人活动记录
export function createContactActivity(data: any): Promise<ApiResponse<any>> {
    return request({
        url: '/front/crm/contacts/activities',
        method: 'post',
        data
    });
}

// 编辑联系人活动记录
export function updateContactActivity(contactId: number, activityId: number, data: any): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/${contactId}/activities/${activityId}`,
        method: 'put',
        data
    });
}

// 删除联系人活动记录
export function deleteContactActivity(contactId: number, activityId: number): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/${contactId}/activities/${activityId}`,
        method: 'delete'
    });
}

// 获取联系人活动记录统计
export function getContactActivityStats(contactId: number): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/${contactId}/activities/stats`,
        method: 'get'
    });
}

// 获取联系人附件列表
export function getContactAttachments(contactId: number): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/${contactId}/attachments`,
        method: 'get'
    });
}

// 上传联系人附件
export function uploadContactAttachment(contactId: number, file: File): Promise<ApiResponse<any>> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('contactId', contactId.toString());
    
    return request({
        url: '/front/crm/contacts/attachments/upload',
        method: 'post',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}

// 删除联系人附件
export function deleteContactAttachment(attachmentId: number): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/attachments/${attachmentId}`,
        method: 'delete'
    });
}

// 获取联系人操作日志
export function getContactOperations(contactId: number, params?: any): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/${contactId}/operations`,
        method: 'get',
        params
    });
}

// 获取联系人操作日志（别名）
export function getContactOperationLogs(contactId: number, params?: any): Promise<ApiResponse<any>> {
    return request({
        url: `/front/crm/contacts/${contactId}/logs`,
        method: 'get',
        params
    });
}

// 获取用户列表（用于负责人选择）
export { listUser as getUserList } from '@/api/system/user';
