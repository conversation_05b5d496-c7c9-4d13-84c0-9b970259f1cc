<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CrmBusinessContractsMapper">
    
    <resultMap type="CrmBusinessContracts" id="CrmBusinessContractsResult">
        <result property="id"    column="id"    />
        <result property="managerId"    column="manager_id"    />
        <result property="contractNumber"    column="contract_number"    />
        <result property="contractName"    column="contract_name"    />
        <result property="customerName"    column="customer_name"    />
        <result property="quotationNumber"    column="quotation_number"    />
        <result property="opportunityName"    column="opportunity_name"    />
        <result property="contractAmount"    column="contract_amount"    />
        <result property="orderDate"    column="order_date"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="customerSignatory"    column="customer_signatory"    />
        <result property="companySignatory"    column="company_signatory"    />
        <result property="remarks"    column="remarks"    />
        <result property="contractType"    column="contract_type"    />
        <result property="contractImage"    column="contract_image"    />
        <result property="attachments"    column="attachments"    />
        <result property="profit"    column="profit"    />
        <result property="totalProductCost"    column="total_product_cost"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectCrmBusinessContractsVo">
        select id, manager_id, contract_number, contract_name, customer_name, quotation_number, opportunity_name, contract_amount, order_date, start_date, end_date, customer_signatory, company_signatory, remarks, contract_type, contract_image, attachments, profit, total_product_cost, created_at, updated_at from crm_business_contracts
    </sql>

    <select id="selectCrmBusinessContractsList" parameterType="CrmBusinessContracts" resultMap="CrmBusinessContractsResult">
        <include refid="selectCrmBusinessContractsVo"/>
        <where>  
            <if test="managerId != null "> and manager_id = #{managerId}</if>
            <if test="contractNumber != null  and contractNumber != ''"> and contract_number = #{contractNumber}</if>
            <if test="contractName != null  and contractName != ''"> and contract_name like concat('%', #{contractName}, '%')</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="quotationNumber != null  and quotationNumber != ''"> and quotation_number = #{quotationNumber}</if>
            <if test="opportunityName != null  and opportunityName != ''"> and opportunity_name like concat('%', #{opportunityName}, '%')</if>
            <if test="contractAmount != null "> and contract_amount = #{contractAmount}</if>
            <if test="orderDate != null "> and order_date = #{orderDate}</if>
            <if test="startDate != null "> and start_date = #{startDate}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
            <if test="customerSignatory != null  and customerSignatory != ''"> and customer_signatory = #{customerSignatory}</if>
            <if test="companySignatory != null  and companySignatory != ''"> and company_signatory = #{companySignatory}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="contractType != null  and contractType != ''"> and contract_type = #{contractType}</if>
            <if test="contractImage != null  and contractImage != ''"> and contract_image = #{contractImage}</if>
            <if test="attachments != null  and attachments != ''"> and attachments = #{attachments}</if>
            <if test="profit != null "> and profit = #{profit}</if>
            <if test="totalProductCost != null "> and total_product_cost = #{totalProductCost}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectCrmBusinessContractsById" parameterType="Long" resultMap="CrmBusinessContractsResult">
        <include refid="selectCrmBusinessContractsVo"/>
        where id = #{id}
    </select>

    <insert id="insertCrmBusinessContracts" parameterType="CrmBusinessContracts" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_contracts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="managerId != null">manager_id,</if>
            <if test="contractNumber != null and contractNumber != ''">contract_number,</if>
            <if test="contractName != null and contractName != ''">contract_name,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="quotationNumber != null">quotation_number,</if>
            <if test="opportunityName != null">opportunity_name,</if>
            <if test="contractAmount != null">contract_amount,</if>
            <if test="orderDate != null">order_date,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="customerSignatory != null">customer_signatory,</if>
            <if test="companySignatory != null">company_signatory,</if>
            <if test="remarks != null">remarks,</if>
            <if test="contractType != null">contract_type,</if>
            <if test="contractImage != null">contract_image,</if>
            <if test="attachments != null">attachments,</if>
            <if test="profit != null">profit,</if>
            <if test="totalProductCost != null">total_product_cost,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="managerId != null">#{managerId},</if>
            <if test="contractNumber != null and contractNumber != ''">#{contractNumber},</if>
            <if test="contractName != null and contractName != ''">#{contractName},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="quotationNumber != null">#{quotationNumber},</if>
            <if test="opportunityName != null">#{opportunityName},</if>
            <if test="contractAmount != null">#{contractAmount},</if>
            <if test="orderDate != null">#{orderDate},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="customerSignatory != null">#{customerSignatory},</if>
            <if test="companySignatory != null">#{companySignatory},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="contractType != null">#{contractType},</if>
            <if test="contractImage != null">#{contractImage},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="profit != null">#{profit},</if>
            <if test="totalProductCost != null">#{totalProductCost},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateCrmBusinessContracts" parameterType="CrmBusinessContracts">
        update crm_business_contracts
        <trim prefix="SET" suffixOverrides=",">
            <if test="managerId != null">manager_id = #{managerId},</if>
            <if test="contractNumber != null and contractNumber != ''">contract_number = #{contractNumber},</if>
            <if test="contractName != null and contractName != ''">contract_name = #{contractName},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="quotationNumber != null">quotation_number = #{quotationNumber},</if>
            <if test="opportunityName != null">opportunity_name = #{opportunityName},</if>
            <if test="contractAmount != null">contract_amount = #{contractAmount},</if>
            <if test="orderDate != null">order_date = #{orderDate},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="customerSignatory != null">customer_signatory = #{customerSignatory},</if>
            <if test="companySignatory != null">company_signatory = #{companySignatory},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="contractType != null">contract_type = #{contractType},</if>
            <if test="contractImage != null">contract_image = #{contractImage},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="profit != null">profit = #{profit},</if>
            <if test="totalProductCost != null">total_product_cost = #{totalProductCost},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmBusinessContractsById" parameterType="Long">
        delete from crm_business_contracts where id = #{id}
    </delete>

    <delete id="deleteCrmBusinessContractsByIds" parameterType="String">
        delete from crm_business_contracts where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>