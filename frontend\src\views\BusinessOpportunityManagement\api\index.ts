import type { ApiResponse, FormData, OpportunityData, QueryParams } from '@/types';
import request from '@/utils/request';

const prefix = '/front/crm/opportunities';

// 查询商机列表
export function listOpportunities(query: QueryParams): Promise<ApiResponse<OpportunityData>> {
    return request({
        url: `${prefix}/list`,
        method: 'get',
        params: query
    });
}

// 查询商机详细
export function getOpportunity(id: number): Promise<ApiResponse<OpportunityData>> {
    return request({
        url: `${prefix}/${id}`,
        method: 'get'
    });
}

// 新增商机
export function addOpportunity(data: FormData): Promise<ApiResponse<any>> {
    return request({
        url: `${prefix}`,
        method: 'post',
        data: data
    });
}

// 修改商机
export function updateOpportunity(data: OpportunityData): Promise<ApiResponse<any>> {
    return request({
        url: `${prefix}`,
        method: 'put',
        data: data
    });
}

// 批量删除商机
export function deleteOpportunities(ids: number[]): Promise<ApiResponse<any>> {
    return request({
        url: `${prefix}/${ids.join(',')}`,
        method: 'delete'
    });
}

// 导出商机（POST，参数放data）
export function exportOpportunities(query: QueryParams): Promise<ApiResponse<any>> {
    return request({
        url: `${prefix}/export`,
        method: 'post',
        data: query
    });
}

// 获取我负责的商机
export function listMyOpportunities(query: QueryParams): Promise<ApiResponse<OpportunityData>> {
    return request({
        url: `${prefix}/my`,
        method: 'get',
        params: query
    });
}

// 获取下属商机
export function listSubordinateOpportunities(query: QueryParams): Promise<ApiResponse<OpportunityData>> {
    return request({
        url: `${prefix}/subordinate`,
        method: 'get',
        params: query
    });
}

// 获取我关注的商机
export function listFollowedOpportunities(query: QueryParams): Promise<ApiResponse<OpportunityData>> {
    return request({
        url: `${prefix}/followed`,
        method: 'get',
        params: query
    });
}

// 分配商机
export function assignOpportunity(data: { opportunityId: number, newOwnerId: number }): Promise<ApiResponse<any>> {
    return request({
        url: `${prefix}/assign`,
        method: 'post',
        data: data
    });
}

// 推进商机阶段
export function advanceStage(data: { opportunityId: number, newStage: string, remarks?: string }): Promise<ApiResponse<any>> {
    return request({
        url: `${prefix}/advance-stage`,
        method: 'post',
        data: data
    });
}

// 商机转化为合同
export function convertToContract(data: { opportunityId: number, contractName?: string, remarks?: string }): Promise<ApiResponse<any>> {
    return request({
        url: `${prefix}/convert-to-contract`,
        method: 'post',
        data: data
    });
} 