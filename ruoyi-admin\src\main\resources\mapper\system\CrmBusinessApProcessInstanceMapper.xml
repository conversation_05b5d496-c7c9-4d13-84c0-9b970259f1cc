<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CrmBusinessApProcessInstanceMapper">
    
    <resultMap type="CrmBusinessApProcessInstance" id="CrmBusinessApProcessInstanceResult">
        <result property="instanceId"    column="instance_id"    />
        <result property="definitionId"    column="definition_id"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="instanceStatus"    column="instance_status"    />
        <result property="instanceName"    column="instance_name"    />
    </resultMap>

    <sql id="selectCrmBusinessApProcessInstanceVo">
        select instance_id, definition_id, start_time, end_time, instance_status, instance_name from crm_business_ap_process_instance
    </sql>

    <select id="selectCrmBusinessApProcessInstanceList" parameterType="CrmBusinessApProcessInstance" resultMap="CrmBusinessApProcessInstanceResult">
        <include refid="selectCrmBusinessApProcessInstanceVo"/>
        <where>  
            <if test="definitionId != null "> and definition_id = #{definitionId}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="instanceStatus != null  and instanceStatus != ''"> and instance_status = #{instanceStatus}</if>
            <if test="instanceName != null  and instanceName != ''"> and instance_name like concat('%', #{instanceName}, '%')</if>
        </where>
    </select>
    
    <select id="selectCrmBusinessApProcessInstanceByInstanceId" parameterType="Long" resultMap="CrmBusinessApProcessInstanceResult">
        <include refid="selectCrmBusinessApProcessInstanceVo"/>
        where instance_id = #{instanceId}
    </select>

    <insert id="insertCrmBusinessApProcessInstance" parameterType="CrmBusinessApProcessInstance" useGeneratedKeys="true" keyProperty="instanceId">
        insert into crm_business_ap_process_instance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="definitionId != null">definition_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="instanceStatus != null">instance_status,</if>
            <if test="instanceName != null">instance_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="definitionId != null">#{definitionId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="instanceStatus != null">#{instanceStatus},</if>
            <if test="instanceName != null">#{instanceName},</if>
         </trim>
    </insert>

    <update id="updateCrmBusinessApProcessInstance" parameterType="CrmBusinessApProcessInstance">
        update crm_business_ap_process_instance
        <trim prefix="SET" suffixOverrides=",">
            <if test="definitionId != null">definition_id = #{definitionId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="instanceStatus != null">instance_status = #{instanceStatus},</if>
            <if test="instanceName != null">instance_name = #{instanceName},</if>
        </trim>
        where instance_id = #{instanceId}
    </update>

    <delete id="deleteCrmBusinessApProcessInstanceByInstanceId" parameterType="Long">
        delete from crm_business_ap_process_instance where instance_id = #{instanceId}
    </delete>

    <delete id="deleteCrmBusinessApProcessInstanceByInstanceIds" parameterType="String">
        delete from crm_business_ap_process_instance where instance_id in 
        <foreach item="instanceId" collection="array" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
    </delete>
</mapper>