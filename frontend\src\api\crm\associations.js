import request from '@/utils/request'

// 查询线索和用户关联列表
export function listAssociations(query) {
  return request({
    url: '/crm/associations/list',
    method: 'get',
    params: query
  })
}

// 查询线索和用户关联详细
export function getAssociations(id) {
  return request({
    url: '/crm/associations/' + id,
    method: 'get'
  })
}

// 新增线索和用户关联
export function addAssociations(data) {
  return request({
    url: '/crm/associations',
    method: 'post',
    data: data
  })
}

// 修改线索和用户关联
export function updateAssociations(data) {
  return request({
    url: '/crm/associations',
    method: 'put',
    data: data
  })
}

// 删除线索和用户关联
export function delAssociations(id) {
  return request({
    url: '/crm/associations/' + id,
    method: 'delete'
  })
}
