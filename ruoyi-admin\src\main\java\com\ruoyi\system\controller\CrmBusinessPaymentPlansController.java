package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.CrmBusinessPaymentPlans;
import com.ruoyi.system.service.ICrmBusinessPaymentPlansService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 回款计划Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Api(tags = "回款计划管理")
@RestController
@RequestMapping("/system/plans")
public class CrmBusinessPaymentPlansController extends BaseController {
    @Autowired
    private ICrmBusinessPaymentPlansService crmBusinessPaymentPlansService;

    /**
     * 查询回款计划列表
     */
    @ApiOperation("获取回款计划列表")
    @PreAuthorize("@ss.hasPermi('system:plans:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusinessPaymentPlans crmBusinessPaymentPlans) {
        startPage();
        List<CrmBusinessPaymentPlans> list = crmBusinessPaymentPlansService
                .selectCrmBusinessPaymentPlansList(crmBusinessPaymentPlans);
        return getDataTable(list);
    }

    /**
     * 导出回款计划列表
     */
    @ApiOperation("导出回款计划列表")
    @PreAuthorize("@ss.hasPermi('system:plans:export')")
    @Log(title = "回款计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,
            @ApiParam("查询条件") CrmBusinessPaymentPlans crmBusinessPaymentPlans) {
        List<CrmBusinessPaymentPlans> list = crmBusinessPaymentPlansService
                .selectCrmBusinessPaymentPlansList(crmBusinessPaymentPlans);
        ExcelUtil<CrmBusinessPaymentPlans> util = new ExcelUtil<CrmBusinessPaymentPlans>(CrmBusinessPaymentPlans.class);
        util.exportExcel(response, list, "回款计划数据");
    }

    /**
     * 获取回款计划详细信息
     */
    @ApiOperation("获取回款计划详细信息")
    @PreAuthorize("@ss.hasPermi('system:plans:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("计划ID") @PathVariable("id") Long id) {
        return success(crmBusinessPaymentPlansService.selectCrmBusinessPaymentPlansById(id));
    }

    /**
     * 新增回款计划
     */
    @ApiOperation("新建回款计划")
    @PreAuthorize("@ss.hasPermi('system:plans:add')")
    @Log(title = "回款计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("回款计划信息") @RequestBody CrmBusinessPaymentPlans crmBusinessPaymentPlans) {
        return toAjax(crmBusinessPaymentPlansService.insertCrmBusinessPaymentPlans(crmBusinessPaymentPlans));
    }

    /**
     * 修改回款计划
     */
    @ApiOperation("修改回款计划")
    @PreAuthorize("@ss.hasPermi('system:plans:edit')")
    @Log(title = "回款计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("回款计划信息") @RequestBody CrmBusinessPaymentPlans crmBusinessPaymentPlans) {
        return toAjax(crmBusinessPaymentPlansService.updateCrmBusinessPaymentPlans(crmBusinessPaymentPlans));
    }

    /**
     * 删除回款计划
     */
    @ApiOperation("删除回款计划")
    @PreAuthorize("@ss.hasPermi('system:plans:remove')")
    @Log(title = "回款计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("计划ID数组") @PathVariable Long[] ids) {
        return toAjax(crmBusinessPaymentPlansService.deleteCrmBusinessPaymentPlansByIds(ids));
    }
}
