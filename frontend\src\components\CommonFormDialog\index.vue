<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    :width="width"
    :draggable="draggable"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 标题栏 -->
    <el-row v-if="subTitle">
      <div class="form-subtitle">
        <div class="subtitle-indicator"></div>
        <h4>{{ subTitle }}</h4>
      </div>
    </el-row>

    <!-- 表单内容 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-position="formConfig.layout?.labelPosition"
      :label-width="formConfig.layout?.labelWidth"
      :size="formConfig.layout?.size"
      class="dialog-form"
    >
      <el-row :gutter="formConfig.layout?.gutter || 20">
        <template v-for="field in formConfig.fields" :key="field.field">
          <el-col
            :span="field.colSpan"
            :xs="field.responsive?.xs"
            :sm="field.responsive?.sm"
            :md="field.responsive?.md"
            :lg="field.responsive?.lg"
            :xl="field.responsive?.xl"
          >
            <el-form-item
              :label="field.label"
              :prop="field.field"
              :required="field.required"
            >
              <!-- 输入框 -->
              <el-input
                v-if="field.type === 'input'"
                v-model="formData[field.field]"
                v-bind="field.props"
                :placeholder="field.placeholder"
                :maxlength="field.maxLength"
                :show-word-limit="field.showWordLimit"
                :clearable="field.clearable"
              >
                <template v-if="field.prefixIcon" #prefix>
                  <el-icon>
                    <component :is="field.prefixIcon" />
                  </el-icon>
                </template>
                <template v-if="field.suffixIcon" #suffix>
                  <el-icon>
                    <component :is="field.suffixIcon" />
                  </el-icon>
                </template>
              </el-input>

              <!-- 选择器 -->
              <el-select
                v-else-if="field.type === 'select'"
                v-model="formData[field.field]"
                v-bind="field.props"
                :placeholder="field.placeholder"
                :clearable="field.clearable"
                :multiple="field.multiple"
                :filterable="field.filterable"
              >
                <el-option
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                  :disabled="option.disabled"
                />
              </el-select>

              <!-- 日期选择器 -->
              <el-date-picker
                v-else-if="field.type === 'date'"
                v-model="formData[field.field]"
                v-bind="field.props"
                :type="field.dateType || 'date'"
                :placeholder="field.placeholder"
                :format="field.format"
                :value-format="field.valueFormat"
                :clearable="field.clearable"
              />

              <!-- 文本域 -->
              <el-input
                v-else-if="field.type === 'textarea'"
                v-model="formData[field.field]"
                type="textarea"
                v-bind="field.props"
                :placeholder="field.placeholder"
                :maxlength="field.maxLength"
                :show-word-limit="field.showWordLimit"
                :rows="field.rows || 3"
              />

              <!-- 数字输入框 -->
              <el-input-number
                v-else-if="field.type === 'number'"
                v-model="formData[field.field]"
                v-bind="field.props"
                :min="field.min"
                :max="field.max"
                :step="field.step"
                :precision="field.precision"
                :controls="field.controls"
                :placeholder="field.placeholder"
              />

              <!-- 开关 -->
              <el-switch
                v-else-if="field.type === 'switch'"
                v-model="formData[field.field]"
                v-bind="field.props"
                :active-text="field.activeText"
                :inactive-text="field.inactiveText"
              />

              <!-- 单选框组 -->
              <el-radio-group
                v-else-if="field.type === 'radio'"
                v-model="formData[field.field]"
                v-bind="field.props"
              >
                <template v-if="field.buttonStyle">
                  <el-radio-button
                    v-for="option in field.options"
                    :key="option.value"
                    :value="option.value"
                    :disabled="option.disabled"
                  >
                    {{ option.label }}
                  </el-radio-button>
                </template>
                <template v-else>
                  <el-radio
                    v-for="option in field.options"
                    :key="option.value"
                    :value="option.value"
                    :disabled="option.disabled"
                  >
                    {{ option.label }}
                  </el-radio>
                </template>
              </el-radio-group>

              <!-- 复选框组 -->
              <el-checkbox-group
                v-else-if="field.type === 'checkbox'"
                v-model="formData[field.field]"
                v-bind="field.props"
              >
                <template v-if="field.buttonStyle">
                  <el-checkbox-button
                    v-for="option in field.options"
                    :key="option.value"
                    :label="option.value"
                    :disabled="option.disabled"
                  >
                    {{ option.label }}
                  </el-checkbox-button>
                </template>
                <template v-else>
                  <el-checkbox
                    v-for="option in field.options"
                    :key="option.value"
                    :label="option.value"
                    :disabled="option.disabled"
                  >
                    {{ option.label }}
                  </el-checkbox>
                </template>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ cancelText }}</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ confirmText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus';
import { ref, watch } from 'vue';

// 表单字段选项接口
interface FieldOption {
  label: string;
  value: any;
  disabled?: boolean;
}

// 响应式布局配置接口
interface ResponsiveConfig {
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
}

// 表单字段配置接口
interface FormField {
  field: string;
  label: string;
  type: 'input' | 'select' | 'date' | 'textarea' | 'number' | 'switch' | 'radio' | 'checkbox';
  colSpan: number;
  required?: boolean;
  rules?: any[];
  placeholder?: string;
  clearable?: boolean;
  props?: Record<string, any>;
  responsive?: ResponsiveConfig;
  prefixIcon?: string;
  suffixIcon?: string;
  maxLength?: number;
  showWordLimit?: boolean;
  options?: FieldOption[];
  multiple?: boolean;
  filterable?: boolean;
  dateType?: 'year' | 'month' | 'date' | 'dates' | 'week' | 'datetime' | 'datetimerange' | 'daterange' | 'monthrange';
  format?: string;
  valueFormat?: string;
  rows?: number;
  min?: number;
  max?: number;
  step?: number;
  precision?: number;
  controls?: boolean;
  activeText?: string;
  inactiveText?: string;
  buttonStyle?: boolean;
}

// 布局配置接口
interface LayoutConfig {
  labelPosition?: 'left' | 'right' | 'top';
  labelWidth?: string | number;
  size?: 'large' | 'default' | 'small';
  gutter?: number;
}

// 表单配置接口
interface FormConfig {
  fields: FormField[];
  layout?: LayoutConfig;
}

interface Props {
  modelValue: boolean;
  title: string;
  subTitle?: string;
  width?: string | number;
  draggable?: boolean;
  formConfig: FormConfig;
  initialData?: Record<string, any>;
  rules?: FormRules;
  loading?: boolean;
  confirmText?: string;
  cancelText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '',
  width: '50%',
  draggable: true,
  initialData: () => ({}),
  loading: false,
  confirmText: '确定',
  cancelText: '取消',
  formConfig: () => ({
    fields: []
  })
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'submit', formData: Record<string, any>): void;
  (e: 'cancel'): void;
}>();

const formRef = ref<FormInstance>();
const dialogVisible = ref(props.modelValue);
const formData = ref<Record<string, any>>({...props.initialData});
const formRules = ref<FormRules>(props.rules || {});

// 监听modelValue属性变化
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val;
});

// 监听dialogVisible变化
watch(() => dialogVisible.value, (val) => {
  emit('update:modelValue', val);
});

// 监听initialData变化
watch(() => props.initialData, (val) => {
  formData.value = {...val};
}, { deep: true });

// 监听rules变化
watch(() => props.rules, (val) => {
  formRules.value = val || {};
}, { deep: true });

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    emit('submit', formData.value);
    dialogVisible.value = false;
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 处理取消
const handleCancel = () => {
  emit('cancel');
  dialogVisible.value = false;
};

// 处理关闭
const handleClose = () => {
  formData.value = {...props.initialData};
  if (formRef.value) {
    formRef.value.resetFields();
  }
};
</script>

<style scoped>
.dialog-form {
  padding: 0 30px 30px;
}

.form-subtitle {
  display: flex;
  align-items: center;
  margin: 20px 30px;
}

.subtitle-indicator {
  width: 4px;
  height: 1.2em;
  background: var(--el-color-primary);
  border-radius: 2px;
  margin-right: 8px;
}

.form-subtitle h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number .el-input__wrapper) {
  padding-left: 11px;
  padding-right: 11px;
}

:deep(.el-dialog__body) {
  padding-top: 10px;
}
</style> 