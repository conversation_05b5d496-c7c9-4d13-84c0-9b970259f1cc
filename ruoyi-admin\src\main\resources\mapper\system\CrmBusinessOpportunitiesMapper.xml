<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CrmBusinessOpportunitiesMapper">
    
    <resultMap type="CrmBusinessOpportunities" id="CrmBusinessOpportunitiesResult">
        <result property="id"    column="id"    />
        <result property="managerId"    column="manager_id"    />
        <result property="opportunityName"    column="opportunity_name"    />
        <result property="customerName"    column="customer_name"    />
        <result property="opportunityAmount"    column="opportunity_amount"    />
        <result property="opportunityStage"    column="opportunity_stage"    />
        <result property="winRate"    column="win_rate"    />
        <result property="expectedCloseDate"    column="expected_close_date"    />
        <result property="opportunitySource"    column="opportunity_source"    />
        <result property="opportunityType"    column="opportunity_type"    />
        <result property="remarks"    column="remarks"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectCrmBusinessOpportunitiesVo">
        select id, manager_id, opportunity_name, customer_name, opportunity_amount, opportunity_stage, win_rate, expected_close_date, opportunity_source, opportunity_type, remarks, created_at, updated_at from crm_business_opportunities
    </sql>

    <select id="selectCrmBusinessOpportunitiesList" parameterType="CrmBusinessOpportunities" resultMap="CrmBusinessOpportunitiesResult">
        <include refid="selectCrmBusinessOpportunitiesVo"/>
        <where>  
            <if test="managerId != null "> and manager_id = #{managerId}</if>
            <if test="opportunityName != null  and opportunityName != ''"> and opportunity_name like concat('%', #{opportunityName}, '%')</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="opportunityAmount != null "> and opportunity_amount = #{opportunityAmount}</if>
            <if test="opportunityStage != null  and opportunityStage != ''"> and opportunity_stage = #{opportunityStage}</if>
            <if test="winRate != null "> and win_rate = #{winRate}</if>
            <if test="expectedCloseDate != null "> and expected_close_date = #{expectedCloseDate}</if>
            <if test="opportunitySource != null  and opportunitySource != ''"> and opportunity_source = #{opportunitySource}</if>
            <if test="opportunityType != null  and opportunityType != ''"> and opportunity_type = #{opportunityType}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectCrmBusinessOpportunitiesById" parameterType="Long" resultMap="CrmBusinessOpportunitiesResult">
        <include refid="selectCrmBusinessOpportunitiesVo"/>
        where id = #{id}
    </select>

    <insert id="insertCrmBusinessOpportunities" parameterType="CrmBusinessOpportunities" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_opportunities
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="managerId != null">manager_id,</if>
            <if test="opportunityName != null and opportunityName != ''">opportunity_name,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="opportunityAmount != null">opportunity_amount,</if>
            <if test="opportunityStage != null">opportunity_stage,</if>
            <if test="winRate != null">win_rate,</if>
            <if test="expectedCloseDate != null">expected_close_date,</if>
            <if test="opportunitySource != null">opportunity_source,</if>
            <if test="opportunityType != null">opportunity_type,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="managerId != null">#{managerId},</if>
            <if test="opportunityName != null and opportunityName != ''">#{opportunityName},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="opportunityAmount != null">#{opportunityAmount},</if>
            <if test="opportunityStage != null">#{opportunityStage},</if>
            <if test="winRate != null">#{winRate},</if>
            <if test="expectedCloseDate != null">#{expectedCloseDate},</if>
            <if test="opportunitySource != null">#{opportunitySource},</if>
            <if test="opportunityType != null">#{opportunityType},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateCrmBusinessOpportunities" parameterType="CrmBusinessOpportunities">
        update crm_business_opportunities
        <trim prefix="SET" suffixOverrides=",">
            <if test="managerId != null">manager_id = #{managerId},</if>
            <if test="opportunityName != null and opportunityName != ''">opportunity_name = #{opportunityName},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="opportunityAmount != null">opportunity_amount = #{opportunityAmount},</if>
            <if test="opportunityStage != null">opportunity_stage = #{opportunityStage},</if>
            <if test="winRate != null">win_rate = #{winRate},</if>
            <if test="expectedCloseDate != null">expected_close_date = #{expectedCloseDate},</if>
            <if test="opportunitySource != null">opportunity_source = #{opportunitySource},</if>
            <if test="opportunityType != null">opportunity_type = #{opportunityType},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmBusinessOpportunitiesById" parameterType="Long">
        delete from crm_business_opportunities where id = #{id}
    </delete>

    <delete id="deleteCrmBusinessOpportunitiesByIds" parameterType="String">
        delete from crm_business_opportunities where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>