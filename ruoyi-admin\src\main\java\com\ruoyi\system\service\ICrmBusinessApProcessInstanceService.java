package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmBusinessApProcessInstance;

/**
 * 流程实例Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface ICrmBusinessApProcessInstanceService 
{
    /**
     * 查询流程实例
     * 
     * @param instanceId 流程实例主键
     * @return 流程实例
     */
    public CrmBusinessApProcessInstance selectCrmBusinessApProcessInstanceByInstanceId(Long instanceId);

    /**
     * 查询流程实例列表
     * 
     * @param crmBusinessApProcessInstance 流程实例
     * @return 流程实例集合
     */
    public List<CrmBusinessApProcessInstance> selectCrmBusinessApProcessInstanceList(CrmBusinessApProcessInstance crmBusinessApProcessInstance);

    /**
     * 新增流程实例
     * 
     * @param crmBusinessApProcessInstance 流程实例
     * @return 结果
     */
    public int insertCrmBusinessApProcessInstance(CrmBusinessApProcessInstance crmBusinessApProcessInstance);

    /**
     * 修改流程实例
     * 
     * @param crmBusinessApProcessInstance 流程实例
     * @return 结果
     */
    public int updateCrmBusinessApProcessInstance(CrmBusinessApProcessInstance crmBusinessApProcessInstance);

    /**
     * 批量删除流程实例
     * 
     * @param instanceIds 需要删除的流程实例主键集合
     * @return 结果
     */
    public int deleteCrmBusinessApProcessInstanceByInstanceIds(Long[] instanceIds);

    /**
     * 删除流程实例信息
     * 
     * @param instanceId 流程实例主键
     * @return 结果
     */
    public int deleteCrmBusinessApProcessInstanceByInstanceId(Long instanceId);
}
