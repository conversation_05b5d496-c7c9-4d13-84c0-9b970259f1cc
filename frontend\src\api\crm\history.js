import request from '@/utils/request'

// 查询线索分配历史记录列表
export function listHistory(query) {
  return request({
    url: '/crm/history/list',
    method: 'get',
    params: query
  })
}

// 查询线索分配历史记录详细
export function getHistory(id) {
  return request({
    url: '/crm/history/' + id,
    method: 'get'
  })
}

// 新增线索分配历史记录
export function addHistory(data) {
  return request({
    url: '/crm/history',
    method: 'post',
    data: data
  })
}

// 修改线索分配历史记录
export function updateHistory(data) {
  return request({
    url: '/crm/history',
    method: 'put',
    data: data
  })
}

// 删除线索分配历史记录
export function delHistory(id) {
  return request({
    url: '/crm/history/' + id,
    method: 'delete'
  })
}
