<template>
    <div class="contact-attachment-tab">
        <!-- 使用通用附件组件 -->
        <AttachmentTab
            :entity-type="'contact'"
            :entity-id="entityData.id"
            :readonly="readonly"
            @update-count="handleAttachmentCountUpdate"
            @attachment-change="handleAttachmentChange"
        />
    </div>
</template>

<script>
import AttachmentTab from '@/components/Attachment/AttachmentTab.vue';

export default {
    name: 'ContactAttachmentTab',
    components: {
        AttachmentTab
    },
    props: {
        entityData: {
            type: Object,
            required: true
        },
        readonly: {
            type: Boolean,
            default: false
        }
    },
    emits: ['update-count', 'attachment-change'],
    methods: {
        // 处理附件数量更新
        handleAttachmentCountUpdate(count) {
            this.$emit('update-count', count)
        },
        
        // 处理附件变更
        handleAttachmentChange(attachments) {
            this.$emit('attachment-change', attachments)
        }
    }
}
</script>

<style scoped>
.contact-attachment-tab {
    padding: 0;
}
</style>
