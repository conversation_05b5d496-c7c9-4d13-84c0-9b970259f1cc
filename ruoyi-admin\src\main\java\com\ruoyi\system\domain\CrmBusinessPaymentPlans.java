package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 回款计划对象 crm_business_payment_plans
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public class CrmBusinessPaymentPlans extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键，自增字段 */
    private Long id;

    /** 负责人ID */
    @Excel(name = "负责人ID")
    private Long managerId;

    /** 回款计划名称 */
    @Excel(name = "回款计划名称")
    private String paymentPlanName;

    /** 客户ID */
    @Excel(name = "客户ID")
    private Long customerId;

    /** 合同ID */
    @Excel(name = "合同ID")
    private Long contractId;

    /** 回款计划明细 */
    @Excel(name = "回款计划明细")
    private String paymentPlanDetails;

    /** 回款计划日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "回款计划日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date paymentPlanDate;

    /** 回款计划金额 */
    @Excel(name = "回款计划金额")
    private BigDecimal paymentPlanAmount;

    /** 回款计划方式 */
    @Excel(name = "回款计划方式")
    private String paymentPlanMethod;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 记录创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记录创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 记录更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记录更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setManagerId(Long managerId) 
    {
        this.managerId = managerId;
    }

    public Long getManagerId() 
    {
        return managerId;
    }
    public void setPaymentPlanName(String paymentPlanName) 
    {
        this.paymentPlanName = paymentPlanName;
    }

    public String getPaymentPlanName() 
    {
        return paymentPlanName;
    }
    public void setCustomerId(Long customerId) 
    {
        this.customerId = customerId;
    }

    public Long getCustomerId() 
    {
        return customerId;
    }
    public void setContractId(Long contractId) 
    {
        this.contractId = contractId;
    }

    public Long getContractId() 
    {
        return contractId;
    }
    public void setPaymentPlanDetails(String paymentPlanDetails) 
    {
        this.paymentPlanDetails = paymentPlanDetails;
    }

    public String getPaymentPlanDetails() 
    {
        return paymentPlanDetails;
    }
    public void setPaymentPlanDate(Date paymentPlanDate) 
    {
        this.paymentPlanDate = paymentPlanDate;
    }

    public Date getPaymentPlanDate() 
    {
        return paymentPlanDate;
    }
    public void setPaymentPlanAmount(BigDecimal paymentPlanAmount) 
    {
        this.paymentPlanAmount = paymentPlanAmount;
    }

    public BigDecimal getPaymentPlanAmount() 
    {
        return paymentPlanAmount;
    }
    public void setPaymentPlanMethod(String paymentPlanMethod) 
    {
        this.paymentPlanMethod = paymentPlanMethod;
    }

    public String getPaymentPlanMethod() 
    {
        return paymentPlanMethod;
    }
    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("managerId", getManagerId())
            .append("paymentPlanName", getPaymentPlanName())
            .append("customerId", getCustomerId())
            .append("contractId", getContractId())
            .append("paymentPlanDetails", getPaymentPlanDetails())
            .append("paymentPlanDate", getPaymentPlanDate())
            .append("paymentPlanAmount", getPaymentPlanAmount())
            .append("paymentPlanMethod", getPaymentPlanMethod())
            .append("remarks", getRemarks())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}
