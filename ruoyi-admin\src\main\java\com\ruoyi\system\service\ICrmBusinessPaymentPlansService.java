package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.CrmBusinessPaymentPlans;

/**
 * 回款计划Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface ICrmBusinessPaymentPlansService 
{
    /**
     * 查询回款计划
     * 
     * @param id 回款计划主键
     * @return 回款计划
     */
    public CrmBusinessPaymentPlans selectCrmBusinessPaymentPlansById(Long id);

    /**
     * 查询回款计划列表
     * 
     * @param crmBusinessPaymentPlans 回款计划
     * @return 回款计划集合
     */
    public List<CrmBusinessPaymentPlans> selectCrmBusinessPaymentPlansList(CrmBusinessPaymentPlans crmBusinessPaymentPlans);

    /**
     * 新增回款计划
     * 
     * @param crmBusinessPaymentPlans 回款计划
     * @return 结果
     */
    public int insertCrmBusinessPaymentPlans(CrmBusinessPaymentPlans crmBusinessPaymentPlans);

    /**
     * 修改回款计划
     * 
     * @param crmBusinessPaymentPlans 回款计划
     * @return 结果
     */
    public int updateCrmBusinessPaymentPlans(CrmBusinessPaymentPlans crmBusinessPaymentPlans);

    /**
     * 批量删除回款计划
     * 
     * @param ids 需要删除的回款计划主键集合
     * @return 结果
     */
    public int deleteCrmBusinessPaymentPlansByIds(Long[] ids);

    /**
     * 删除回款计划信息
     * 
     * @param id 回款计划主键
     * @return 结果
     */
    public int deleteCrmBusinessPaymentPlansById(Long id);
}
