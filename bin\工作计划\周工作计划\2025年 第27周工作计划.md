# 2025年 第27周工作计划 (对账单、附件、无密码登录)

## 总体目标

本周集中完成以下三个核心模块的开发、测试与集成工作：
1.  **对账单模块**：完成核心功能的开发与实现。
2.  **通用附件模块**：复核、最终测试并确保其在各业务模块中正确集成。
3.  **无密码登录与认证模块**：完成后端逻辑与前端界面的开发。

---

## 任务分解

### 一、 对账单模块 (Reconciliation/Statement)

**目标**：根据现有设计文档，完成前后端主要功能的开发。

#### 后端开发
- [x] **数据库**：执行 `对账单` 模块的数据库脚本，创建相关表结构。
- [x] **实体与Mapper**：创建 `Reconciliation` 相关的实体类和 MyBatis Mapper 接口及 XML。
- [x] **Service层**：实现 `IReconciliationService` 接口，包括对账单的生成、查询、状态更新等核心业务逻辑。
- [x] **Controller层**：开发 `ReconciliationController`，提供对外的 RESTful API 接口，并编写 Swagger 文档。

#### 前端开发
- [x] **API封装**：在 `src/api/crm/` 下创建 `reconciliation.ts`，封装所有与对账单相关的接口。
- [x] **页面与组件**：
    - [x] 在 `src/views/` 下创建 `ReconciliationManagement` 页面。
    - [ ] 开发对账单列表、筛选、详情展示等组件。
    - [ ] 实现对账单生成、导出的前端交互逻辑。

#### 测试
- [ ] **集成测试**：联调前后端接口，确保数据流转正常。

---

### 二、 通用附件模块 (Common Attachment)

**目标**：复核已完成的通用附件模块，进行最终测试，并确保其在关键业务模块中无缝集成。

- [ ] **代码复核 (Code Review)**：
    - [ ] 参照《CRM通用附件模块开发完成报告》，逐一核对后端代码（Controller, Service, Mapper, Entity）是否符合规范。
    - [ ] 复核前端 `AttachmentTab.vue` 组件的实现，确认其功能完备性和 UI 风格一致性。
- [ ] **集成验证**：
    - [ ] 确认附件组件已正确集成到 `联系人` 模块。
    - [ ] 将附件组件集成到 `客户`、`商机`、`合同` 模块的详情页 Tab 中。
- [ ] **最终测试**：
    - [ ] 对附件模块进行完整的回归测试，包括上传、下载、预览、删除、批量操作等。
    - [ ] 测试在不同业务模块中的附件功能，确保 `entityType` 和 `entityId` 传递正确。
    - [ ] 压力测试：测试大文件上传和高并发下载的稳定性。
- [ ] **文档完善**：更新相关模块的开发文档，说明附件组件的使用方法。

---

### 三、 密码与登录认证模块 (Password & Login Authentication)

**目标**：根据无密码登录设计方案，完成用户注册、登录流程的开发。

#### 后端开发
- [ ] **核心逻辑**：
    - [ ] 实现基于手机/邮箱验证码的无密码登录逻辑。
    - [ ] 实现用户注册接口，支持无密码方式创建账户。
- [ ] **API接口**：
    - [ ] 开发发送验证码的接口。
    - [ ] 开发验证码登录/注册的接口。
    - [ ] 开发JWT生成与刷新逻辑。
- [ ] **安全性**：
    - [ ] 实现验证码的频率限制和有效期管理。
    - [ ] 对敏感接口增加安全防护，防止暴力破解。

#### 前端开发
- [ ] **页面开发**：
    - [ ] 改造现有登录页面 `src/views/login/index.vue`，增加"验证码登录" Tab。
    - [ ] 开发用户注册页面，支持无密码注册流程。
- [ ] **状态管理**：
    - [ ] 在 Pinia store 中改造 `login` 和 `getInfo` action，适配新的登录方式。
- [ ] **路由守卫**：更新 `src/permission.ts`，确保登录逻辑与路由权限控制无缝衔接。

#### 测试
- [ ] **功能测试**：测试完整的注册、登录、登出流程。
- [ ] **安全性测试**：测试验证码防刷、token 有效性等安全机制。
- [ ] **兼容性测试**：确保新登录方式在不同浏览器下表现一致。

---

## 备忘
- 每日站会同步进度和风险。
- 所有代码提交需遵循项目 Git Commit 规范。
- 优先完成高风险、具有依赖关系的任务。
