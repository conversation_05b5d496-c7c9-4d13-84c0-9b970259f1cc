# 对账单模块Activiti流程引擎与业务代码协作机制说明文档

## 摘要

本文档旨在详细阐述CRM系统对账单模块中，Activiti工作流引擎与本地业务代码之间如何协同工作。通过对数据层、服务层和流程层的分析，清晰地展示业务与流程解耦、状态同步及任务管理的完整实现方案。

---

## 1. 核心设计思想

对账单模块的流程管理遵循"业务与流程分离，状态通过监听器同步"的核心思想。

*   **业务与流程分离**: 本地业务代码（如`CrmReconciliationServiceImpl`）不直接处理复杂的流程逻辑（如流转、跳转）。它只负责准备数据并调用Activiti标准服务（如`RuntimeService`, `TaskService`）来启动流程或完成任务。
*   **状态同步**: 业务实体的状态（如对账单的`status`字段）与Activiti流程的状态不是直接绑定的。它们通过在BPMN流程定义中配置的**监听器（Listener）**来完成同步。当流程节点变化时，监听器被触发，从而更新业务表中的状态字段。
*   **数据驱动**: 流程的流转和决策依赖于在启动或处理任务时传入的**流程变量（Variables）**。例如，对账单的金额、申请人等信息会作为变量传递给流程，用于后续的判断和任务分配。

---

## 2. 协作机制详解

协作主要体现在以下三个层面：

### 2.1. 数据层协作 (`crm_reconciliation` 表)

业务与流程的数据集成是协作的基础。这是通过在业务实体表（`crm_reconciliation`）中增加特定的流程字段来实现的。

*   **业务字段**:
    *   `status`: 存储业务的当前状态，如 `draft`（草稿）、`submitted`（已提交）、`approved`（已审核）等。这个状态是给用户看的，并且由流程监听器来更新。
*   **流程关联字段**:
    *   `process_instance_id`: 核心关联字段，用于将一条业务记录（一个对账单）与一个具体的Activiti流程实例绑定。
    *   `process_status`: 存储Activiti流程实例的宏观状态，如 `running`、`completed`。
    *   `current_task_id`, `current_task_name`, `current_assignee`: 存储当前活动任务的信息，便于在界面上直接显示当前处理节点和处理人，而无需每次都去查询Activiti的运行时表。

这种设计将业务数据和流程快照数据存储在同一张表中，既能快速查询业务和流程的当前状态，也保持了两者在逻辑上的分离。

此外，系统还设计了两个辅助表：
*   `crm_reconciliation_process_history`: 用于记录每一步流程操作的详细历史，提供了比Activiti原生历史表更丰富的业务信息。
*   `crm_reconciliation_task_todo`: 一个统一的待办任务视图，将所有Activiti任务同步到该表中，便于进行统一的任务查询、过滤和管理，提升前端查询性能。

### 2.2. 服务层协作 (`ReconciliationWorkflowServiceImpl`)

服务层是业务逻辑与流程引擎交互的桥梁，主要通过一个专门的流程服务 `IReconciliationWorkflowService` 来实现。

*   **启动流程 (`startApprovalProcess`)**:
    1.  **权限/状态检查**: 业务代码首先检查对账单是否处于可以提交的状态（如`draft`）。
    2.  **更新业务状态**: 将业务状态更新为`submitted`，并设置好将要启动的流程定义Key (`reconciliation-approval`)。
    3.  **准备流程变量**: 将业务数据（如`reconciliationId`, `totalAmount`, `applicant`等）打包成一个`Map<String, Object>`。
    4.  **调用Activiti**: 调用`runtimeService.startProcessInstanceByKey()`方法，将业务ID作为`businessKey`，并传入流程变量，启动一个新的流程实例。
    5.  **回写流程信息**: 将返回的`processInstanceId`和其他任务信息更新回对账单业务表中。

*   **处理任务 (`processApprovalTask`)**:
    1.  **获取任务**: 用户在界面上处理待办任务时，前端将`taskId`传递给后端。
    2.  **设置任务变量**: 业务逻辑根据用户的操作（如"同意"或"驳回"）和填写的意见，准备好要传递给下一个节点的任务变量（如`approvalResult`, `approvalComments`）。
    3.  **调用Activiti**: 调用`taskService.complete(taskId, taskVariables)`来完成当前任务。Activiti引擎会根据BPMN定义和传入的变量，自动将流程推向下一个节点（例如，根据`approvalResult`是`approved`还是`rejected`走不同的路径）。

### 2.3. 流程层协作 (BPMN 与 Listener)

流程层定义了业务流程的"剧本"，而监听器则是连接"剧本"与"现实"（业务数据）的关键。

*   **BPMN 定义**:
    *   在BPMN流程图中，关键的节点（如"财务审批"）或流转（如"审批通过"的线）上会配置Java类监听器（`JavaDelegate`）。
    *   例如，在财务审批任务完成后，会配置一个名为`reconciliationStatusListener`的监听器。

*   **监听器 (`ReconciliationStatusListener`)**:
    1.  **触发**: 当`taskService.complete()`被调用，并且流程流转到配置了监听器的地方时，Activiti引擎会自动实例化并执行这个`ReconciliationStatusListener`类。
    2.  **获取流程变量**: 在监听器内部，可以通过`DelegateExecution`对象安全地获取当前流程实例的所有变量（如`reconciliationId`, `approvalResult`等）。
    3.  **更新业务数据库**: 监听器会调用本地的业务服务（`ICrmReconciliationService`），根据获取到的流程变量去更新对账单主表的`status`字段（例如，更新为`approved`或`rejected`）。

这个机制确保了**业务状态的更新是由流程驱动的**，并且这个更新操作与用户处理任务的Service层代码是解耦的，保证了单一职责原则。

---

## 3. 流程图与时序图

### 业务-流程协作流程图
```mermaid
graph TD
    A[用户在UI提交对账单] --> B(Controller层接收请求);
    B --> C[业务Service层: startApprovalProcess];
    C --> D{"检查业务状态<br>更新业务表状态为\'submitted\'"};
    D --> E["准备流程变量<br>（reconciliationId, amount, ...）"];
    E --> F(4. 调用Activiti: runtimeService.startProcessInstanceByKey);
    F --> G[Activiti引擎创建流程实例和任务];
    G --> H(5. 业务Service层回写process_instance_id到业务表);
    
    subgraph 后续审批
        direction LR
        I[用户处理待办任务] --> J(Controller层接收请求);
        J --> K[业务Service层: processApprovalTask];
        K --> L("准备任务变量<br>approvalResult: \'approved\'");
        L --> M(2. 调用Activiti: taskService.complete);
        M --> N[Activiti引擎根据BPMN流转];
        N --> O(3. 触发BPMN中配置的Listener);
        O --> P["Listener执行<br>更新业务表状态为\'approved\'"];
    end

    H --> I;
```

### 核心时序图
```mermaid
sequenceDiagram
    participant User
    participant BusinessService as 业务服务
    participant ActivitiEngine as Activiti引擎
    participant Listener as 状态监听器
    participant Database as 数据库
    
    User->>BusinessService: 提交审核(reconciliationId)
    BusinessService->>Database: 更新业务状态为 'submitted'
    BusinessService->>ActivitiEngine: startProcessInstanceByKey(businessKey, variables)
    ActivitiEngine-->>BusinessService: 返回 processInstanceId
    BusinessService->>Database: 回写 process_instance_id
    
    Note right of User: ...一段时间后，审批人处理任务...

    User->>BusinessService: 完成任务(taskId, 'approved', '同意')
    BusinessService->>ActivitiEngine: complete(taskId, variables)
    ActivitiEngine->>Listener: 执行execute()方法
    Listener->>Database: 更新业务状态为 'approved'
    
```
---

## 4. 总结

对账单模块的Activiti集成方案是一个典型的、健壮的业务流程管理（BPM）实现。它通过以下方式实现了高效协作：

1.  **数据层面**: 在业务表中预留流程字段，实现业务与流程的快速关联和状态查询。
2.  **服务层面**: 通过专门的`WorkflowService`封装与Activiti引擎的交互，使业务代码保持纯粹。
3.  **流程层面**: 利用BPMN定义流程，并通过监听器（Listener）作为回调机制来同步更新业务数据，实现了流程对业务的反向驱动。
4.  **辅助表**: 通过独立的待办任务表和历史记录表，优化了查询性能并丰富了业务追溯能力。

该方案逻辑清晰，层次分明，易于维护和扩展，为其他需要集成工作流的业务模块提供了良好的范例。 

