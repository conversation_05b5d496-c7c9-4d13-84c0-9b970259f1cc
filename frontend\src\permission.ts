import { useUserStore } from '@/store/modules/user';
import { getToken } from '@/utils/auth';
import { ElMessage } from 'element-plus';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import router from './router';

NProgress.configure({ showSpinner: false });

// 白名单路由
const whiteList = ['/login', '/auth-redirect', '/wecom-callback', '/404', '/401', '/3d-printing/quote'];

router.beforeEach(async (to, from, next) => {
  NProgress.start();
  const userStore = useUserStore();
  
  // 检查路由是否需要验证登录状态
  const requiresAuth = to.meta.requiresAuth !== false;
  
  if (getToken()) {
    if (to.path === '/login') {
      next({ path: '/' });
      NProgress.done();
    } else {
      if (userStore.roles.length === 0) {
        try {
          // 获取用户信息
          const { roles } = await userStore.getInfo();
          console.log("roles", roles);
          // 动态路由，根据用户权限动态加载路由
          // TODO: 这里可以添加动态路由的逻辑
          
          next({ ...to, replace: true });
        } catch (error) {
          console.log("error", error);
          // 获取用户信息失败，重新登录
          await userStore.logout();
          ElMessage.error(error as string || '获取用户信息失败');
          next(`/login?redirect=${encodeURIComponent(to.fullPath)}`);
          NProgress.done();
        }
      } else {
        next();
      }
    }
  } else {
    if (!requiresAuth || whiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      next(`/login?redirect=${encodeURIComponent(to.fullPath)}`);
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
