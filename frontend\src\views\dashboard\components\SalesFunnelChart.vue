<template>
  <div class="sales-funnel-chart">
    <div class="chart-content">
      <div class="chart-main" ref="chartRef"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  
  const funnelData = [
    { value: 100, name: '线索' },
    { value: 80, name: '意向' },
    { value: 60, name: '需求确认' },
    { value: 40, name: '方案制定' },
    { value: 25, name: '商务谈判' },
    { value: 15, name: '合同签订' },
    { value: 10, name: '回款' }
  ]
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      left: 'center',
      textStyle: {
        fontSize: 12,
        color: '#666'
      },
      itemWidth: 12,
      itemHeight: 12
    },
    series: [
      {
        name: '销售漏斗',
        type: 'funnel',
        left: '10%',
        top: '10%',
        width: '80%',
        height: '70%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside',
          formatter: '{b}: {c}',
          fontSize: 12,
          fontWeight: 'bold',
          color: '#fff'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 14
          }
        },
        data: funnelData.map((item, index) => ({
          ...item,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: `hsl(${210 + index * 15}, 70%, ${65 - index * 5}%)`
              },
              {
                offset: 1,
                color: `hsl(${210 + index * 15}, 70%, ${45 - index * 3}%)`
              }
            ])
          }
        }))
      }
    ]
  }
  
  chartInstance.setOption(option)
  
  // 响应式调整
  const resizeObserver = new ResizeObserver(() => {
    chartInstance?.resize()
  })
  resizeObserver.observe(chartRef.value)
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style lang="scss" scoped>
.sales-funnel-chart {
  padding: 24px;
  height: 100%;
  
  .chart-content {
    height: 100%;
    
    .chart-main {
      height: 100%;
      min-height: 350px;
    }
  }
}
</style> 