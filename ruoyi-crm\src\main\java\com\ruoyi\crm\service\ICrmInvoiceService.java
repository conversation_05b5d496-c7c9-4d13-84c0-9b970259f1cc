package com.ruoyi.crm.service;

import java.util.List;
import com.ruoyi.common.domain.CrmInvoice;

/**
 * 发票Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface ICrmInvoiceService {
    /**
     * 查询发票
     * 
     * @param id 发票主键
     * @return 发票
     */
    public CrmInvoice selectCrmInvoiceById(Long id);

    /**
     * 查询发票列表
     * 
     * @param crmInvoice 发票
     * @return 发票集合
     */
    public List<CrmInvoice> selectCrmInvoiceList(CrmInvoice crmInvoice);

    /**
     * 新增发票
     * 
     * @param crmInvoice 发票
     * @return 结果
     */
    public int insertCrmInvoice(CrmInvoice crmInvoice);

    /**
     * 修改发票
     * 
     * @param crmInvoice 发票
     * @return 结果
     */
    public int updateCrmInvoice(CrmInvoice crmInvoice);

    /**
     * 批量删除发票
     * 
     * @param ids 需要删除的发票主键集合
     * @return 结果
     */
    public int deleteCrmInvoiceByIds(Long[] ids);

    /**
     * 删除发票信息
     * 
     * @param id 发票主键
     * @return 结果
     */
    public int deleteCrmInvoiceById(Long id);

    /**
     * 根据发票编号查询发票
     * 
     * @param invoiceNo 发票编号
     * @return 发票
     */
    public CrmInvoice selectCrmInvoiceByInvoiceNo(String invoiceNo);

    /**
     * 根据报价单ID查询发票列表
     * 
     * @param quotationId 报价单ID
     * @return 发票集合
     */
    public List<CrmInvoice> selectCrmInvoiceByQuotationId(Long quotationId);

    /**
     * 从报价单创建发票
     * 
     * @param quotationId 报价单ID
     * @return 发票
     */
    public CrmInvoice createInvoiceFromQuotation(Long quotationId);

    /**
     * 提交发票审批
     * 
     * @param invoiceId 发票ID
     * @param processDefinitionKey 流程定义Key
     * @return 结果
     */
    public int submitInvoiceApproval(Long invoiceId, String processDefinitionKey);

    /**
     * 审批发票
     * 
     * @param taskId 任务ID
     * @param approved 是否通过
     * @param comment 审批意见
     * @return 结果
     */
    public int approveInvoice(String taskId, boolean approved, String comment);

    /**
     * 开票
     * 
     * @param invoiceId 发票ID
     * @return 结果
     */
    public int issueInvoice(Long invoiceId);

    /**
     * 作废发票
     * 
     * @param invoiceId 发票ID
     * @param reason 作废原因
     * @return 结果
     */
    public int cancelInvoice(Long invoiceId, String reason);

    /**
     * 生成发票编号
     * 
     * @return 发票编号
     */
    public String generateInvoiceNo();

    /**
     * 计算发票金额（含税、不含税、税额）
     * 
     * @param crmInvoice 发票
     */
    public void calculateInvoiceAmounts(CrmInvoice crmInvoice);
}