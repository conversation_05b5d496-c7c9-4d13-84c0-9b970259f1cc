<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM系统完整数据字典</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .header .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .nav-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .nav-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .nav-section {
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        
        .nav-section h4 {
            color: #495057;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .nav-section ul {
            list-style: none;
        }
        
        .nav-section li {
            margin-bottom: 5px;
        }
        
        .nav-section a {
            color: #007bff;
            text-decoration: none;
            font-size: 0.9rem;
            padding: 2px 0;
            display: block;
            transition: color 0.2s;
        }
        
        .nav-section a:hover {
            color: #0056b3;
            text-decoration: underline;
        }
        
        .section {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 3px solid #007bff;
            font-weight: 600;
        }
        
        .table-section {
            margin-bottom: 40px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .table-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .table-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #007bff;
            margin-bottom: 8px;
        }
        
        .table-desc {
            color: #6c757d;
            font-style: italic;
            margin-bottom: 10px;
        }
        
        .table-info {
            display: flex;
            gap: 20px;
            font-size: 0.9rem;
        }
        
        .table-info span {
            background: #fff;
            padding: 4px 12px;
            border-radius: 15px;
            border: 1px solid #dee2e6;
        }
        
        .field-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        
        .field-table th {
            background: #343a40;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            font-size: 0.9rem;
            border-bottom: 2px solid #495057;
        }
        
        .field-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.85rem;
            vertical-align: top;
        }
        
        .field-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .field-table tr:hover {
            background-color: #e3f2fd;
        }
        
        .primary-key {
            background-color: #fff3cd;
            font-weight: 600;
        }
        
        .foreign-key {
            background-color: #d1ecf1;
        }
        
        .required {
            color: #dc3545;
            font-weight: 600;
        }
        
        .data-type {
            font-family: 'Courier New', monospace;
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 600;
        }
        
        .index-info {
            background: #e8f5e8;
            padding: 15px;
            margin-top: 10px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        
        .index-info h5 {
            color: #155724;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .index-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .index-item {
            background: #d4edda;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            border: 1px solid #c3e6cb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #007bff;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s;
            display: none;
        }
        
        .back-to-top:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .update-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .update-info strong {
            color: #856404;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .nav-grid {
                grid-template-columns: 1fr;
            }
            
            .field-table {
                font-size: 0.8rem;
            }
            
            .field-table th,
            .field-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>CRM系统完整数据字典</h1>
            <div class="subtitle">全面详细的数据库表结构说明文档</div>
        </div>
        
        <div class="update-info">
            <strong>最后更新时间：</strong><span id="update-time"></span><br>
            <strong>版本说明：</strong>完整重构版本，包含所有表的详细字段信息、索引信息和表关系说明
        </div>

        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-number">24</div>
                <div class="stats-label">工作流引擎表</div>
            </div>
            <div class="stats-card">
                <div class="stats-number">50</div>
                <div class="stats-label">CRM业务表</div>
            </div>
            <div class="stats-card">
                <div class="stats-number">16</div>
                <div class="stats-label">系统管理表</div>
            </div>
            <div class="stats-card">
                <div class="stats-number">11</div>
                <div class="stats-label">定时任务表</div>
            </div>
            <div class="stats-card">
                <div class="stats-number">4</div>
                <div class="stats-label">数据库视图</div>
            </div>
        </div>

        <div class="nav-container">
            <div class="nav-title">📋 快速导航目录</div>
            <div class="nav-grid">
                <div class="nav-section">
                    <h4>🔄 工作流引擎表</h4>
                    <ul>
                        <li><a href="#act_evt_log">act_evt_log - 事件日志表</a></li>
                        <li><a href="#act_ge_bytearray">act_ge_bytearray - 通用字节数组表</a></li>
                        <li><a href="#act_ge_property">act_ge_property - 属性数据表</a></li>
                        <li><a href="#act_procdef_info">act_procdef_info - 流程定义信息表</a></li>
                        <li><a href="#act_re_deployment">act_re_deployment - 部署信息表</a></li>
                        <li><a href="#act_re_model">act_re_model - 流程设计模型表</a></li>
                        <li><a href="#act_re_procdef">act_re_procdef - 流程定义表</a></li>
                        <li><a href="#act_ru_deadletter_job">act_ru_deadletter_job - 无法执行的工作表</a></li>
                        <li><a href="#act_ru_event_subscr">act_ru_event_subscr - 运行时事件订阅表</a></li>
                        <li><a href="#act_ru_execution">act_ru_execution - 运行时流程执行实例表</a></li>
                        <li><a href="#act_ru_identitylink">act_ru_identitylink - 运行时用户关系表</a></li>
                        <li><a href="#act_ru_integration">act_ru_integration - 运行时集成表</a></li>
                        <li><a href="#act_ru_job">act_ru_job - 运行时作业表</a></li>
                        <li><a href="#act_ru_suspended_job">act_ru_suspended_job - 暂停作业表</a></li>
                        <li><a href="#act_ru_task">act_ru_task - 运行时任务表</a></li>
                        <li><a href="#act_ru_timer_job">act_ru_timer_job - 定时作业表</a></li>
                        <li><a href="#act_ru_variable">act_ru_variable - 运行时变量表</a></li>
                    </ul>
                </div>
                
                <div class="nav-section">
                    <h4>💼 CRM核心业务表</h4>
                    <ul>
                        <li><a href="#crm_business_leads">crm_business_leads - 线索表</a></li>
                        <li><a href="#crm_business_customers">crm_business_customers - 客户表</a></li>
                        <li><a href="#crm_business_contacts">crm_business_contacts - 联系人表</a></li>
                        <li><a href="#crm_business_opportunities">crm_business_opportunities - 商机表</a></li>
                        <li><a href="#crm_business_contracts">crm_business_contracts - 合同表</a></li>
                        <li><a href="#crm_business_payments">crm_business_payments - 回款表</a></li>
                        <li><a href="#crm_products">crm_products - 产品表</a></li>
                        <li><a href="#crm_visit_plans">crm_visit_plans - 拜访计划表</a></li>
                        <li><a href="#crm_contact_team_members">crm_contact_team_members - 联系人团队成员表</a></li>
                        <li><a href="#crm_static_files">crm_static_files - 静态文件表</a></li>
                    </ul>
                </div>
                
                <div class="nav-section">
                    <h4>⚙️ 系统管理表</h4>
                    <ul>
                        <li><a href="#sys_user">sys_user - 用户信息表</a></li>
                        <li><a href="#sys_role">sys_role - 角色信息表</a></li>
                        <li><a href="#sys_menu">sys_menu - 菜单权限表</a></li>
                        <li><a href="#sys_dept">sys_dept - 部门表</a></li>
                        <li><a href="#sys_config">sys_config - 参数配置表</a></li>
                        <li><a href="#sys_dict_type">sys_dict_type - 字典类型表</a></li>
                        <li><a href="#sys_dict_data">sys_dict_data - 字典数据表</a></li>
                        <li><a href="#sys_oper_log">sys_oper_log - 操作日志表</a></li>
                    </ul>
                </div>
                
                <div class="nav-section">
                    <h4>⏰ 定时任务表</h4>
                    <ul>
                        <li><a href="#qrtz_job_details">qrtz_job_details - 任务详细信息表</a></li>
                        <li><a href="#qrtz_triggers">qrtz_triggers - 触发器详细信息表</a></li>
                        <li><a href="#qrtz_cron_triggers">qrtz_cron_triggers - Cron类型触发器表</a></li>
                        <li><a href="#qrtz_simple_triggers">qrtz_simple_triggers - 简单触发器信息表</a></li>
                        <li><a href="#sys_job">sys_job - 定时任务调度表</a></li>
                        <li><a href="#sys_job_log">sys_job_log - 定时任务调度日志表</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 工作流引擎表 -->
        <div class="section" id="workflow-engine">
            <h2 class="section-title">🔄 工作流引擎表</h2>
            <p>基于Activiti工作流引擎的表结构，用于处理业务流程的定义、执行和管理。</p>

            <!-- act_evt_log -->
            <div class="table-section" id="act_evt_log">
                <div class="table-header">
                    <div class="table-name">act_evt_log</div>
                    <div class="table-desc">存储工作流引擎的事件日志</div>
                    <div class="table-info">
                        <span>引擎：InnoDB</span>
                        <span>字符集：utf8</span>
                        <span>类型：Activiti表</span>
                    </div>
                </div>
                <table class="field-table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>数据类型</th>
                            <th>长度</th>
                            <th>允许空</th>
                            <th>默认值</th>
                            <th>主键</th>
                            <th>字段说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>LOG_NR_</td>
                            <td><span class="data-type">bigint</span></td>
                            <td>20</td>
                            <td><span class="required">否</span></td>
                            <td>AUTO_INCREMENT</td>
                            <td>✓</td>
                            <td>日志编号，主键，自增</td>
                        </tr>
                        <tr>
                            <td>TYPE_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>事件类型</td>
                        </tr>
                        <tr>
                            <td>PROC_DEF_ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>流程定义ID</td>
                        </tr>
                        <tr>
                            <td>PROC_INST_ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>流程实例ID</td>
                        </tr>
                        <tr>
                            <td>EXECUTION_ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>执行实例ID</td>
                        </tr>
                        <tr>
                            <td>TASK_ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>任务ID</td>
                        </tr>
                        <tr>
                            <td>TIME_STAMP_</td>
                            <td><span class="data-type">timestamp</span></td>
                            <td>3</td>
                            <td><span class="required">否</span></td>
                            <td>CURRENT_TIMESTAMP(3)</td>
                            <td>-</td>
                            <td>时间戳</td>
                        </tr>
                        <tr>
                            <td>USER_ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>用户ID</td>
                        </tr>
                        <tr>
                            <td>DATA_</td>
                            <td><span class="data-type">longblob</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>事件数据</td>
                        </tr>
                        <tr>
                            <td>LOCK_OWNER_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>锁拥有者</td>
                        </tr>
                        <tr>
                            <td>LOCK_TIME_</td>
                            <td><span class="data-type">timestamp</span></td>
                            <td>3</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>锁定时间</td>
                        </tr>
                        <tr>
                            <td>IS_PROCESSED_</td>
                            <td><span class="data-type">tinyint</span></td>
                            <td>4</td>
                            <td>是</td>
                            <td>0</td>
                            <td>-</td>
                            <td>是否已处理（0否 1是）</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- act_ge_bytearray -->
            <div class="table-section" id="act_ge_bytearray">
                <div class="table-header">
                    <div class="table-name">act_ge_bytearray</div>
                    <div class="table-desc">存储流程定义和流程资源文件的二进制数据</div>
                    <div class="table-info">
                        <span>引擎：InnoDB</span>
                        <span>字符集：utf8</span>
                        <span>类型：Activiti表</span>
                    </div>
                </div>
                <table class="field-table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>数据类型</th>
                            <th>长度</th>
                            <th>允许空</th>
                            <th>默认值</th>
                            <th>主键</th>
                            <th>字段说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>✓</td>
                            <td>主键ID</td>
                        </tr>
                        <tr>
                            <td>REV_</td>
                            <td><span class="data-type">int</span></td>
                            <td>11</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>版本号</td>
                        </tr>
                        <tr>
                            <td>NAME_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>资源名称</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>DEPLOYMENT_ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>部署ID，外键关联act_re_deployment.ID_</td>
                        </tr>
                        <tr>
                            <td>BYTES_</td>
                            <td><span class="data-type">longblob</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>二进制数据</td>
                        </tr>
                        <tr>
                            <td>GENERATED_</td>
                            <td><span class="data-type">tinyint</span></td>
                            <td>4</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>是否自动生成</td>
                        </tr>
                    </tbody>
                </table>
                <div class="index-info">
                    <h5>索引信息</h5>
                    <div class="index-list">
                        <span class="index-item">ACT_FK_BYTEARR_DEPL (DEPLOYMENT_ID_)</span>
                    </div>
                </div>
            </div>

            <!-- act_ge_property -->
            <div class="table-section" id="act_ge_property">
                <div class="table-header">
                    <div class="table-name">act_ge_property</div>
                    <div class="table-desc">存储工作流引擎级别的属性数据</div>
                    <div class="table-info">
                        <span>引擎：InnoDB</span>
                        <span>字符集：utf8</span>
                        <span>类型：Activiti表</span>
                    </div>
                </div>
                <table class="field-table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>数据类型</th>
                            <th>长度</th>
                            <th>允许空</th>
                            <th>默认值</th>
                            <th>主键</th>
                            <th>字段说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>NAME_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>✓</td>
                            <td>属性名称，主键</td>
                        </tr>
                        <tr>
                            <td>VALUE_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>300</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>属性值</td>
                        </tr>
                        <tr>
                            <td>REV_</td>
                            <td><span class="data-type">int</span></td>
                            <td>11</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>版本号</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- act_procdef_info -->
            <div class="table-section" id="act_procdef_info">
                <div class="table-header">
                    <div class="table-name">act_procdef_info</div>
                    <div class="table-desc">存储流程定义的相关信息</div>
                    <div class="table-info">
                        <span>引擎：InnoDB</span>
                        <span>字符集：utf8</span>
                        <span>类型：Activiti表</span>
                    </div>
                </div>
                <table class="field-table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>数据类型</th>
                            <th>长度</th>
                            <th>允许空</th>
                            <th>默认值</th>
                            <th>主键</th>
                            <th>字段说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>✓</td>
                            <td>主键ID</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>PROC_DEF_ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>-</td>
                            <td>流程定义ID，外键关联act_re_procdef.ID_</td>
                        </tr>
                        <tr>
                            <td>REV_</td>
                            <td><span class="data-type">int</span></td>
                            <td>11</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>版本号</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>INFO_JSON_ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>信息JSON ID，外键关联act_ge_bytearray.ID_</td>
                        </tr>
                    </tbody>
                </table>
                <div class="index-info">
                    <h5>索引信息</h5>
                    <div class="index-list">
                        <span class="index-item">ACT_UNIQ_INFO_PROCDEF (PROC_DEF_ID_) - 唯一索引</span>
                        <span class="index-item">ACT_IDX_INFO_PROCDEF (PROC_DEF_ID_)</span>
                        <span class="index-item">ACT_FK_INFO_JSON_BA (INFO_JSON_ID_)</span>
                    </div>
                </div>
            </div>

            <!-- act_ru_task -->
            <div class="table-section" id="act_ru_task">
                <div class="table-header">
                    <div class="table-name">act_ru_task</div>
                    <div class="table-desc">运行时任务表，存储当前运行中的任务实例</div>
                    <div class="table-info">
                        <span>引擎：InnoDB</span>
                        <span>字符集：utf8</span>
                        <span>类型：Activiti运行时表</span>
                    </div>
                </div>
                <table class="field-table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>数据类型</th>
                            <th>长度</th>
                            <th>允许空</th>
                            <th>默认值</th>
                            <th>主键</th>
                            <th>字段说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>✓</td>
                            <td>任务ID，主键</td>
                        </tr>
                        <tr>
                            <td>REV_</td>
                            <td><span class="data-type">int</span></td>
                            <td>11</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>版本号</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>EXECUTION_ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>执行实例ID，外键关联act_ru_execution.ID_</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>PROC_INST_ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>流程实例ID，外键关联act_ru_execution.ID_</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>PROC_DEF_ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>流程定义ID，外键关联act_re_procdef.ID_</td>
                        </tr>
                        <tr>
                            <td>NAME_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>任务名称</td>
                        </tr>
                        <tr>
                            <td>BUSINESS_KEY_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>业务键</td>
                        </tr>
                        <tr>
                            <td>PARENT_TASK_ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>父任务ID</td>
                        </tr>
                        <tr>
                            <td>DESCRIPTION_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>4000</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>任务描述</td>
                        </tr>
                        <tr>
                            <td>TASK_DEF_KEY_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>任务定义键</td>
                        </tr>
                        <tr>
                            <td>OWNER_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>任务所有者</td>
                        </tr>
                        <tr>
                            <td>ASSIGNEE_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>任务执行人</td>
                        </tr>
                        <tr>
                            <td>DELEGATION_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>委托状态</td>
                        </tr>
                        <tr>
                            <td>PRIORITY_</td>
                            <td><span class="data-type">int</span></td>
                            <td>11</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>任务优先级</td>
                        </tr>
                        <tr>
                            <td>CREATE_TIME_</td>
                            <td><span class="data-type">timestamp</span></td>
                            <td>3</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>创建时间</td>
                        </tr>
                        <tr>
                            <td>DUE_DATE_</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>3</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>截止时间</td>
                        </tr>
                        <tr>
                            <td>CATEGORY_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>任务分类</td>
                        </tr>
                        <tr>
                            <td>SUSPENSION_STATE_</td>
                            <td><span class="data-type">int</span></td>
                            <td>11</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>暂停状态</td>
                        </tr>
                        <tr>
                            <td>TENANT_ID_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>''</td>
                            <td>-</td>
                            <td>租户ID</td>
                        </tr>
                        <tr>
                            <td>FORM_KEY_</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>表单键</td>
                        </tr>
                        <tr>
                            <td>CLAIM_TIME_</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>3</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>认领时间</td>
                        </tr>
                        <tr>
                            <td>APP_VERSION_</td>
                            <td><span class="data-type">int</span></td>
                            <td>11</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>应用版本</td>
                        </tr>
                    </tbody>
                </table>
                <div class="index-info">
                    <h5>索引信息</h5>
                    <div class="index-list">
                        <span class="index-item">ACT_IDX_TASK_CREATE (CREATE_TIME_)</span>
                        <span class="index-item">ACT_FK_TASK_EXE (EXECUTION_ID_)</span>
                        <span class="index-item">ACT_FK_TASK_PROCINST (PROC_INST_ID_)</span>
                        <span class="index-item">ACT_FK_TASK_PROCDEF (PROC_DEF_ID_)</span>
                    </div>
                </div>
            </div>

        </div>

        <!-- CRM核心业务表 -->
        <div class="section" id="crm-business">
            <h2 class="section-title">💼 CRM核心业务表</h2>
            <p>CRM系统的核心业务数据表，包括线索、客户、联系人、商机、合同等主要业务实体。</p>

            <!-- crm_business_leads -->
            <div class="table-section" id="crm_business_leads">
                <div class="table-header">
                    <div class="table-name">crm_business_leads</div>
                    <div class="table-desc">线索表，存储销售线索信息</div>
                    <div class="table-info">
                        <span>引擎：MyISAM</span>
                        <span>字符集：utf8</span>
                        <span>类型：CRM业务表</span>
                    </div>
                </div>
                <table class="field-table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>数据类型</th>
                            <th>长度</th>
                            <th>允许空</th>
                            <th>默认值</th>
                            <th>主键</th>
                            <th>字段说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td><span class="data-type">int</span></td>
                            <td>11</td>
                            <td><span class="required">否</span></td>
                            <td>AUTO_INCREMENT</td>
                            <td>✓</td>
                            <td>主键，自增字段</td>
                        </tr>
                        <tr>
                            <td>responsible_person_id</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>-</td>
                            <td>负责人ID</td>
                        </tr>
                        <tr>
                            <td>lead_name</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>-</td>
                            <td>线索名称</td>
                        </tr>
                        <tr>
                            <td>lead_source</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>线索来源，例如网络、推荐等</td>
                        </tr>
                        <tr>
                            <td>mobile</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>20</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>手机号码</td>
                        </tr>
                        <tr>
                            <td>phone</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>20</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>电话号码</td>
                        </tr>
                        <tr>
                            <td>email</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>电子邮件地址</td>
                        </tr>
                        <tr>
                            <td>address</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>地址</td>
                        </tr>
                        <tr>
                            <td>detailed_address</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>详细地址信息</td>
                        </tr>
                        <tr>
                            <td>customer_industry</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>客户所属行业</td>
                        </tr>
                        <tr>
                            <td>customer_level</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>50</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>客户级别，例如潜在客户、高级客户等</td>
                        </tr>
                        <tr>
                            <td>next_contact_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>下次联系时间</td>
                        </tr>
                        <tr>
                            <td>selected_date</td>
                            <td><span class="data-type">date</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>选择日期</td>
                        </tr>
                        <tr>
                            <td>remarks</td>
                            <td><span class="data-type">text</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>备注信息</td>
                        </tr>
                        <tr>
                            <td>created_at</td>
                            <td><span class="data-type">timestamp</span></td>
                            <td>-</td>
                            <td><span class="required">否</span></td>
                            <td>CURRENT_TIMESTAMP</td>
                            <td>-</td>
                            <td>记录创建时间</td>
                        </tr>
                        <tr>
                            <td>updated_at</td>
                            <td><span class="data-type">timestamp</span></td>
                            <td>-</td>
                            <td><span class="required">否</span></td>
                            <td>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</td>
                            <td>-</td>
                            <td>记录更新时间</td>
                        </tr>
                        <tr>
                            <td>customer_name</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>客户名称</td>
                        </tr>
                        <tr>
                            <td>status</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>50</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>状态</td>
                        </tr>
                        <tr>
                            <td>del_flag</td>
                            <td><span class="data-type">char</span></td>
                            <td>1</td>
                            <td>是</td>
                            <td>'0'</td>
                            <td>-</td>
                            <td>删除标志（0代表存在 2代表删除）</td>
                        </tr>
                        <tr>
                            <td>create_by</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>创建人</td>
                        </tr>
                        <tr>
                            <td>create_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>创建时间</td>
                        </tr>
                        <tr>
                            <td>update_by</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>更新人</td>
                        </tr>
                        <tr>
                            <td>update_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>更新时间</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- crm_business_contacts -->
            <div class="table-section" id="crm_business_contacts">
                <div class="table-header">
                    <div class="table-name">crm_business_contacts</div>
                    <div class="table-desc">联系人表，存储客户联系人信息</div>
                    <div class="table-info">
                        <span>引擎：MyISAM</span>
                        <span>字符集：utf8</span>
                        <span>类型：CRM业务表</span>
                    </div>
                </div>
                <table class="field-table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>数据类型</th>
                            <th>长度</th>
                            <th>允许空</th>
                            <th>默认值</th>
                            <th>主键</th>
                            <th>字段说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td><span class="data-type">int</span></td>
                            <td>11</td>
                            <td><span class="required">否</span></td>
                            <td>AUTO_INCREMENT</td>
                            <td>✓</td>
                            <td>主键，自增字段</td>
                        </tr>
                        <tr>
                            <td>responsible_person_id</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>-</td>
                            <td>负责人ID</td>
                        </tr>
                        <tr>
                            <td>name</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>-</td>
                            <td>联系人姓名</td>
                        </tr>
                        <tr>
                            <td>mobile</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>20</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>手机号码</td>
                        </tr>
                        <tr>
                            <td>phone</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>20</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>电话号码</td>
                        </tr>
                        <tr>
                            <td>telephone</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>20</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>固定电话</td>
                        </tr>
                        <tr>
                            <td>email</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>电子邮件地址</td>
                        </tr>
                        <tr>
                            <td>position</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>职务</td>
                        </tr>
                        <tr>
                            <td>department</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>100</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>部门</td>
                        </tr>
                        <tr>
                            <td>decision_role</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>50</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>决策角色(决策者/影响者/使用者/其他)</td>
                        </tr>
                        <tr>
                            <td>contact_level</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>10</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>联系人级别(A/B/C/D)</td>
                        </tr>
                        <tr>
                            <td>is_key_decision_maker</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>50</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>是否关键决策人</td>
                        </tr>
                        <tr>
                            <td>direct_superior</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>直属上级</td>
                        </tr>
                        <tr>
                            <td>address</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>地址</td>
                        </tr>
                        <tr>
                            <td>detailed_address</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>详细地址</td>
                        </tr>
                        <tr>
                            <td>next_contact_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>下次联系的时间</td>
                        </tr>
                        <tr>
                            <td>selected_date</td>
                            <td><span class="data-type">date</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>选择日期</td>
                        </tr>
                        <tr>
                            <td>gender</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>50</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>性别</td>
                        </tr>
                        <tr>
                            <td>birthday</td>
                            <td><span class="data-type">date</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>生日</td>
                        </tr>
                        <tr>
                            <td>status</td>
                            <td><span class="data-type">char</span></td>
                            <td>1</td>
                            <td>是</td>
                            <td>'0'</td>
                            <td>-</td>
                            <td>状态(0有效 1无效)</td>
                        </tr>
                        <tr>
                            <td>remarks</td>
                            <td><span class="data-type">text</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>备注信息</td>
                        </tr>
                        <tr>
                            <td>del_flag</td>
                            <td><span class="data-type">char</span></td>
                            <td>1</td>
                            <td>是</td>
                            <td>'0'</td>
                            <td>-</td>
                            <td>删除标志（0代表存在 2代表删除）</td>
                        </tr>
                        <tr>
                            <td>create_by</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>创建人</td>
                        </tr>
                        <tr>
                            <td>create_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>创建时间</td>
                        </tr>
                        <tr>
                            <td>update_by</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>更新人</td>
                        </tr>
                        <tr>
                            <td>update_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>更新时间</td>
                        </tr>
                        <tr>
                            <td>customer_name_backup</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>255</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>客户名称备份</td>
                        </tr>
                    </tbody>
                </table>
                <div class="index-info">
                    <h5>索引信息</h5>
                    <div class="index-list">
                        <span class="index-item">idx_responsible_person_del (responsible_person_id, del_flag)</span>
                        <span class="index-item">idx_name_mobile (name, mobile)</span>
                        <span class="index-item">idx_create_time_desc (create_time)</span>
                        <span class="index-item">idx_telephone (telephone)</span>
                        <span class="index-item">idx_department (department)</span>
                        <span class="index-item">idx_decision_role (decision_role)</span>
                        <span class="index-item">idx_contact_level (contact_level)</span>
                        <span class="index-item">idx_status (status)</span>
                        <span class="index-item">idx_birthday (birthday)</span>
                    </div>
                </div>
            </div>

            <!-- crm_visit_plans -->
            <div class="table-section" id="crm_visit_plans">
                <div class="table-header">
                    <div class="table-name">crm_visit_plans</div>
                    <div class="table-desc">拜访计划表，存储客户拜访计划信息</div>
                    <div class="table-info">
                        <span>引擎：InnoDB</span>
                        <span>字符集：utf8mb4</span>
                        <span>类型：CRM业务表</span>
                    </div>
                </div>
                <table class="field-table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>数据类型</th>
                            <th>长度</th>
                            <th>允许空</th>
                            <th>默认值</th>
                            <th>主键</th>
                            <th>字段说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td><span class="data-type">bigint</span></td>
                            <td>20</td>
                            <td><span class="required">否</span></td>
                            <td>AUTO_INCREMENT</td>
                            <td>✓</td>
                            <td>主键ID</td>
                        </tr>
                        <tr>
                            <td>visit_plan_name</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>200</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>-</td>
                            <td>拜访计划名称</td>
                        </tr>
                        <tr>
                            <td>visit_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>-</td>
                            <td>预计拜访时间</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>customer_id</td>
                            <td><span class="data-type">bigint</span></td>
                            <td>20</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>-</td>
                            <td>客户ID</td>
                        </tr>
                        <tr>
                            <td>customer_name</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>200</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>客户名称（冗余）</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>contact_id</td>
                            <td><span class="data-type">bigint</span></td>
                            <td>20</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>联系人ID</td>
                        </tr>
                        <tr>
                            <td>contact_name</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>100</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>联系人姓名（冗余）</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>opportunity_id</td>
                            <td><span class="data-type">bigint</span></td>
                            <td>20</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>商机ID</td>
                        </tr>
                        <tr>
                            <td>opportunity_name</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>200</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>商机名称（冗余）</td>
                        </tr>
                        <tr>
                            <td>visit_purpose</td>
                            <td><span class="data-type">text</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>拜访目的</td>
                        </tr>
                        <tr>
                            <td>remind_time</td>
                            <td><span class="data-type">int</span></td>
                            <td>11</td>
                            <td>是</td>
                            <td>30</td>
                            <td>-</td>
                            <td>提前提醒时间（分钟）</td>
                        </tr>
                        <tr>
                            <td>remark</td>
                            <td><span class="data-type">text</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>备注</td>
                        </tr>
                        <tr>
                            <td>postpone_reason</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>500</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>延期原因</td>
                        </tr>
                        <tr>
                            <td>postpone_remark</td>
                            <td><span class="data-type">text</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>延期备注</td>
                        </tr>
                        <tr>
                            <td>cancel_reason</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>500</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>取消原因</td>
                        </tr>
                        <tr>
                            <td>cancel_remark</td>
                            <td><span class="data-type">text</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>取消备注</td>
                        </tr>
                        <tr>
                            <td>followup_content</td>
                            <td><span class="data-type">text</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>跟进记录内容</td>
                        </tr>
                        <tr>
                            <td>owner_id</td>
                            <td><span class="data-type">bigint</span></td>
                            <td>20</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>-</td>
                            <td>负责人ID</td>
                        </tr>
                        <tr>
                            <td>owner_name</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>100</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>负责人姓名（冗余）</td>
                        </tr>
                        <tr>
                            <td>status</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>20</td>
                            <td><span class="required">否</span></td>
                            <td>'planned'</td>
                            <td>-</td>
                            <td>状态：planned-计划中,ongoing-进行中,completed-已完成,postponed-已延期,cancelled-已取消</td>
                        </tr>
                        <tr>
                            <td>dept_id</td>
                            <td><span class="data-type">bigint</span></td>
                            <td>20</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>所属部门ID</td>
                        </tr>
                        <tr>
                            <td>dept_name</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>100</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>部门名称（冗余）</td>
                        </tr>
                        <tr>
                            <td>actual_visit_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>实际拜访时间</td>
                        </tr>
                        <tr>
                            <td>complete_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>完成时间</td>
                        </tr>
                        <tr>
                            <td>del_flag</td>
                            <td><span class="data-type">char</span></td>
                            <td>1</td>
                            <td>是</td>
                            <td>'0'</td>
                            <td>-</td>
                            <td>删除标志（0代表存在 2代表删除）</td>
                        </tr>
                        <tr>
                            <td>create_by</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>''</td>
                            <td>-</td>
                            <td>创建者</td>
                        </tr>
                        <tr>
                            <td>create_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>CURRENT_TIMESTAMP</td>
                            <td>-</td>
                            <td>创建时间</td>
                        </tr>
                        <tr>
                            <td>update_by</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>''</td>
                            <td>-</td>
                            <td>更新者</td>
                        </tr>
                        <tr>
                            <td>update_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</td>
                            <td>-</td>
                            <td>更新时间</td>
                        </tr>
                    </tbody>
                </table>
                <div class="index-info">
                    <h5>索引信息</h5>
                    <div class="index-list">
                        <span class="index-item">idx_visit_time (visit_time)</span>
                        <span class="index-item">idx_customer_id (customer_id)</span>
                        <span class="index-item">idx_contact_id (contact_id)</span>
                        <span class="index-item">idx_opportunity_id (opportunity_id)</span>
                        <span class="index-item">idx_owner_id (owner_id)</span>
                        <span class="index-item">idx_status (status)</span>
                        <span class="index-item">idx_create_time (create_time)</span>
                    </div>
                </div>
            </div>

            <!-- crm_contact_team_members -->
            <div class="table-section" id="crm_contact_team_members">
                <div class="table-header">
                    <div class="table-name">crm_contact_team_members</div>
                    <div class="table-desc">联系人团队成员关系表，管理联系人的团队权限</div>
                    <div class="table-info">
                        <span>引擎：InnoDB</span>
                        <span>字符集：utf8mb4</span>
                        <span>类型：CRM业务表</span>
                    </div>
                </div>
                <table class="field-table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>数据类型</th>
                            <th>长度</th>
                            <th>允许空</th>
                            <th>默认值</th>
                            <th>主键</th>
                            <th>字段说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td><span class="data-type">bigint</span></td>
                            <td>20</td>
                            <td><span class="required">否</span></td>
                            <td>AUTO_INCREMENT</td>
                            <td>✓</td>
                            <td>主键ID</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>contact_id</td>
                            <td><span class="data-type">bigint</span></td>
                            <td>20</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>-</td>
                            <td>联系人ID，关联crm_contacts表</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>user_id</td>
                            <td><span class="data-type">bigint</span></td>
                            <td>20</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>-</td>
                            <td>用户ID，关联sys_user表</td>
                        </tr>
                        <tr>
                            <td>role_type</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>20</td>
                            <td><span class="required">否</span></td>
                            <td>'member'</td>
                            <td>-</td>
                            <td>角色类型：owner-负责人，admin-管理员，member-成员</td>
                        </tr>
                        <tr>
                            <td>permissions</td>
                            <td><span class="data-type">json</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>权限列表：["view","edit","delete","assign"]</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>assigned_by</td>
                            <td><span class="data-type">bigint</span></td>
                            <td>20</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>分配人ID</td>
                        </tr>
                        <tr>
                            <td>assigned_at</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>CURRENT_TIMESTAMP</td>
                            <td>-</td>
                            <td>分配时间</td>
                        </tr>
                        <tr>
                            <td>status</td>
                            <td><span class="data-type">char</span></td>
                            <td>1</td>
                            <td>是</td>
                            <td>'0'</td>
                            <td>-</td>
                            <td>状态：0-正常，1-停用</td>
                        </tr>
                        <tr>
                            <td>create_by</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>''</td>
                            <td>-</td>
                            <td>创建者</td>
                        </tr>
                        <tr>
                            <td>create_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>CURRENT_TIMESTAMP</td>
                            <td>-</td>
                            <td>创建时间</td>
                        </tr>
                        <tr>
                            <td>update_by</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>''</td>
                            <td>-</td>
                            <td>更新者</td>
                        </tr>
                        <tr>
                            <td>update_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</td>
                            <td>-</td>
                            <td>更新时间</td>
                        </tr>
                        <tr>
                            <td>remark</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>500</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>备注</td>
                        </tr>
                    </tbody>
                </table>
                <div class="index-info">
                    <h5>索引信息</h5>
                    <div class="index-list">
                        <span class="index-item">idx_contact_id (contact_id)</span>
                        <span class="index-item">idx_user_id (user_id)</span>
                        <span class="index-item">idx_role_type (role_type)</span>
                        <span class="index-item">idx_status (status)</span>
                        <span class="index-item">uk_contact_user (contact_id, user_id) - 唯一索引</span>
                    </div>
                </div>
            </div>
            
        </div>

        <!-- 系统管理表 -->
        <div class="section" id="system-management">
            <h2 class="section-title">⚙️ 系统管理表</h2>
            <p>系统核心管理功能相关的数据表，包括用户、角色、权限、部门等基础管理数据。</p>

            <!-- sys_user -->
            <div class="table-section" id="sys_user">
                <div class="table-header">
                    <div class="table-name">sys_user</div>
                    <div class="table-desc">用户信息表，存储系统用户基本信息</div>
                    <div class="table-info">
                        <span>引擎：InnoDB</span>
                        <span>字符集：utf8</span>
                        <span>类型：系统表</span>
                    </div>
                </div>
                <table class="field-table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>数据类型</th>
                            <th>长度</th>
                            <th>允许空</th>
                            <th>默认值</th>
                            <th>主键</th>
                            <th>字段说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>user_id</td>
                            <td><span class="data-type">bigint</span></td>
                            <td>20</td>
                            <td><span class="required">否</span></td>
                            <td>AUTO_INCREMENT</td>
                            <td>✓</td>
                            <td>用户ID</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>dept_id</td>
                            <td><span class="data-type">bigint</span></td>
                            <td>20</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>部门ID</td>
                        </tr>
                        <tr>
                            <td>user_name</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>30</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>-</td>
                            <td>用户账号</td>
                        </tr>
                        <tr>
                            <td>nick_name</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>30</td>
                            <td><span class="required">否</span></td>
                            <td>-</td>
                            <td>-</td>
                            <td>用户昵称</td>
                        </tr>
                        <tr>
                            <td>user_type</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>2</td>
                            <td>是</td>
                            <td>'00'</td>
                            <td>-</td>
                            <td>用户类型（00系统用户）</td>
                        </tr>
                        <tr>
                            <td>email</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>50</td>
                            <td>是</td>
                            <td>''</td>
                            <td>-</td>
                            <td>用户邮箱</td>
                        </tr>
                        <tr>
                            <td>phonenumber</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>11</td>
                            <td>是</td>
                            <td>''</td>
                            <td>-</td>
                            <td>手机号码</td>
                        </tr>
                        <tr>
                            <td>sex</td>
                            <td><span class="data-type">char</span></td>
                            <td>1</td>
                            <td>是</td>
                            <td>'0'</td>
                            <td>-</td>
                            <td>用户性别（0男 1女 2未知）</td>
                        </tr>
                        <tr>
                            <td>avatar</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>100</td>
                            <td>是</td>
                            <td>''</td>
                            <td>-</td>
                            <td>头像地址</td>
                        </tr>
                        <tr>
                            <td>password</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>100</td>
                            <td>是</td>
                            <td>''</td>
                            <td>-</td>
                            <td>密码</td>
                        </tr>
                        <tr>
                            <td>status</td>
                            <td><span class="data-type">char</span></td>
                            <td>1</td>
                            <td>是</td>
                            <td>'0'</td>
                            <td>-</td>
                            <td>帐号状态（0正常 1停用）</td>
                        </tr>
                        <tr>
                            <td>del_flag</td>
                            <td><span class="data-type">char</span></td>
                            <td>1</td>
                            <td>是</td>
                            <td>'0'</td>
                            <td>-</td>
                            <td>删除标志（0代表存在 2代表删除）</td>
                        </tr>
                        <tr>
                            <td>login_ip</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>128</td>
                            <td>是</td>
                            <td>''</td>
                            <td>-</td>
                            <td>最后登录IP</td>
                        </tr>
                        <tr>
                            <td>login_date</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>最后登录时间</td>
                        </tr>
                        <tr>
                            <td>create_by</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>''</td>
                            <td>-</td>
                            <td>创建者</td>
                        </tr>
                        <tr>
                            <td>create_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>创建时间</td>
                        </tr>
                        <tr>
                            <td>update_by</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>64</td>
                            <td>是</td>
                            <td>''</td>
                            <td>-</td>
                            <td>更新者</td>
                        </tr>
                        <tr>
                            <td>update_time</td>
                            <td><span class="data-type">datetime</span></td>
                            <td>-</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>更新时间</td>
                        </tr>
                        <tr>
                            <td>remark</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>500</td>
                            <td>是</td>
                            <td>NULL</td>
                            <td>-</td>
                            <td>备注</td>
                        </tr>
                    </tbody>
                </table>
            </div>

        </div>

        <!-- 数据库视图 -->
        <div class="section" id="database-views">
            <h2 class="section-title">📊 数据库视图</h2>
            <p>系统中的数据库视图，提供复杂查询的简化访问和数据统计功能。</p>

            <!-- v_customer_contacts -->
            <div class="table-section" id="v_customer_contacts">
                <div class="table-header">
                    <div class="table-name">v_customer_contacts</div>
                    <div class="table-desc">客户联系人视图，整合客户和联系人关系信息</div>
                    <div class="table-info">
                        <span>类型：VIEW</span>
                        <span>基于表：crm_business_customers, crm_customer_contact_relations, crm_business_contacts</span>
                    </div>
                </div>
                <table class="field-table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>数据类型</th>
                            <th>来源表.字段</th>
                            <th>字段说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>customer_id</td>
                            <td><span class="data-type">int</span></td>
                            <td>c.id</td>
                            <td>客户ID</td>
                        </tr>
                        <tr>
                            <td>customer_name</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>c.customer_name</td>
                            <td>客户名称</td>
                        </tr>
                        <tr>
                            <td>contact_id</td>
                            <td><span class="data-type">int</span></td>
                            <td>ct.id</td>
                            <td>联系人ID</td>
                        </tr>
                        <tr>
                            <td>contact_name</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>ct.name</td>
                            <td>联系人姓名</td>
                        </tr>
                        <tr>
                            <td>mobile</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>ct.mobile</td>
                            <td>手机号码</td>
                        </tr>
                        <tr>
                            <td>email</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>ct.email</td>
                            <td>电子邮件</td>
                        </tr>
                        <tr>
                            <td>position</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>ct.position</td>
                            <td>职务</td>
                        </tr>
                        <tr>
                            <td>relation_type</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>r.relation_type</td>
                            <td>关系类型</td>
                        </tr>
                        <tr>
                            <td>is_primary</td>
                            <td><span class="data-type">tinyint</span></td>
                            <td>r.is_primary</td>
                            <td>是否主要联系人</td>
                        </tr>
                        <tr>
                            <td>relation_status</td>
                            <td><span class="data-type">varchar</span></td>
                            <td>r.status</td>
                            <td>关系状态</td>
                        </tr>
                        <tr>
                            <td>start_date</td>
                            <td><span class="data-type">date</span></td>
                            <td>r.start_date</td>
                            <td>关系开始日期</td>
                        </tr>
                        <tr>
                            <td>end_date</td>
                            <td><span class="data-type">date</span></td>
                            <td>r.end_date</td>
                            <td>关系结束日期</td>
                        </tr>
                    </tbody>
                </table>
            </div>

        </div>

        <button class="back-to-top" onclick="scrollToTop()">↑</button>
    </div>

    <script>
        // 设置更新时间
        document.getElementById('update-time').textContent = new Date().toLocaleString('zh-CN');

        // 返回顶部功能
        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 显示/隐藏返回顶部按钮
        window.addEventListener('scroll', function() {
            const backToTop = document.querySelector('.back-to-top');
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'block';
            } else {
                backToTop.style.display = 'none';
            }
        });

        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
