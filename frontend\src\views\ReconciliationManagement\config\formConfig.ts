import { reactive } from 'vue';

export const formConfig = {
  formItems: [
    {
      prop: 'customerId',
      label: '选择客户',
      type: 'select-customer', // 自定义客户选择组件
      placeholder: '请选择客户',
      required: true,
    },
    {
      prop: 'reconciliationDate',
      label: '对账日期',
      type: 'date',
      placeholder: '请选择对账日期',
      required: true,
    },
    {
      prop: 'reconciliationPeriod',
      label: '对账周期',
      type: 'text',
      placeholder: '例如：2025年6月',
    },
    {
      prop: 'remark',
      label: '备注',
      type: 'textarea',
      placeholder: '请输入备注信息',
    },
  ],
  rules: reactive({
    customerId: [{ required: true, message: '客户不能为空', trigger: 'change' }],
    reconciliationDate: [{ required: true, message: '对账日期不能为空', trigger: 'change' }],
  }),
};
