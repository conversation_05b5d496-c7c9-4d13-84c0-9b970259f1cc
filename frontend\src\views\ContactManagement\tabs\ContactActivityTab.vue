<template>
    <div class="contact-activity-tab">
        <!-- 添加跟进记录区域 -->
        <div class="add-activity-section">
            <el-card class="activity-form-card">
                <template #header>
                    <div class="card-header" @click="toggleFormExpanded" style="cursor: pointer;">
                        <el-icon class="header-icon"><Plus /></el-icon>
                        <span>添加跟进记录</span>
                        <el-icon class="expand-icon" :class="{ 'expanded': isFormExpanded }">
                            <ArrowDown />
                        </el-icon>
                    </div>
                </template>
                
                <el-collapse-transition>
                    <div v-show="isFormExpanded">
                        <el-form :model="newActivity" label-width="100px" size="default">
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="活动类型" required>
                                <el-select v-model="newActivity.activityType" placeholder="请选择活动类型" style="width: 100%">
                                    <el-option 
                                        v-for="option in activityTypeOptions" 
                                        :key="option.value" 
                                        :label="option.label" 
                                        :value="option.value" 
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="联系时间" required>
                                <el-date-picker 
                                    v-model="newActivity.activityTime" 
                                    type="datetime" 
                                    placeholder="选择联系时间"
                                    style="width: 100%"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <el-form-item label="活动内容" required>
                        <el-input 
                            v-model="newActivity.content" 
                            type="textarea" 
                            :rows="4" 
                            placeholder="请详细描述本次跟进的内容、客户反馈和下步计划..."
                            maxlength="500"
                            show-word-limit
                        />
                    </el-form-item>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="下次联系时间">
                                <el-date-picker 
                                    v-model="newActivity.nextFollowTime" 
                                    type="datetime" 
                                    placeholder="选择下次联系时间"
                                    style="width: 100%"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="联系结果">
                                <el-select v-model="newActivity.result" placeholder="请选择联系结果" style="width: 100%">
                                    <el-option label="感兴趣" value="interested" />
                                    <el-option label="需要考虑" value="considering" />
                                    <el-option label="暂无需求" value="no_need" />
                                    <el-option label="有意向" value="interested_deep" />
                                    <el-option label="拒绝" value="refused" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <div class="form-actions">
                        <el-button @click="resetForm">清空</el-button>
                        <el-button type="primary" @click="addActivity" :loading="addingActivity">
                            <el-icon><Plus /></el-icon>
                            {{ editingActivityId ? '保存修改' : '添加记录' }}
                        </el-button>
                        </div>
                    </el-form>
                </div>
                </el-collapse-transition>
            </el-card>
        </div>

        <!-- 活动记录时间线 -->
        <div class="activity-timeline-section">
            <el-card class="timeline-card">
                <template #header>
                    <div class="card-header">
                        <el-icon class="header-icon"><Clock /></el-icon>
                        <span>跟进记录 ({{ activities.length }})</span>
                        <div class="header-actions">
                            <el-button size="small" @click="refreshActivities">
                                <el-icon><Refresh /></el-icon>
                                刷新
                            </el-button>
                        </div>
                    </div>
                </template>
                
                <div class="timeline-container" v-loading="loadingActivities">
                    <div v-if="activities.length === 0" class="empty-state">
                        <el-empty description="暂无跟进记录" />
                    </div>
                    
                    <el-timeline v-else>
                        <el-timeline-item
                            v-for="(activity, index) in activities"
                            :key="activity.id || index"
                            :timestamp="formatDate(activity.activityTime)"
                            placement="top"
                            :color="getTimelineColor(activity.activityType)"
                            :icon="getTimelineIcon(activity.activityType)"
                            size="large"
                        >
                            <el-card class="activity-item-card" :body-style="{ padding: '20px' }">
                                <div class="activity-header">
                                    <div class="activity-type">
                                        <el-tag :type="getActivityTagType(activity.activityType)" size="small">
                                            {{ getActivityTypeLabel(activity.activityType) }}
                                        </el-tag>
                                        <span class="activity-result" v-if="activity.result">
                                            · {{ getResultLabel(activity.result) }}
                                        </span>
                                    </div>
                                    <div class="activity-actions">
                                        <el-button size="small" text @click="editActivity(activity)">
                                            <el-icon><Edit /></el-icon>
                                        </el-button>
                                        <el-button size="small" text type="danger" @click="deleteActivity(activity)">
                                            <el-icon><Delete /></el-icon>
                                        </el-button>
                                    </div>
                                </div>
                                
                                <div class="activity-content">
                                    {{ activity.content }}
                                </div>
                                
                                <div class="activity-footer" v-if="activity.nextFollowTime">
                                    <div class="next-contact">
                                        <el-icon><Clock /></el-icon>
                                        <span>下次联系: {{ formatDate(activity.nextFollowTime) }}</span>
                                    </div>
                                </div>
                                
                                <div class="activity-meta">
                                    <span class="creator">{{ activity.createBy || '系统' }}</span>
                                    <span class="create-time">{{ formatDate(activity.createTime) }}</span>
                                </div>
                            </el-card>
                        </el-timeline-item>
                    </el-timeline>
                </div>
            </el-card>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { ContactActivity } from '@/types/contact';
import { ACTIVITY_TYPES } from '@/types/contact';
import { formatTime } from '@/utils/date';
import { createContactActivity, deleteContactActivity, getContactActivities, updateContactActivity } from '@/views/ContactManagement/api';
import { ContactEntity } from '@/views/ContactManagement/types';
import {
    ArrowDown,
    ChatDotRound,
    Clock,
    Delete,
    Edit,
    Location,
    Message,
    Monitor,
    More,
    Phone,
    Plus,
    Refresh
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, reactive, ref, watch } from 'vue';
defineOptions({
  name: 'ContactActivityTab'
});
interface Props {
    entityData: ContactEntity;
}

const emit = defineEmits<{
    (e: 'update:entity', value: Record<string, any>): void;
    (e: 'update-count', count: number): void;
}>();

const props = defineProps<Props>();

// 状态管理
const activities = ref<ContactActivity[]>([]);
const loadingActivities = ref(false);
const addingActivity = ref(false);
const isFormExpanded = ref(false);
const editingActivityId = ref<number | null>(null);

// 活动类型选项数组
const activityTypeOptions = Object.entries(ACTIVITY_TYPES).map(([value, label]) => ({
    value,
    label
}));

// 新建活动表单
const newActivity = reactive({
    activityType: '',
    content: '',
    activityTime: '',
    nextFollowTime: '',
    result: ''
});

// 加载活动记录
const loadActivities = async () => {
    if (!props.entityData.id) return;
    
    try {
        loadingActivities.value = true;
        const response = await getContactActivities(props.entityData.id);

        // 转换后端数据结构为前端所需格式
        activities.value = (response.data || []).map((item: any) => ({
            id: item.id,
            content: item.followUpContent,
            activityType: item.followUpMethod,  // 保留原始格式，不需要toLowerCase
            activityTime: item.createdAt,
            nextFollowTime: item.nextContactTime,
            result: item.contactQuality,
            createTime: item.createdAt,
            createBy: item.creatorId === 1 ? 'admin' : String(item.creatorId), // 临时处理，后续可能需要查询用户信息
            communicationResult: item.communicationResult
        }));
        
        // 按时间倒序排序
        activities.value.sort((a, b) => 
            new Date(b.activityTime).getTime() - new Date(a.activityTime).getTime()
        );
        
        // 通知父组件更新计数
        emit('update-count', activities.value.length);
    } catch (error) {
        console.error('加载活动记录失败:', error);
        ElMessage.error('加载活动记录失败');
        activities.value = [];
    } finally {
        loadingActivities.value = false;
    }
};

// 监听联系人变化
watch(
    () => props.entityData.id,
    (newId) => {
        if (newId) {
            loadActivities();
        }
    },
    { immediate: true }
);

// 组件挂载时加载数据
onMounted(() => {
    if (props.entityData.id) {
        loadActivities();
    }
});



// 添加或编辑活动记录
const addActivity = async () => {
    if (!newActivity.activityType || !newActivity.content || !newActivity.activityTime) {
        ElMessage.warning('请填写必填字段');
        return;
    }
    
    try {
        addingActivity.value = true;
        const activityData = {
            moduleType: 'contact',
            followUpContent: newActivity.content,
            followUpMethod: newActivity.activityType,
            nextContactTime: formatDateForBackend(newActivity.nextFollowTime),
            activityTime: formatDateForBackend(newActivity.activityTime),
            contactQuality: newActivity.result,
            communicationResult: '',
            contactId: props.entityData.id! // 修正字段名，原为 relatedContactId
        };
        
        if (editingActivityId.value) {
            // 编辑模式
            await updateContactActivity(props.entityData.id!, editingActivityId.value, activityData);
            ElMessage.success('更新跟进记录成功');
        } else {
            // 新增模式
            await createContactActivity(activityData);
            ElMessage.success('添加跟进记录成功');
        }
        
        // 重新加载活动记录
        await loadActivities();
        resetForm();
        
    } catch (error) {
        console.error('保存活动记录失败:', error);
        ElMessage.error(editingActivityId.value ? '更新跟进记录失败' : '添加跟进记录失败');
    } finally {
        addingActivity.value = false;
    }
};

// 重置表单
const resetForm = () => {
    Object.assign(newActivity, {
        activityType: '',
        content: '',
        activityTime: '',
        nextFollowTime: '',
        result: ''
    });
    editingActivityId.value = null;
};

// 切换表单展开状态
const toggleFormExpanded = () => {
    isFormExpanded.value = !isFormExpanded.value;
};

// 编辑活动记录
const editActivity = (activity: ContactActivity) => {
    // 将活动数据填充到表单中
    Object.assign(newActivity, {
        activityType: activity.activityType,
        content: activity.content,
        activityTime: activity.activityTime ? new Date(activity.activityTime) : '',
        nextFollowTime: activity.nextFollowTime ? new Date(activity.nextFollowTime) : '',
        result: activity.result
    });
    
    // 展开表单
    isFormExpanded.value = true;
    
    // 设置编辑模式
    editingActivityId.value = activity.id;
    
    ElMessage.info('已加载活动记录到表单，修改后点击保存');
};

// 删除活动记录
const deleteActivity = async (activity: ContactActivity) => {
    try {
        await ElMessageBox.confirm('确定要删除这条跟进记录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });
        
        // 调用API删除
        await deleteContactActivity(props.entityData.id!, activity.id);
        
        // 重新加载活动记录
        await loadActivities();
        ElMessage.success('删除成功');
    } catch (error: any) {
        if (error !== 'cancel') {
            console.error('删除活动记录失败:', error);
            ElMessage.error('删除失败');
        }
    }
};

// 刷新活动记录
const refreshActivities = () => {
    loadActivities();
};

// 将日期格式化为 yyyy-MM-dd HH:mm:ss
const formatDateForBackend = (date: string | Date | null | undefined): string | null => {
    if (!date) return null;
    
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 格式化日期
const formatDate = (dateStr: string | undefined) => {
    if (!dateStr) return '-';
    return formatTime(dateStr);
};

// 时间线图标映射
const getTimelineIcon = (type: string) => {
    const iconMap: Record<string, any> = {
        '电话': Phone,
        '邮件': Message,
        '会议': ChatDotRound,
        '拜访': Location,
        '演示': Monitor,
        '其他': More
    };
    return iconMap[type] || More;
};

// 获取时间线颜色
const getTimelineColor = (type: string) => {
    const colorMap: Record<string, string> = {
        '电话': '#409EFF',
        '邮件': '#67C23A',
        '会议': '#E6A23C',
        '拜访': '#F56C6C',
        '演示': '#909399',
        '其他': '#909399'
    };
    return colorMap[type] || '#909399';
};

// 获取活动类型标签类型
const getActivityTagType = (type: string): any => {
    const typeMap: Record<string, string> = {
        '电话': 'primary',
        '邮件': 'success',
        '会议': 'warning',
        '拜访': 'danger',
        '演示': 'info',
        '其他': 'info'
    };
    return typeMap[type] || 'info';
};

// 获取活动类型标签
const getActivityTypeLabel = (type: string) => {
    return ACTIVITY_TYPES[type as keyof typeof ACTIVITY_TYPES] || type;
};

// 获取结果标签
const getResultLabel = (result: string) => {
    const labelMap: Record<string, string> = {
        interested: '感兴趣',
        considering: '需要考虑',
        no_need: '暂无需求',
        interested_deep: '有意向',
        refused: '拒绝'
    };
    return labelMap[result] || result;
};
</script>

<style scoped>
.contact-activity-tab {
    padding: 20px;
}

.add-activity-section {
    margin-bottom: 24px;
}

.activity-form-card,
.timeline-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.06);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #333;
    user-select: none;
    transition: all 0.3s ease;
}

.card-header:hover {
    color: var(--el-color-primary);
}

.expand-icon {
    margin-left: auto;
    transition: transform 0.3s ease;
}

.expand-icon.expanded {
    transform: rotate(180deg);
}

.header-icon {
    font-size: 18px;
    color: var(--el-color-primary);
}

.header-actions {
    margin-left: auto;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
}

.timeline-container {
    min-height: 200px;
}

.empty-state {
    text-align: center;
    padding: 40px 0;
}

.activity-item-card {
    margin-bottom: 0;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.activity-item-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.activity-type {
    display: flex;
    align-items: center;
    gap: 8px;
}

.activity-result {
    color: #666;
    font-size: 12px;
}

.activity-actions {
    display: flex;
    gap: 4px;
}

.activity-content {
    color: #333;
    line-height: 1.6;
    margin-bottom: 12px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 6px;
    border-left: 3px solid var(--el-color-primary);
}

.activity-footer {
    margin-bottom: 12px;
}

.next-contact {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #E6A23C;
    font-size: 13px;
}

.activity-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #999;
    padding-top: 8px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
}

:deep(.el-timeline-item__timestamp) {
    font-weight: 500;
    color: #666;
}

:deep(.el-timeline-item__node) {
    border-width: 3px;
}

:deep(.el-form-item) {
    margin-bottom: 20px;
}

@media (max-width: 768px) {
    .contact-activity-tab {
        padding: 12px;
    }
    
    .activity-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .activity-actions {
        align-self: flex-end;
    }
}
</style>