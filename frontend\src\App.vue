<template>
  <el-config-provider namespace="ep">
    <BaseHeader v-if="$route.meta.showHeader !== false" />
    <div class="flex main-container" :class="{ 'no-header': $route.meta.showHeader === false }">
      <div class="content-container">
        <router-view />
      </div>
    </div>
  </el-config-provider>
</template>

<script setup>
import BaseHeader from '@/components/layouts/BaseHeader.vue';
</script>

<style lang="scss">
@use './styles/variables.scss' as vars;

#app {
  text-align: center;
  color: var(--ep-text-color-primary);
  background: vars.$gradient-background-main;
}

.main-container {
  height: calc(100vh - 60px);
  background: vars.$gradient-background-main;
}

.main-container.no-header {
  height: 100vh;
}

.content-container {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  overflow-y: auto;

  /* 隐藏滚动条 - Webkit浏览器 */
  &::-webkit-scrollbar {
    display: none;
  }

  /* 隐藏滚动条 - Firefox */
  scrollbar-width: none;

  /* 隐藏滚动条 - IE */
  -ms-overflow-style: none;
}

.ep-main{
  overflow: hidden !important;
  padding: 0 30px !important;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 30px;
    margin: 0;
    background-color: transparent;
    box-shadow: none;

    h1 {
        font-weight: 400;
        font-size: 20px;
        color: #606266;
        margin: 0;
    }
}
</style>
