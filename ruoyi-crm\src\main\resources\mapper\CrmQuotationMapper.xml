<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmQuotationMapper">
    
    <resultMap type="CrmQuotation" id="CrmQuotationResult">
        <result property="id"    column="id"    />
        <result property="quotationNo"    column="quotation_no"    />
        <result property="quotationName"    column="quotation_name"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="contactId"    column="contact_id"    />
        <result property="contactName"    column="contact_name"    />
        <result property="responsiblePersonId"    column="responsible_person_id"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="currency"    column="currency"    />
        <result property="status"    column="status"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="processInstanceId"    column="process_instance_id"    />
        <result property="processStatus"    column="process_status"    />
        <result property="currentTaskId"    column="current_task_id"    />
        <result property="currentTaskName"    column="current_task_name"    />
        <result property="currentAssignee"    column="current_assignee"    />
        <result property="processStartTime"    column="process_start_time"    />
        <result property="processEndTime"    column="process_end_time"    />
        <result property="validUntil"    column="valid_until"    />
        <result property="quotationDate"    column="quotation_date"    />
        <result property="deliveryTerms"    column="delivery_terms"    />
        <result property="paymentTerms"    column="payment_terms"    />
        <result property="remarks"    column="remarks"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="CrmQuotationCrmQuotationItemResult" type="CrmQuotation" extends="CrmQuotationResult">
        <collection property="quotationItems" javaType="java.util.List" resultMap="CrmQuotationItemResult" />
    </resultMap>

    <resultMap type="CrmQuotationItem" id="CrmQuotationItemResult">
        <result property="id"    column="sub_id"    />
        <result property="quotationId"    column="sub_quotation_id"    />
        <result property="productName"    column="sub_product_name"    />
        <result property="productCode"    column="sub_product_code"    />
        <result property="specification"    column="sub_specification"    />
        <result property="brand"    column="sub_brand"    />
        <result property="model"    column="sub_model"    />
        <result property="quantity"    column="sub_quantity"    />
        <result property="unit"    column="sub_unit"    />
        <result property="unitPrice"    column="sub_unit_price"    />
        <result property="totalPrice"    column="sub_total_price"    />
        <result property="discountRate"    column="sub_discount_rate"    />
        <result property="discountAmount"    column="sub_discount_amount"    />
        <result property="finalAmount"    column="sub_final_amount"    />
        <result property="deliveryDate"    column="sub_delivery_date"    />
        <result property="warrantyPeriod"    column="sub_warranty_period"    />
        <result property="remarks"    column="sub_remarks"    />
        <result property="sortOrder"    column="sub_sort_order"    />
    </resultMap>

    <sql id="selectCrmQuotationVo">
        select id, quotation_no, quotation_name, customer_id, customer_name, contact_id, contact_name, 
               responsible_person_id, total_amount, currency, status, approval_status, process_instance_id, 
               process_status, current_task_id, current_task_name, current_assignee, process_start_time, 
               process_end_time, valid_until, quotation_date, delivery_terms, payment_terms, remarks, 
               del_flag, create_by, create_time, update_by, update_time 
        from crm_quotations
    </sql>

    <select id="selectCrmQuotationList" parameterType="CrmQuotation" resultMap="CrmQuotationResult">
        <include refid="selectCrmQuotationVo"/>
        <where>
            del_flag = '0'
            <if test="quotationNo != null  and quotationNo != ''"> and quotation_no like concat('%', #{quotationNo}, '%')</if>
            <if test="quotationName != null  and quotationName != ''"> and quotation_name like concat('%', #{quotationName}, '%')</if>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="contactId != null "> and contact_id = #{contactId}</if>
            <if test="responsiblePersonId != null  and responsiblePersonId != ''"> and responsible_person_id = #{responsiblePersonId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="approvalStatus != null  and approvalStatus != ''"> and approval_status = #{approvalStatus}</if>
            <if test="params.beginQuotationDate != null and params.beginQuotationDate != ''"><!-- 开始时间检索 -->
                and date_format(quotation_date,'%y%m%d') &gt;= date_format(#{params.beginQuotationDate},'%y%m%d')
            </if>
            <if test="params.endQuotationDate != null and params.endQuotationDate != ''"><!-- 结束时间检索 -->
                and date_format(quotation_date,'%y%m%d') &lt;= date_format(#{params.endQuotationDate},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectCrmQuotationById" parameterType="Long" resultMap="CrmQuotationCrmQuotationItemResult">
        select a.id, a.quotation_no, a.quotation_name, a.customer_id, a.customer_name, a.contact_id, a.contact_name, 
               a.responsible_person_id, a.total_amount, a.currency, a.status, a.approval_status, a.process_instance_id, 
               a.process_status, a.current_task_id, a.current_task_name, a.current_assignee, a.process_start_time, 
               a.process_end_time, a.valid_until, a.quotation_date, a.delivery_terms, a.payment_terms, a.remarks, 
               a.del_flag, a.create_by, a.create_time, a.update_by, a.update_time,
               b.id as sub_id, b.quotation_id as sub_quotation_id, b.product_name as sub_product_name, 
               b.product_code as sub_product_code, b.specification as sub_specification, b.brand as sub_brand, 
               b.model as sub_model, b.quantity as sub_quantity, b.unit as sub_unit, b.unit_price as sub_unit_price, 
               b.total_price as sub_total_price, b.discount_rate as sub_discount_rate, b.discount_amount as sub_discount_amount, 
               b.final_amount as sub_final_amount, b.delivery_date as sub_delivery_date, b.warranty_period as sub_warranty_period, 
               b.remarks as sub_remarks, b.sort_order as sub_sort_order
        from crm_quotations a
        left join crm_quotation_items b on b.quotation_id = a.id
        where a.id = #{id} and a.del_flag = '0'
        order by b.sort_order
    </select>

    <select id="selectCrmQuotationByQuotationNo" parameterType="String" resultMap="CrmQuotationResult">
        <include refid="selectCrmQuotationVo"/>
        where quotation_no = #{quotationNo} and del_flag = '0'
    </select>

    <select id="selectCrmQuotationByProcessInstanceId" parameterType="String" resultMap="CrmQuotationResult">
        <include refid="selectCrmQuotationVo"/>
        where process_instance_id = #{processInstanceId} and del_flag = '0'
    </select>
        
    <insert id="insertCrmQuotation" parameterType="CrmQuotation" useGeneratedKeys="true" keyProperty="id">
        insert into crm_quotations
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="quotationNo != null and quotationNo != ''">quotation_no,</if>
            <if test="quotationName != null and quotationName != ''">quotation_name,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="contactId != null">contact_id,</if>
            <if test="contactName != null and contactName != ''">contact_name,</if>
            <if test="responsiblePersonId != null and responsiblePersonId != ''">responsible_person_id,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="currency != null and currency != ''">currency,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status,</if>
            <if test="processInstanceId != null">process_instance_id,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="currentTaskId != null">current_task_id,</if>
            <if test="currentTaskName != null">current_task_name,</if>
            <if test="currentAssignee != null">current_assignee,</if>
            <if test="processStartTime != null">process_start_time,</if>
            <if test="processEndTime != null">process_end_time,</if>
            <if test="validUntil != null">valid_until,</if>
            <if test="quotationDate != null">quotation_date,</if>
            <if test="deliveryTerms != null">delivery_terms,</if>
            <if test="paymentTerms != null">payment_terms,</if>
            <if test="remarks != null">remarks,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="quotationNo != null and quotationNo != ''">#{quotationNo},</if>
            <if test="quotationName != null and quotationName != ''">#{quotationName},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="contactId != null">#{contactId},</if>
            <if test="contactName != null and contactName != ''">#{contactName},</if>
            <if test="responsiblePersonId != null and responsiblePersonId != ''">#{responsiblePersonId},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="currency != null and currency != ''">#{currency},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="approvalStatus != null and approvalStatus != ''">#{approvalStatus},</if>
            <if test="processInstanceId != null">#{processInstanceId},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="currentTaskId != null">#{currentTaskId},</if>
            <if test="currentTaskName != null">#{currentTaskName},</if>
            <if test="currentAssignee != null">#{currentAssignee},</if>
            <if test="processStartTime != null">#{processStartTime},</if>
            <if test="processEndTime != null">#{processEndTime},</if>
            <if test="validUntil != null">#{validUntil},</if>
            <if test="quotationDate != null">#{quotationDate},</if>
            <if test="deliveryTerms != null">#{deliveryTerms},</if>
            <if test="paymentTerms != null">#{paymentTerms},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCrmQuotation" parameterType="CrmQuotation">
        update crm_quotations
        <trim prefix="SET" suffixOverrides=",">
            <if test="quotationNo != null and quotationNo != ''">quotation_no = #{quotationNo},</if>
            <if test="quotationName != null and quotationName != ''">quotation_name = #{quotationName},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="contactId != null">contact_id = #{contactId},</if>
            <if test="contactName != null and contactName != ''">contact_name = #{contactName},</if>
            <if test="responsiblePersonId != null and responsiblePersonId != ''">responsible_person_id = #{responsiblePersonId},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="currency != null and currency != ''">currency = #{currency},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status = #{approvalStatus},</if>
            <if test="processInstanceId != null">process_instance_id = #{processInstanceId},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="currentTaskId != null">current_task_id = #{currentTaskId},</if>
            <if test="currentTaskName != null">current_task_name = #{currentTaskName},</if>
            <if test="currentAssignee != null">current_assignee = #{currentAssignee},</if>
            <if test="processStartTime != null">process_start_time = #{processStartTime},</if>
            <if test="processEndTime != null">process_end_time = #{processEndTime},</if>
            <if test="validUntil != null">valid_until = #{validUntil},</if>
            <if test="quotationDate != null">quotation_date = #{quotationDate},</if>
            <if test="deliveryTerms != null">delivery_terms = #{deliveryTerms},</if>
            <if test="paymentTerms != null">payment_terms = #{paymentTerms},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmQuotationById" parameterType="Long">
        update crm_quotations set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteCrmQuotationByIds" parameterType="String">
        update crm_quotations set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteCrmQuotationItemByQuotationIds" parameterType="String">
        delete from crm_quotation_items where quotation_id in 
        <foreach item="quotationId" collection="array" open="(" separator="," close=")">
            #{quotationId}
        </foreach>
    </delete>

    <delete id="deleteCrmQuotationItemByQuotationId" parameterType="Long">
        delete from crm_quotation_items where quotation_id = #{quotationId}
    </delete>

    <insert id="batchCrmQuotationItem">
        insert into crm_quotation_items( id, quotation_id, product_name, product_code, specification, brand, model, quantity, unit, unit_price, total_price, discount_rate, discount_amount, final_amount, delivery_date, warranty_period, remarks, sort_order) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.quotationId}, #{item.productName}, #{item.productCode}, #{item.specification}, #{item.brand}, #{item.model}, #{item.quantity}, #{item.unit}, #{item.unitPrice}, #{item.totalPrice}, #{item.discountRate}, #{item.discountAmount}, #{item.finalAmount}, #{item.deliveryDate}, #{item.warrantyPeriod}, #{item.remarks}, #{item.sortOrder})
        </foreach>
    </insert>

    <select id="selectCrmQuotationItemList" parameterType="Long" resultMap="CrmQuotationItemResult">
        select id, quotation_id, product_name, product_code, specification, brand, model, quantity, unit, unit_price, total_price, discount_rate, discount_amount, final_amount, delivery_date, warranty_period, remarks, sort_order
        from crm_quotation_items
        where quotation_id = #{quotationId}
        order by sort_order
    </select>
</mapper>