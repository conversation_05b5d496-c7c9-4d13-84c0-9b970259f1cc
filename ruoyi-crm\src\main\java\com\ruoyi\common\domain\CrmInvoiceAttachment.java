package com.ruoyi.common.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发票附件对象 crm_invoice_attachments
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmInvoiceAttachment extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 发票ID */
    private Long invoiceId;

    /** 文件名称 */
    private String fileName;

    /** 原始文件名 */
    private String fileOriginalName;

    /** 文件存储路径 */
    private String filePath;

    /** 文件访问URL */
    private String fileUrl;

    /** 文件大小（字节） */
    private Long fileSize;

    /** 文件类型（MIME类型） */
    private String fileType;

    /** 文件扩展名 */
    private String fileExtension;

    /** 附件类型：invoice_image-发票图片,contract_scan-合同扫描件,other-其他 */
    private String attachmentType;

    /** 附件分类：正本、副本、电子版等 */
    private String attachmentCategory;

    /** 附件描述 */
    private String description;

    /** 排序 */
    private Integer sortOrder;

    /** 是否主要附件（0否 1是） */
    private String isMain;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;
}