<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CrmBusinessApApprovalProcessMapper">
    
    <resultMap type="CrmBusinessApApprovalProcess" id="CrmBusinessApApprovalProcessResult">
        <result property="approvalId"    column="approval_id"    />
        <result property="instanceNodeId"    column="instance_node_id"    />
        <result property="assignee"    column="assignee"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="status"    column="status"    />
        <result property="contentId"    column="content_id"    />
        <result property="comment"    column="comment"    />
        <result property="isProcessed"    column="is_processed"    />
        <result property="applicationId"    column="application_id"    />
        <result property="previousHistoryId"    column="previous_history_id"    />
        <result property="previousInstanceNodeId"    column="previous_instance_node_id"    />
        <result property="previousApplicant"    column="previous_applicant"    />
        <result property="previousApproverType"    column="previous_approver_type"    />
    </resultMap>

    <sql id="selectCrmBusinessApApprovalProcessVo">
        select approval_id, instance_node_id, assignee, start_time, end_time, status, content_id, comment, is_processed, application_id, previous_history_id, previous_instance_node_id, previous_applicant, previous_approver_type from crm_business_ap_approval_process
    </sql>

    <select id="selectCrmBusinessApApprovalProcessList" parameterType="CrmBusinessApApprovalProcess" resultMap="CrmBusinessApApprovalProcessResult">
        <include refid="selectCrmBusinessApApprovalProcessVo"/>
        <where>  
            <if test="instanceNodeId != null "> and instance_node_id = #{instanceNodeId}</if>
            <if test="assignee != null  and assignee != ''"> and assignee = #{assignee}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="contentId != null "> and content_id = #{contentId}</if>
            <if test="comment != null  and comment != ''"> and comment = #{comment}</if>
            <if test="isProcessed != null "> and is_processed = #{isProcessed}</if>
            <if test="applicationId != null "> and application_id = #{applicationId}</if>
            <if test="previousHistoryId != null "> and previous_history_id = #{previousHistoryId}</if>
            <if test="previousInstanceNodeId != null "> and previous_instance_node_id = #{previousInstanceNodeId}</if>
            <if test="previousApplicant != null "> and previous_applicant = #{previousApplicant}</if>
            <if test="previousApproverType != null  and previousApproverType != ''"> and previous_approver_type = #{previousApproverType}</if>
        </where>
    </select>
    
    <select id="selectCrmBusinessApApprovalProcessByApprovalId" parameterType="Long" resultMap="CrmBusinessApApprovalProcessResult">
        <include refid="selectCrmBusinessApApprovalProcessVo"/>
        where approval_id = #{approvalId}
    </select>

    <insert id="insertCrmBusinessApApprovalProcess" parameterType="CrmBusinessApApprovalProcess" useGeneratedKeys="true" keyProperty="approvalId">
        insert into crm_business_ap_approval_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="instanceNodeId != null">instance_node_id,</if>
            <if test="assignee != null">assignee,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="status != null">status,</if>
            <if test="contentId != null">content_id,</if>
            <if test="comment != null">comment,</if>
            <if test="isProcessed != null">is_processed,</if>
            <if test="applicationId != null">application_id,</if>
            <if test="previousHistoryId != null">previous_history_id,</if>
            <if test="previousInstanceNodeId != null">previous_instance_node_id,</if>
            <if test="previousApplicant != null">previous_applicant,</if>
            <if test="previousApproverType != null">previous_approver_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="instanceNodeId != null">#{instanceNodeId},</if>
            <if test="assignee != null">#{assignee},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="status != null">#{status},</if>
            <if test="contentId != null">#{contentId},</if>
            <if test="comment != null">#{comment},</if>
            <if test="isProcessed != null">#{isProcessed},</if>
            <if test="applicationId != null">#{applicationId},</if>
            <if test="previousHistoryId != null">#{previousHistoryId},</if>
            <if test="previousInstanceNodeId != null">#{previousInstanceNodeId},</if>
            <if test="previousApplicant != null">#{previousApplicant},</if>
            <if test="previousApproverType != null">#{previousApproverType},</if>
         </trim>
    </insert>

    <update id="updateCrmBusinessApApprovalProcess" parameterType="CrmBusinessApApprovalProcess">
        update crm_business_ap_approval_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="instanceNodeId != null">instance_node_id = #{instanceNodeId},</if>
            <if test="assignee != null">assignee = #{assignee},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="contentId != null">content_id = #{contentId},</if>
            <if test="comment != null">comment = #{comment},</if>
            <if test="isProcessed != null">is_processed = #{isProcessed},</if>
            <if test="applicationId != null">application_id = #{applicationId},</if>
            <if test="previousHistoryId != null">previous_history_id = #{previousHistoryId},</if>
            <if test="previousInstanceNodeId != null">previous_instance_node_id = #{previousInstanceNodeId},</if>
            <if test="previousApplicant != null">previous_applicant = #{previousApplicant},</if>
            <if test="previousApproverType != null">previous_approver_type = #{previousApproverType},</if>
        </trim>
        where approval_id = #{approvalId}
    </update>

    <delete id="deleteCrmBusinessApApprovalProcessByApprovalId" parameterType="Long">
        delete from crm_business_ap_approval_process where approval_id = #{approvalId}
    </delete>

    <delete id="deleteCrmBusinessApApprovalProcessByApprovalIds" parameterType="String">
        delete from crm_business_ap_approval_process where approval_id in 
        <foreach item="approvalId" collection="array" open="(" separator="," close=")">
            #{approvalId}
        </foreach>
    </delete>
</mapper>