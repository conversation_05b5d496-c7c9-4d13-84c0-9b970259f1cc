import { createPinia } from "pinia";
import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";

// Element Plus 相关导入
import * as Icons from '@element-plus/icons-vue';
import ElementPlus from 'element-plus';

// 组件注册中心
import { createComponentRegistry } from '~/components/registry';

// 样式导入 - 只加载最基础的
import "element-plus/dist/index.css";

import './permission'; // 引入路由守卫

const app = createApp(App);
const pinia = createPinia();

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(Icons)) {
    app.component(key, component);
}

// 使用插件
app.use(ElementPlus);
app.use(createComponentRegistry());
app.use(pinia);
app.use(router);

app.mount("#app"); 