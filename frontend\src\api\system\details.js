import request from '@/utils/request'

// 查询回款明细列表
export function listDetails(query) {
  return request({
    url: '/system/details/list',
    method: 'get',
    params: query
  })
}

// 查询回款明细详细
export function getDetails(id) {
  return request({
    url: '/system/details/' + id,
    method: 'get'
  })
}

// 新增回款明细
export function addDetails(data) {
  return request({
    url: '/system/details',
    method: 'post',
    data: data
  })
}

// 修改回款明细
export function updateDetails(data) {
  return request({
    url: '/system/details',
    method: 'put',
    data: data
  })
}

// 删除回款明细
export function delDetails(id) {
  return request({
    url: '/system/details/' + id,
    method: 'delete'
  })
}
