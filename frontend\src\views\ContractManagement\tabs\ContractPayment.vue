<template>
    <div class="contract-payment">
        <el-table :data="payments" border style="width: 100%">
            <el-table-column prop="payment_number" label="付款单号" />
            <el-table-column prop="payment_amount" label="付款金额" />
            <el-table-column prop="payment_date" label="付款日期" />
            <el-table-column prop="payment_method" label="付款方式" />
            <el-table-column prop="status" label="状态" />
            <el-table-column prop="remarks" label="备注" />
        </el-table>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

interface Payment {
    payment_number: string;
    payment_amount: number;
    payment_date: string;
    payment_method: string;
    status: string;
    remarks: string;
}

export default defineComponent({
    name: 'ContractPayment',
    props: {
        contract: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            payments: [] as Payment[]
        };
    },
    created() {
        // TODO: 从API获取付款记录
        this.payments = [
            {
                payment_number: 'PAY2024001',
                payment_amount: 5000,
                payment_date: '2024-11-01',
                payment_method: '银行转账',
                status: '已付款',
                remarks: '首付款'
            }
        ];
    }
});
</script>

<style scoped>
.contract-payment {
    padding: 20px;
}
</style> 