package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmBusinessApNodeDetail;

/**
 * 审批节点详情Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface CrmBusinessApNodeDetailMapper 
{
    /**
     * 查询审批节点详情
     * 
     * @param detailId 审批节点详情主键
     * @return 审批节点详情
     */
    public CrmBusinessApNodeDetail selectCrmBusinessApNodeDetailByDetailId(Long detailId);

    /**
     * 查询审批节点详情列表
     * 
     * @param crmBusinessApNodeDetail 审批节点详情
     * @return 审批节点详情集合
     */
    public List<CrmBusinessApNodeDetail> selectCrmBusinessApNodeDetailList(CrmBusinessApNodeDetail crmBusinessApNodeDetail);

    /**
     * 新增审批节点详情
     * 
     * @param crmBusinessApNodeDetail 审批节点详情
     * @return 结果
     */
    public int insertCrmBusinessApNodeDetail(CrmBusinessApNodeDetail crmBusinessApNodeDetail);

    /**
     * 修改审批节点详情
     * 
     * @param crmBusinessApNodeDetail 审批节点详情
     * @return 结果
     */
    public int updateCrmBusinessApNodeDetail(CrmBusinessApNodeDetail crmBusinessApNodeDetail);

    /**
     * 删除审批节点详情
     * 
     * @param detailId 审批节点详情主键
     * @return 结果
     */
    public int deleteCrmBusinessApNodeDetailByDetailId(Long detailId);

    /**
     * 批量删除审批节点详情
     * 
     * @param detailIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCrmBusinessApNodeDetailByDetailIds(Long[] detailIds);
}
