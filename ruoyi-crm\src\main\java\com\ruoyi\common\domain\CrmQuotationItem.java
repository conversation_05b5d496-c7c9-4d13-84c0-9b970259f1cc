package com.ruoyi.common.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 报价单明细对象 crm_quotation_items
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
public class CrmQuotationItem {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 报价单ID */
    private Long quotationId;

    /** 产品名称 */
    private String productName;

    /** 产品编码 */
    private String productCode;

    /** 规格说明 */
    private String specification;

    /** 品牌 */
    private String brand;

    /** 型号 */
    private String model;

    /** 数量 */
    private BigDecimal quantity;

    /** 单位 */
    private String unit;

    /** 单价 */
    private BigDecimal unitPrice;

    /** 小计 */
    private BigDecimal totalPrice;

    /** 折扣率 */
    private BigDecimal discountRate;

    /** 折扣金额 */
    private BigDecimal discountAmount;

    /** 最终金额 */
    private BigDecimal finalAmount;

    /** 交货日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deliveryDate;

    /** 质保期 */
    private String warrantyPeriod;

    /** 备注 */
    private String remarks;

    /** 排序 */
    private Integer sortOrder;
}