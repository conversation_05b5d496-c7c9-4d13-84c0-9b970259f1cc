# CRM对账单系统开发实施方案（Activiti流程引擎集成版）

## 📋 项目总览

基于客户需求和现有CRM系统的Activiti流程引擎集成模式，创建完整的对账单系统设计文档，包含以下核心内容：

### 📁 文档结构
```
对账单/
├── 需求分析与业务流程.md      # 详细业务需求分析和流程设计
├── 时序图设计.md              # 完整的业务时序图和交互流程  
├── 数据库设计.md              # 数据库表结构和关系设计（已更新为流程引擎集成版）
├── 接口设计文档.md            # RESTful API接口设计
├── 对账单UI设计参考.html      # UI界面设计参考
└── 开发实施方案.md            # 本文档 - 开发指导
```

## 🎯 核心功能模块

### 1. 对账单管理
- **创建对账单**：支持客户和联系人两种关联方式，基于订单选择创建，支持手动添加明细
- **预收款集成**：自动计算预收款抵扣，实时更新余额
- **Activiti审批流程**：深度集成Activiti工作流引擎，完整的审核工作流，状态流转由流程引擎驱动
- **流程状态同步**：业务状态与流程状态实时同步，确保数据一致性
- **批量操作**：支持批量选择和批量处理

### 2. 开票申请
- **申请创建**：基于对账单创建开票申请，支持联系人和客户关联
- **合并开票**：支持多个对账单合并开票
- **Activiti财务审批**：基于Activiti工作流引擎的财务审批和开票流程
- **发票管理**：发票信息管理和状态跟踪
- **流程监控**：实时监控审批进度和任务分配

### 3. 回款管理  
- **单笔回款**：针对单个订单或对账单回款，支持独立回款（不关联对账单）
- **批量回款**：多个对账单合并回款处理
- **Activiti审批**：基于金额的分级审批流程
- **金额分配**：自动按比例分配回款金额
- **回款统计**：回款数据统计和分析

### 4. 流程管理（新增）
- **任务待办**：统一的待办任务中心，支持任务提醒
- **流程监控**：实时查看流程执行状态和进度
- **操作历史**：完整的流程操作历史记录
- **流程统计**：审批效率和通过率统计

## 🚀 技术实现方案

### 后端技术栈
- **框架**：Spring Boot 2.7+
- **ORM**：MyBatis Plus 3.5+
- **数据库**：MySQL 8.0+
- **缓存**：Redis 6.0+
- **安全**：Spring Security + JWT
- **工作流引擎**：Activiti 7（核心）
- **文档**：Swagger 3.0
- **流程设计器**：Activiti Designer

### 前端技术栈
- **框架**：Vue 3 + TypeScript
- **UI库**：Element Plus
- **状态管理**：Pinia
- **路由**：Vue Router 4
- **HTTP**：Axios
- **构建工具**：Vite
- **流程图渲染**：bpmn-js（用于流程图展示）

### 架构分层遵循CRM412规范
```
ruoyi-crm/
├── src/main/java/com/ruoyi/common/     # CRM模块通用组件
│   ├── domain/entity/                  # 实体类（增加流程字段）
│   ├── mapper/                         # Mapper接口
│   └── service/                        # 通用Service接口
├── src/main/java/com/ruoyi/crm/        # CRM业务逻辑
│   ├── controller/                     # 控制器层
│   ├── service/                        # 业务实现层
│   │   ├── impl/                       # Service实现
│   │   └── workflow/                   # 流程服务层（新增）
│   └── common/                         # 业务通用组件
└── src/main/java/com/ruoyi/activiti/   # Activiti集成层
    ├── service/                        # 流程服务
    ├── listener/                       # 流程监听器
    ├── delegate/                       # 服务任务委托
    └── config/                         # 流程配置
```

## 📅 开发时间表

### 阶段一：环境准备与流程配置 (3天)
**时间：7月3日-7月5日**
- [ ] **创建数据库表结构（流程引擎集成版）**
- [ ] **配置Activiti流程引擎**
- [ ] **部署对账单、开票、回款流程定义**
- [ ] **创建流程监听器和服务委托类**
- [ ] 生成基础代码框架
- [ ] 配置开发环境

### 阶段二：流程服务层开发 (5天)
**时间：7月6日-7月10日**

#### Day 1-2: 流程基础服务
- [ ] **Activiti服务封装（RuntimeService、TaskService等）**
- [ ] **流程启动和任务处理通用服务**
- [ ] **流程状态同步监听器开发**
- [ ] **流程历史记录服务**
- [ ] **任务待办统一管理服务**

#### Day 3-5: 业务流程集成
- [ ] **对账单审批流程服务集成**
- [ ] **开票申请审批流程服务集成**
- [ ] **回款审批流程服务集成**
- [ ] **流程变量管理和传递**
- [ ] **业务状态与流程状态同步机制**

### 阶段三：核心业务功能开发 (6天)
**时间：7月11日-7月16日**

#### Day 1-2: 对账单业务功能
- [ ] 对账单CRUD操作（集成流程字段）
- [ ] **支持客户和联系人两种关联方式**
- [ ] 订单选择和关联
- [ ] 手动明细添加
- [ ] 金额计算逻辑

#### Day 3-4: 审批和预收款
- [ ] **对账单提交审批接口（启动Activiti流程）**
- [ ] **流程任务处理接口（审批、驳回、取消）**
- [ ] 预收款集成
- [ ] **流程状态查询和监控**
- [ ] 业务日志记录

#### Day 5-6: 开票和回款
- [ ] **开票申请功能（集成Activiti审批）**
- [ ] **回款记录管理（集成Activiti审批）**
- [ ] **支持独立回款（不关联对账单）**
- [ ] 金额分配算法

### 阶段四：前端界面开发 (5天)
**时间：7月17日-7月21日**

#### Day 1-2: 对账单界面
- [ ] 对账单列表页面（增加流程状态显示）
- [ ] 创建对账单表单（支持关联类型选择）
- [ ] 详情页面展示（包含流程进度）
- [ ] **流程审批任务列表与处理界面**

#### Day 3-4: 流程管理界面
- [ ] **统一待办任务中心**
- [ ] **流程监控和进度查看**
- [ ] **流程历史操作记录**
- [ ] **流程图可视化展示**

#### Day 5: 开票回款界面
- [ ] 开票申请页面（集成流程状态）
- [ ] 回款管理界面（支持独立回款）
- [ ] 统计报表页面

### 阶段五：测试优化 (3天)
**时间：7月22日-7月24日**
- [ ] 单元测试编写
- [ ] **Activiti流程测试（重点）**
- [ ] **流程状态同步测试**
- [ ] **任务分配和权限测试**
- [ ] 集成测试
- [ ] 性能优化
- [ ] Bug修复

## 🔧 关键代码实现

### 1. 实体类设计（遵循CRM412规范，集成流程字段）

#### 对账单主表实体
```java
// 路径: ruoyi-crm/src/main/java/com/ruoyi/common/domain/entity/
@Data
@TableName("crm_reconciliation")
public class CrmReconciliation extends BaseEntity {
    @TableId(value = "reconciliation_id", type = IdType.AUTO)
    private Long reconciliationId;
    
    private String reconciliationNo;
    
    // === 关联关系优化 ===
    private String relationType; // customer-客户, contact-联系人
    private Long customerId;
    private String customerName;
    private Long contactId;
    private String contactName;
    private Long contactCustomerId;
    private String contactCustomerName;
    
    private String reconciliationPeriod;
    
    @TableField(typeHandler = BigDecimalTypeHandler.class)
    private BigDecimal totalAmount;
    
    @TableField(typeHandler = BigDecimalTypeHandler.class)
    private BigDecimal prepaymentAmount;
    
    @TableField(typeHandler = BigDecimalTypeHandler.class)
    private BigDecimal netAmount;
    
    // === 业务状态 ===
    private String status; // draft, submitted, approved, rejected, invoiced, paid, completed, cancelled
    
    // === Activiti流程字段 ===
    private String processInstanceId;
    private String processDefinitionKey;
    private String processStatus; // not_started, running, suspended, completed, terminated
    private String currentTaskId;
    private String currentTaskName;
    private String currentAssignee;
    private LocalDateTime processStartTime;
    private LocalDateTime processEndTime;
    
    // === 审批相关（兼容字段） ===
    private String approvalStatus;
    private String approvalNotes;
    private Long approvedBy;
    private LocalDateTime approvedTime;
    
    private Long responsibleUserId;
    private String responsibleUserName;
    private Long teamId;
    private String teamName;
    
    // 非数据库字段
    @TableField(exist = false)
    private List<CrmReconciliationDetail> details;
    
    @TableField(exist = false)
    private List<String> relatedOrders;
    
    @TableField(exist = false)
    private String processInstanceStatus; // 流程实例状态描述
}
```

### 2. 流程服务接口设计

#### 对账单流程服务
```java
// 路径: ruoyi-crm/src/main/java/com/ruoyi/crm/service/workflow/
public interface IReconciliationWorkflowService {
    
    /**
     * 启动对账单审批流程
     * @param reconciliationId 对账单ID
     * @return 流程实例ID
     */
    String startApprovalProcess(Long reconciliationId);
    
    /**
     * 处理审批任务
     * @param taskId 任务ID
     * @param approved 是否通过
     * @param comments 审批意见
     * @return 处理结果
     */
    boolean processApprovalTask(String taskId, boolean approved, String comments);
    
    /**
     * 查询待办任务
     * @param assignee 处理人
     * @return 待办任务列表
     */
    List<TaskInfo> getPendingTasks(String assignee);
    
    /**
     * 获取流程历史
     * @param reconciliationId 对账单ID
     * @return 流程历史
     */
    List<ProcessHistoryInfo> getProcessHistory(Long reconciliationId);
    
    /**
     * 撤销流程
     * @param reconciliationId 对账单ID
     * @param reason 撤销原因
     * @return 是否成功
     */
    boolean cancelProcess(Long reconciliationId, String reason);
}
```

### 3. 流程服务实现（参考LeaveApplicationServiceImpl）

```java
// 路径: ruoyi-crm/src/main/java/com/ruoyi/crm/service/workflow/impl/
@Service
@Transactional
public class ReconciliationWorkflowServiceImpl implements IReconciliationWorkflowService {
    
    @Autowired
    private RuntimeService runtimeService;
    
    @Autowired
    private TaskService taskService;
    
    @Autowired
    private HistoryService historyService;
    
    @Autowired
    private ICrmReconciliationService reconciliationService;
    
    /**
     * 启动对账单审批流程
     */
    @Override
    public String startApprovalProcess(Long reconciliationId) {
        // 1. 获取业务数据
        CrmReconciliation reconciliation = reconciliationService.getById(reconciliationId);
        if (reconciliation == null) {
            throw new BusinessException("对账单不存在");
        }
        
        // 2. 检查状态
        if (!"draft".equals(reconciliation.getStatus())) {
            throw new BusinessException("只有草稿状态的对账单才能提交审批");
        }
        
        // 3. 更新业务状态
        reconciliation.setStatus("submitted");
        reconciliation.setProcessDefinitionKey("reconciliation-approval");
        reconciliation.setProcessStatus("running");
        reconciliation.setProcessStartTime(LocalDateTime.now());
        
        // 4. 准备流程变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("reconciliationId", reconciliationId);
        variables.put("reconciliationNo", reconciliation.getReconciliationNo());
        variables.put("totalAmount", reconciliation.getTotalAmount());
        variables.put("netAmount", reconciliation.getNetAmount());
        variables.put("applicant", reconciliation.getCreateBy());
        variables.put("relationType", reconciliation.getRelationType());
        variables.put("customerName", reconciliation.getCustomerName());
        
        // 5. 启动流程实例
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(
            "reconciliation-approval", 
            String.valueOf(reconciliationId), // businessKey
            variables
        );
        
        // 6. 更新流程字段
        reconciliation.setProcessInstanceId(processInstance.getId());
        
        // 7. 查询当前任务并更新
        List<Task> tasks = taskService.createTaskQuery()
            .processInstanceId(processInstance.getId())
            .active()
            .list();
            
        if (!tasks.isEmpty()) {
            Task currentTask = tasks.get(0);
            reconciliation.setCurrentTaskId(currentTask.getId());
            reconciliation.setCurrentTaskName(currentTask.getName());
            reconciliation.setCurrentAssignee(currentTask.getAssignee());
        }
        
        // 8. 保存业务数据
        reconciliationService.updateById(reconciliation);
        
        // 9. 记录流程历史
        recordProcessHistory(reconciliationId, "reconciliation", 
            processInstance.getId(), null, "submit", 
            reconciliation.getCreateBy(), "提交审批", 
            "draft", "submitted", null);
        
        return processInstance.getId();
    }
    
    /**
     * 处理审批任务
     */
    @Override
    public boolean processApprovalTask(String taskId, boolean approved, String comments) {
        try {
            // 1. 获取任务信息
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                throw new BusinessException("任务不存在或已处理");
            }
            
            // 2. 获取流程变量
            String processInstanceId = task.getProcessInstanceId();
            Long reconciliationId = (Long) runtimeService.getVariable(processInstanceId, "reconciliationId");
            
            // 3. 设置任务变量
            Map<String, Object> taskVariables = new HashMap<>();
            taskVariables.put("approvalResult", approved ? "approved" : "rejected");
            taskVariables.put("approvalComments", comments);
            taskVariables.put("approver", getCurrentUser());
            taskVariables.put("approvalTime", LocalDateTime.now());
            
            // 4. 完成任务
            taskService.complete(taskId, taskVariables);
            
            // 5. 更新业务状态（通过监听器自动触发）
            
            // 6. 记录流程历史
            recordProcessHistory(reconciliationId, "reconciliation", 
                processInstanceId, taskId, approved ? "approve" : "reject", 
                getCurrentUser(), comments, 
                "submitted", approved ? "approved" : "rejected", 
                JSON.toJSONString(taskVariables));
            
            return true;
        } catch (Exception e) {
            log.error("处理审批任务失败", e);
            return false;
        }
    }
    
    /**
     * 查询待办任务
     */
    @Override
    public List<TaskInfo> getPendingTasks(String assignee) {
        List<Task> tasks = taskService.createTaskQuery()
            .taskAssignee(assignee)
            .active()
            .orderByTaskCreateTime()
            .desc()
            .list();
            
        return tasks.stream().map(this::convertToTaskInfo).collect(Collectors.toList());
    }
    
    /**
     * 获取流程历史
     */
    @Override
    public List<ProcessHistoryInfo> getProcessHistory(Long reconciliationId) {
        // 从自定义历史表查询
        return processHistoryService.getByBusinessId("reconciliation", reconciliationId);
    }
    
    /**
     * 记录流程历史
     */
    private void recordProcessHistory(Long businessId, String businessType, 
            String processInstanceId, String taskId, String actionType,
            String actionUser, String actionComment, 
            String fromStatus, String toStatus, String formData) {
        
        CrmReconciliationProcessHistory history = new CrmReconciliationProcessHistory();
        history.setBusinessType(businessType);
        history.setBusinessId(businessId);
        history.setProcessInstanceId(processInstanceId);
        history.setTaskId(taskId);
        history.setActionType(actionType);
        history.setActionUserName(actionUser);
        history.setActionTime(LocalDateTime.now());
        history.setActionComment(actionComment);
        history.setFromStatus(fromStatus);
        history.setToStatus(toStatus);
        history.setFormData(formData);
        
        processHistoryService.save(history);
    }
    
    private String getCurrentUser() {
        // 获取当前登录用户
        return SecurityUtils.getUsername();
    }
}
```

### 4. 流程状态同步监听器

```java
// 路径: ruoyi-crm/src/main/java/com/ruoyi/activiti/listener/
@Component("reconciliationStatusListener")
@Slf4j
public class ReconciliationStatusListener implements JavaDelegate {
    
    @Autowired
    private ICrmReconciliationService reconciliationService;
    
    @Override
    public void execute(DelegateExecution execution) {
        try {
            Long reconciliationId = (Long) execution.getVariable("reconciliationId");
            String approvalResult = (String) execution.getVariable("approvalResult");
            String approver = (String) execution.getVariable("approver");
            String approvalComments = (String) execution.getVariable("approvalComments");
            LocalDateTime approvalTime = (LocalDateTime) execution.getVariable("approvalTime");
            
            // 同步更新业务状态
            CrmReconciliation reconciliation = reconciliationService.getById(reconciliationId);
            if (reconciliation != null) {
                if ("approved".equals(approvalResult)) {
                    reconciliation.setStatus("approved");
                    reconciliation.setApprovalStatus("approved");
                    reconciliation.setProcessStatus("completed");
                    reconciliation.setProcessEndTime(LocalDateTime.now());
                } else if ("rejected".equals(approvalResult)) {
                    reconciliation.setStatus("rejected");
                    reconciliation.setApprovalStatus("rejected");
                    reconciliation.setProcessStatus("completed");
                    reconciliation.setProcessEndTime(LocalDateTime.now());
                }
                
                reconciliation.setApprovalNotes(approvalComments);
                reconciliation.setApprovedTime(approvalTime);
                reconciliation.setCurrentTaskId(null);
                reconciliation.setCurrentTaskName(null);
                reconciliation.setCurrentAssignee(null);
                
                reconciliationService.updateById(reconciliation);
                
                log.info("对账单状态同步完成: ID={}, 结果={}", reconciliationId, approvalResult);
            }
        } catch (Exception e) {
            log.error("对账单状态同步失败", e);
            throw new RuntimeException("状态同步失败", e);
        }
    }
}
```

### 5. 任务待办统一管理

```java
// 路径: ruoyi-crm/src/main/java/com/ruoyi/crm/service/
@Service
public class TaskTodoServiceImpl implements ITaskTodoService {
    
    @Autowired
    private TaskService taskService;
    
    @Autowired
    private RuntimeService runtimeService;
    
    /**
     * 同步Activiti任务到待办表
     */
    @Override
    public void syncActivitiTasks() {
        // 1. 查询所有活动任务
        List<Task> tasks = taskService.createTaskQuery().active().list();
        
        // 2. 清空待办表
        taskTodoMapper.deleteAll();
        
        // 3. 重新插入
        for (Task task : tasks) {
            CrmReconciliationTaskTodo todo = new CrmReconciliationTaskTodo();
            
            // 获取流程变量确定业务类型和ID
            String businessType = (String) runtimeService.getVariable(
                task.getProcessInstanceId(), "businessType");
            Long businessId = (Long) runtimeService.getVariable(
                task.getProcessInstanceId(), "businessId");
            String businessNo = (String) runtimeService.getVariable(
                task.getProcessInstanceId(), "businessNo");
            
            todo.setBusinessType(businessType);
            todo.setBusinessId(businessId);
            todo.setBusinessNo(businessNo);
            todo.setBusinessTitle(generateBusinessTitle(businessType, businessId));
            todo.setProcessInstanceId(task.getProcessInstanceId());
            todo.setTaskId(task.getId());
            todo.setTaskName(task.getName());
            todo.setTaskDefinitionKey(task.getTaskDefinitionKey());
            todo.setAssignee(task.getAssignee());
            todo.setPriority(task.getPriority());
            todo.setTaskCreateTime(task.getCreateTime());
            todo.setDueDate(task.getDueDate());
            todo.setFormKey(task.getFormKey());
            
            taskTodoMapper.insert(todo);
        }
    }
    
    /**
     * 获取用户待办任务
     */
    @Override
    public List<TaskTodoVO> getUserTodoTasks(String assignee) {
        return taskTodoMapper.selectByAssignee(assignee);
    }
}
```

## ✅ 关键集成要点

### 1. 数据表设计要点
- **关联关系优化**：支持客户和联系人两种关联方式，使用`relation_type`字段区分
- **流程字段完整**：所有业务表都包含完整的流程相关字段
- **状态同步**：业务状态与流程状态分离但保持同步
- **约束检查**：通过数据库约束确保关联关系的正确性

### 2. 流程引擎集成要点
- **业务与流程解耦**：业务逻辑不直接依赖流程引擎，通过服务层隔离
- **状态同步机制**：通过监听器确保业务状态与流程状态同步
- **任务统一管理**：通过待办表实现高效的任务查询和管理
- **历史记录完整**：自定义历史表记录完整的操作历史

### 3. 开发规范要点
- **遵循CRM412规范**：包结构、命名规范、代码分层都遵循现有规范
- **参考现有模式**：完全参考`LeaveApplicationServiceImpl`的集成模式
- **事务管理**：确保业务操作和流程操作在同一事务中
- **异常处理**：完善的异常处理和回滚机制

这个设计方案完全基于您现有的Activiti集成模式，确保了与现有系统的兼容性，同时解决了客户/联系人关联和流程引擎集成的问题。

## 📞 技术支持

如有任何技术问题或需要进一步的开发指导，请随时联系。这套完整的设计文档将为您的对账单系统开发提供全面的技术支持。

---

**📌 重要提醒：** 
1. 严格遵循CRM412项目的分层架构规范
2. 所有代码需要进行充分的单元测试
3. 数据库操作必须保证事务一致性
4. 前端界面需要适配移动端设备
5. 定期进行代码审查和性能优化
