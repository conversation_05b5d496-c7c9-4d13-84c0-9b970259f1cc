<template>
  <div class="app-container">
    <el-card>
      <!-- 搜索栏 -->
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="客户名称" prop="customerName">
          <el-input
            v-model="queryParams.customerName"
            placeholder="请输入客户名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="对账单状态" clearable>
            <el-option label="草稿" value="draft" />
            <el-option label="待审核" value="submitted" />
            <el-option label="已审核" value="approved" />
            <!-- 其他状态 -->
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
          >新建对账单</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="selectedIds.length === 0"
            @click="handleBatchDelete"
          >批量删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="Download"
            @click="handleExport"
          >导出</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="reconciliationList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="对账单编号" align="center" prop="reconciliationNo" />
        <el-table-column label="客户名称" align="center" prop="customerName" />
        <el-table-column label="对账金额" align="center" prop="totalAmount" />
        <el-table-column label="状态" align="center" prop="status" />
        <el-table-column label="创建人" align="center" prop="createBy" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)">详情</el-button>
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-if="scope.row.status === 'draft'">修改</el-button>
            <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-if="scope.row.status === 'draft'">删除</el-button>
            <el-button link type="success" icon="Position" @click="handleSubmit(scope.row)" v-if="scope.row.status === 'draft'">提交</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改对账单对话框 -->
    <el-dialog :title="form.reconciliationId ? '修改对账单' : '新增对账单'" v-model="dialogVisible" width="70%" append-to-body>
      <el-form ref="reconciliationFormRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12" v-for="item in formConfig.formItems" :key="item.prop">
            <el-form-item :label="item.label" :prop="item.prop">
              <el-input
                v-if="item.type === 'text' || item.type === 'textarea'"
                :type="item.type"
                v-model="(form as any)[item.prop]"
                :placeholder="item.placeholder"
              />
              <el-date-picker
                v-else-if="item.type === 'date'"
                v-model="(form as any)[item.prop]"
                type="date"
                value-format="YYYY-MM-DD"
                :placeholder="item.placeholder"
                style="width: 100%;"
              />
              <!-- 临时用输入框代替客户选择 -->
              <el-input
                v-else-if="item.type === 'select-customer'"
                v-model="form.customerName"
                :placeholder="item.placeholder"
              >
                <template #append>
                  <el-button icon="Search" />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">对账明细</el-divider>
        <el-button type="primary" plain icon="Plus" class="mb8" @click="handleAddOrder">添加订单</el-button>
        <el-table :data="form.details">
            <el-table-column label="订单编号" prop="orderNo" />
            <el-table-column label="订单金额" prop="amount" />
            <el-table-column label="操作">
                <template #default="scope">
                    <el-button link type="danger" @click="removeDetailItem(scope.$index)">移除</el-button>
                </template>
            </el-table-column>
        </el-table>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 选择订单对话框 -->
    <select-order-dialog
      v-if="selectOrderDialogVisible"
      v-model:visible="selectOrderDialogVisible"
      :customer-id="form.customerId"
      @confirm="handleSelectOrderConfirm"
    />

    <!-- 对账单详情抽屉 -->
    <reconciliation-detail-drawer
      v-model:visible="detailDrawerVisible"
      :reconciliation="currentReconciliation"
      @edit="handleEditFromDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import type { ElForm } from 'element-plus';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getReconciliationList, getReconciliation, addReconciliation, updateReconciliation, delReconciliation, submitReconciliation, exportReconciliation } from './api';
import { Reconciliation, ReconciliationQuery, ReconciliationExportQuery, Order, ReconciliationDetail } from './types';
import RightToolbar from '@/components/RightToolbar/index.vue';
import Pagination from '@/components/Pagination/index.vue';
import { formConfig } from './config/formConfig';
import SelectOrderDialog from './components/SelectOrderDialog.vue';
import ReconciliationDetailDrawer from './components/ReconciliationDetailDrawer.vue';

const { proxy } = getCurrentInstance()!;

const queryFormRef = ref<InstanceType<typeof ElForm> | null>(null);
const reconciliationFormRef = ref<InstanceType<typeof ElForm> | null>(null);
const loading = ref(true);
const showSearch = ref(true);
const reconciliationList = ref<Reconciliation[]>([]);
const total = ref(0);
const dialogVisible = ref(false);
const selectOrderDialogVisible = ref(false);
const detailDrawerVisible = ref(false);
const currentReconciliation = ref<Reconciliation | null>(null);
const selectedIds = ref<number[]>([]);
const form = ref<Partial<Reconciliation>>({});
const rules = ref(formConfig.rules);

const queryParams = reactive<ReconciliationQuery>({
  pageNum: 1,
  pageSize: 10,
  customerName: undefined,
  status: undefined,
});

/** 查询列表 */
function getList() {
  loading.value = true;
  getReconciliationList(queryParams).then((response: any) => {
    reconciliationList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 取消按钮 */
function cancel() {
  dialogVisible.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    reconciliationId: undefined,
    customerId: undefined,
    customerName: undefined,
    reconciliationDate: new Date(),
    reconciliationPeriod: '',
    remark: '',
    details: []
  };
  reconciliationFormRef.value?.resetFields();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  dialogVisible.value = true;
}

/** 查看详情操作 */
function handleView(row: Reconciliation) {
  // 获取完整的对账单数据
  getReconciliation(row.reconciliationId).then((response: any) => {
    currentReconciliation.value = response.data;
    detailDrawerVisible.value = true;
  }).catch(() => {
    // 如果API调用失败，使用列表中的数据
    currentReconciliation.value = row;
    detailDrawerVisible.value = true;
  });
}

/** 从详情页编辑 */
function handleEditFromDetail(reconciliation: Reconciliation) {
  form.value = { ...reconciliation };
  dialogVisible.value = true;
}

/** 修改按钮操作 */
function handleUpdate(row: Reconciliation) {
  reset();
  getReconciliation(row.reconciliationId).then((response: any) => {
    form.value = response.data;
    dialogVisible.value = true;
  });
}

/** 添加订单按钮操作 */
function handleAddOrder() {
  if (!form.value.customerId) {
    ElMessage.warning('请先选择客户');
    return;
  }
  selectOrderDialogVisible.value = true;
}

/** 选择订单确认操作 */
function handleSelectOrderConfirm(selectedOrders: Order[]) {
  if (!form.value.details) {
    form.value.details = [];
  }
  // 转换并去重
  selectedOrders.forEach(order => {
    const isExisting = form.value.details!.some(detail => detail.orderId === order.orderId);
    if (!isExisting) {
      form.value.details!.push({
        orderId: order.orderId,
        orderNo: order.orderNo,
        amount: order.amount,
        detailType: 'order'
      } as ReconciliationDetail);
    }
  });
}

/** 移除明细项 */
function removeDetailItem(index: number) {
    if (form.value.details) {
      form.value.details.splice(index, 1);
    }
}

/** 提交按钮 */
function submitForm() {
  if (!reconciliationFormRef.value) return;
  reconciliationFormRef.value.validate((valid: boolean) => {
    if (!valid) return;
    if (form.value.reconciliationId) {
      updateReconciliation(form.value as Reconciliation).then(() => {
        (proxy as any).$modal.msgSuccess("修改成功");
        dialogVisible.value = false;
        getList();
      });
    } else {
      addReconciliation(form.value as Reconciliation).then(() => {
        (proxy as any).$modal.msgSuccess("新增成功");
        dialogVisible.value = false;
        getList();
      });
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row: Reconciliation) {
  ElMessageBox.confirm(`是否确认删除编号为"${row.reconciliationNo}"的数据项?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    delReconciliation(row.reconciliationId).then(() => {
      getList();
      ElMessage.success("删除成功");
    });
  });
}

/** 多选框选中数据 */
function handleSelectionChange(selection: Reconciliation[]) {
  selectedIds.value = selection.map(item => item.reconciliationId);
}

/** 批量删除操作 */
function handleBatchDelete() {
  if (selectedIds.value.length === 0) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }
  
  ElMessageBox.confirm(`是否确认删除选中的 ${selectedIds.value.length} 条数据?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    delReconciliation(selectedIds.value).then(() => {
      getList();
      selectedIds.value = [];
      ElMessage.success("批量删除成功");
    });
  });
}

/** 导出操作 */
function handleExport() {
  ElMessageBox.confirm('是否确认导出对账单数据?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info',
  }).then(() => {
    // 构建导出参数
    const exportParams: ReconciliationExportQuery = {
      reconciliationNo: queryParams.reconciliationNo,
      customerName: queryParams.customerName,
      customerId: queryParams.customerId,
      status: queryParams.status,
    };
    
    // 调用导出API
    exportReconciliation(exportParams).then((response: any) => {
      // 处理blob响应
      const blob = new Blob([response], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `对账单数据_${new Date().toISOString().slice(0, 10)}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      ElMessage.success('导出成功');
    }).catch(() => {
      ElMessage.error('导出失败');
    });
  }).catch(() => {
    ElMessage.info('已取消导出');
  });
}

/** 提交审批按钮 */
function handleSubmit(row: Reconciliation) {
  ElMessageBox.confirm(`是否确认提交编号为"${row.reconciliationNo}"的数据项?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    submitReconciliation(row.reconciliationId).then(() => {
      ElMessage.success('提交成功');
      getList();
    });
  });
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.reconciliation-management-container {
  padding: 20px;
}
</style>
