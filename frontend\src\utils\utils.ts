import type { HeaderField, ValidationRule } from './types';

// 格式化日期
export const formatDate = (date: string | Date): string => {
    if (!date) return '-';
    const d = new Date(date);
    return d.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// 格式化文件大小
export const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 验证字段值
export const validateField = (value: any, rules: ValidationRule[]): string | true => {
    for (const rule of rules) {
        if (rule.required && !value) {
            return rule.message || '该字段为必填项';
        }
        if (rule.pattern && !rule.pattern.test(value)) {
            return rule.message || '格式不正确';
        }
        if (rule.min !== undefined && value < rule.min) {
            return rule.message || `最小值不能小于${rule.min}`;
        }
        if (rule.max !== undefined && value > rule.max) {
            return rule.message || `最大值不能大于${rule.max}`;
        }
        if (rule.validator) {
            return new Promise((resolve) => {
                rule.validator(rule, value, (error) => {
                    resolve(error ? error.message : true);
                });
            });
        }
    }
    return true;
};

// 获取字段显示值
export const getFieldDisplayValue = (field: HeaderField, value: any): string => {
    if (!value) return '-';
    if (field.formatter) {
        return field.formatter(value);
    }
    if (field.type === 'date') {
        return formatDate(value);
    }
    if (field.type === 'select' && field.options) {
        const option = field.options.find(opt => opt.value === value);
        return option ? option.label : value;
    }
    return String(value);
};

// 深拷贝对象
export const deepClone = <T>(obj: T): T => {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    if (obj instanceof Date) {
        return new Date(obj.getTime()) as any;
    }
    if (obj instanceof Array) {
        return obj.map(item => deepClone(item)) as any;
    }
    if (obj instanceof Object) {
        const copy = {} as any;
        Object.keys(obj).forEach(key => {
            copy[key] = deepClone((obj as any)[key]);
        });
        return copy;
    }
    return obj;
};

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait: number
): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
};

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
    func: T,
    wait: number
): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(() => (inThrottle = false), wait);
        }
    };
};

// 生成唯一ID
export const generateId = (): string => {
    return Math.random().toString(36).substr(2, 9);
};

// 检查对象是否为空
export const isEmpty = (obj: any): boolean => {
    if (obj === null || obj === undefined) return true;
    if (typeof obj === 'string') return obj.trim().length === 0;
    if (Array.isArray(obj)) return obj.length === 0;
    if (typeof obj === 'object') return Object.keys(obj).length === 0;
    return false;
};

// 获取对象指定路径的值
export const getValueByPath = (obj: any, path: string): any => {
    return path.split('.').reduce((prev, curr) => {
        return prev ? prev[curr] : undefined;
    }, obj);
};

// 设置对象指定路径的值
export const setValueByPath = (obj: any, path: string, value: any): void => {
    const keys = path.split('.');
    const lastKey = keys.pop();
    const target = keys.reduce((prev, curr) => {
        if (!prev[curr]) prev[curr] = {};
        return prev[curr];
    }, obj);
    if (lastKey) target[lastKey] = value;
}; 