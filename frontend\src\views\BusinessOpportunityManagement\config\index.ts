import { TableOperationsConfig } from '@/components/TableOperations/types';
import type { ButtonType } from 'element-plus';

// 表格列配置
export const tableColumns = [
    { type: 'selection', width: 55 },
    { prop: 'opportunity_name', label: '商机名称', width: 180, sortable: true },
    { prop: 'customer_name', label: '客户名称', width: 180, sortable: true },
    { prop: 'opportunity_amount', label: '商机金额', width: 180, sortable: true },
    { prop: 'opportunity_stage', label: '商机阶段', width: 180, sortable: true },
    { prop: 'win_rate', label: '赢单率', width: 180, sortable: true },
    { prop: 'expected_close_date', label: '预计关闭日期', width: 180, sortable: true },
    { prop: 'opportunity_source', label: '商机来源', width: 180, sortable: true },
    { prop: 'opportunity_type', label: '商机类型', width: 180, sortable: true },
    { prop: 'remarks', label: '备注', width: 180, sortable: true },
    { prop: 'manager', label: '负责人', width: 180, sortable: true }
];

// 表格操作配置
export const tableOperations: TableOperationsConfig = {
    width: 220,
    fixed: 'right' as const,
    buttons: [
        {
            label: '分配',
            type: 'primary' as ButtonType,
            link: true,
            handler: 'handleAssign',
            icon: 'Share',
            show: true
        },
        {
            label: '转化',
            type: 'success' as ButtonType,
            link: true,
            handler: 'handleConvert',
            icon: 'Promotion',
            show: true
        },
        {
            label: '删除',
            type: 'danger' as ButtonType,
            link: true,
            handler: 'handleDelete',
            icon: 'Delete',
            show: true
        }
    ]
};

// 新建商机表单配置
export const newOpportunityFormConfig = {
    layout: {
        labelPosition: 'top',
        size: 'default',
        columns: 2
    },
    fields: [
        { label: '商机名称', field: 'opportunity_name', component: 'el-input', colSpan: 12 },
        { label: '客户名称', field: 'customer_name', component: 'el-input', colSpan: 12 },
        { 
            label: '商机金额', 
            field: 'opportunity_amount', 
            component: 'el-input-number', 
            colSpan: 12,
            props: {
                min: 0,
                precision: 2,
                step: 1000,
                'controls-position': 'right'
            }
        },
        { 
            label: '商机阶段', 
            field: 'opportunity_stage', 
            component: 'el-select', 
            colSpan: 12,
            props: {
                options: [
                    { label: '初步接触', value: 'initial_contact' },
                    { label: '需求分析', value: 'requirement_analysis' },
                    { label: '方案评估', value: 'solution_evaluation' },
                    { label: '合同谈判', value: 'contract_negotiation' }
                ]
            }
        },
        { 
            label: '赢单率', 
            field: 'win_rate', 
            component: 'el-input-number', 
            colSpan: 12,
            props: {
                min: 0,
                max: 100,
                step: 10,
                'controls-position': 'right'
            }
        },
        { 
            label: '预计关闭日期', 
            field: 'expected_close_date', 
            component: 'el-date-picker', 
            colSpan: 12,
            props: {
                type: 'date',
                placeholder: '选择日期'
            }
        },
        { 
            label: '商机来源', 
            field: 'opportunity_source', 
            component: 'el-select', 
            colSpan: 12,
            props: {
                options: [
                    { label: '线上广告', value: '线上广告' },
                    { label: '客户推荐', value: '客户推荐' },
                    { label: '电话营销', value: '电话营销' },
                    { label: '展会', value: '展会' }
                ]
            }
        },
        { 
            label: '商机类型', 
            field: 'opportunity_type', 
            component: 'el-select', 
            colSpan: 12,
            props: {
                options: [
                    { label: '新客户', value: '新客户' },
                    { label: '老客户', value: '老客户' },
                    { label: '战略客户', value: '战略客户' }
                ]
            }
        },
        { 
            label: '备注', 
            field: 'remarks', 
            component: 'el-input', 
            colSpan: 24,
            props: {
                type: 'textarea',
                rows: 4
            }
        }
    ]
};

// 抽屉配置
export const drawerConfig = {
    headerFields: [
        { label: '客户名称', field: 'customer_name' },
        { label: '商机金额', field: 'opportunity_amount' },
        { label: '商机阶段', field: 'opportunity_stage' },
        { label: '创建时间', field: 'createTime' }
    ],
    actions: [
        {
            label: '编辑',
            type: 'primary',
            icon: 'Edit',
            handler: (data: any) => {
                console.log('编辑', data);
            }
        },
        {
            label: '转移',
            icon: 'Share',
            handler: (data: any) => {
                console.log('转移', data);
            }
        },
        {
            label: '打印',
            icon: 'Printer',
            handler: (data: any) => {
                console.log('打印', data);
            }
        },
        {
            label: '转化',
            type: 'success',
            icon: 'Promotion',
            handler: (data: any) => {
                console.log('转化', data);
            }
        },
        {
            label: '删除',
            type: 'danger',
            icon: 'Delete',
            handler: (data: any) => {
                console.log('删除', data);
            }
        }
    ],
    menuItems: [
        {
            key: 'details',
            label: '详细资料',
            icon: 'Document',
            component: 'OpportunityDetailsTab'
        },
        {
            key: 'activity',
            label: '活动记录',
            icon: 'Timer',
            component: 'OpportunityActivityTab'
        },
        {
            key: 'attachments',
            label: '附件',
            icon: 'Folder',
            component: 'OpportunityAttachmentsTab',
            badge: true
        },
        {
            key: 'operations',
            label: '操作记录',
            icon: 'List',
            component: 'OpportunityOperationsTab'
        }
    ]
};

// 导航配置
export const navConfig = {
    title: '商机管理',
    menuItems: [
        {
            key: 'opportunities',
            label: '商机',
            icon: 'Opportunity'
        },
        {
            key: 'followup',
            label: '跟进记录',
            icon: 'ChatRound'
        }
    ]
}; 