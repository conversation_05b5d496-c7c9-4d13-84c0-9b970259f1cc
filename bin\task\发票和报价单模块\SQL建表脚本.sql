-- =====================================================
-- 发票和报价单模块数据库建表脚本
-- 创建时间: 2025-07-21
-- 说明: 基于现有CRM系统架构设计
-- =====================================================

-- 1. 报价单主表
DROP TABLE IF EXISTS `crm_quotations`;
CREATE TABLE `crm_quotations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `quotation_no` varchar(50) NOT NULL COMMENT '报价单编号',
  `quotation_name` varchar(200) NOT NULL COMMENT '报价单名称',
  
  -- === 关联关系字段 ===
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID，外键关联crm_business_customers.id',
  `customer_name` varchar(100) NOT NULL COMMENT '客户名称（冗余字段）',
  `contact_id` bigint(20) NOT NULL COMMENT '联系人ID，外键关联crm_business_contacts.id',
  `contact_name` varchar(100) NOT NULL COMMENT '联系人姓名（冗余字段）',
  `responsible_person_id` varchar(64) NOT NULL COMMENT '负责人ID',
  
  -- === 金额相关字段 ===
  `total_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '报价总金额',
  `currency` varchar(10) NOT NULL DEFAULT 'CNY' COMMENT '币种',
  
  -- === 状态管理字段 ===
  `status` varchar(20) NOT NULL DEFAULT 'draft' COMMENT '状态：draft-草稿,submitted-已提交,approved-已审批,rejected-已驳回,cancelled-已取消',
  `approval_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '审批状态：pending-待审批,approved-审批通过,rejected-审批驳回',
  
  -- === 工作流相关字段 ===
  `process_instance_id` varchar(64) DEFAULT NULL COMMENT '流程实例ID',
  `process_status` varchar(20) DEFAULT NULL COMMENT '流程状态：running-运行中,completed-完成,terminated-终止',
  `current_task_id` varchar(64) DEFAULT NULL COMMENT '当前任务ID',
  `current_task_name` varchar(100) DEFAULT NULL COMMENT '当前任务名称',
  `current_assignee` varchar(64) DEFAULT NULL COMMENT '当前处理人',
  `process_start_time` datetime DEFAULT NULL COMMENT '流程开始时间',
  `process_end_time` datetime DEFAULT NULL COMMENT '流程结束时间',
  
  -- === 业务字段 ===
  `valid_until` date DEFAULT NULL COMMENT '有效期至',
  `quotation_date` date NOT NULL DEFAULT (CURRENT_DATE) COMMENT '报价日期',
  `delivery_terms` varchar(200) DEFAULT NULL COMMENT '交货条款',
  `payment_terms` varchar(200) DEFAULT NULL COMMENT '付款条款',
  `remarks` text COMMENT '备注',
  
  -- === 系统字段 ===
  `del_flag` char(1) NOT NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_quotation_no` (`quotation_no`),
  KEY `idx_quotations_customer` (`customer_id`),
  KEY `idx_quotations_contact` (`contact_id`),
  KEY `idx_quotations_responsible` (`responsible_person_id`),
  KEY `idx_quotations_status` (`status`),
  KEY `idx_quotations_date` (`quotation_date`),
  KEY `idx_quotations_process` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报价单主表';

-- 2. 报价单明细表
DROP TABLE IF EXISTS `crm_quotation_items`;
CREATE TABLE `crm_quotation_items` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `quotation_id` bigint(20) NOT NULL COMMENT '报价单ID，外键关联crm_quotations.id',
  
  -- === 产品信息 ===
  `product_name` varchar(200) NOT NULL COMMENT '产品名称',
  `product_code` varchar(50) DEFAULT NULL COMMENT '产品编码',
  `specification` varchar(500) DEFAULT NULL COMMENT '规格说明',
  `brand` varchar(100) DEFAULT NULL COMMENT '品牌',
  `model` varchar(100) DEFAULT NULL COMMENT '型号',
  
  -- === 数量和价格 ===
  `quantity` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '数量',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `unit_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '单价',
  `total_price` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '小计',
  `discount_rate` decimal(5,4) DEFAULT '0.0000' COMMENT '折扣率',
  `discount_amount` decimal(15,2) DEFAULT '0.00' COMMENT '折扣金额',
  `final_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '最终金额',
  
  -- === 其他信息 ===
  `delivery_date` date DEFAULT NULL COMMENT '交货日期',
  `warranty_period` varchar(50) DEFAULT NULL COMMENT '质保期',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  
  PRIMARY KEY (`id`),
  KEY `idx_quotation_items_quotation` (`quotation_id`),
  KEY `idx_quotation_items_product` (`product_name`),
  CONSTRAINT `fk_quotation_items_quotation` FOREIGN KEY (`quotation_id`) REFERENCES `crm_quotations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报价单明细表';

-- 3. 发票主表
DROP TABLE IF EXISTS `crm_invoices`;
CREATE TABLE `crm_invoices` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `invoice_no` varchar(50) NOT NULL COMMENT '发票编号',
  `invoice_title` varchar(200) NOT NULL COMMENT '发票抬头',
  `invoice_type` varchar(20) NOT NULL DEFAULT 'special' COMMENT '发票类型：special-增值税专用发票,ordinary-增值税普通发票,electronic-电子发票',
  
  -- === 关联关系字段 ===
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID，外键关联crm_business_customers.id',
  `customer_name` varchar(100) NOT NULL COMMENT '客户名称（冗余字段）',
  `contact_id` bigint(20) DEFAULT NULL COMMENT '联系人ID，外键关联crm_business_contacts.id',
  `contact_name` varchar(100) DEFAULT NULL COMMENT '联系人姓名（冗余字段）',
  `quotation_id` bigint(20) DEFAULT NULL COMMENT '关联报价单ID，外键关联crm_quotations.id',
  `contract_id` bigint(20) DEFAULT NULL COMMENT '关联合同ID，外键关联crm_contracts.id',
  `contract_no` varchar(50) DEFAULT NULL COMMENT '合同编号（冗余字段）',
  `responsible_person_id` varchar(64) NOT NULL COMMENT '负责人ID',
  
  -- === 税务信息 ===
  `taxpayer_id` varchar(50) DEFAULT NULL COMMENT '纳税人识别号',
  `tax_rate` decimal(5,4) NOT NULL DEFAULT '0.1300' COMMENT '税率',
  `amount_excluding_tax` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '不含税金额',
  `tax_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '税额',
  `amount_including_tax` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '含税金额',
  
  -- === 开票信息 ===
  `bank_name` varchar(100) DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
  `company_address` varchar(200) DEFAULT NULL COMMENT '公司地址',
  `company_phone` varchar(50) DEFAULT NULL COMMENT '公司电话',
  
  -- === 状态和日期 ===
  `status` varchar(20) NOT NULL DEFAULT 'draft' COMMENT '发票状态：draft-草稿,submitted-已提交,approved-已审批,rejected-已驳回,issued-已开票,cancelled-已作废,returned-已退票',
  `approval_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '审批状态：pending-待审批,approved-审批通过,rejected-审批驳回',

  -- === 工作流相关字段 ===
  `process_instance_id` varchar(64) DEFAULT NULL COMMENT '流程实例ID',
  `process_status` varchar(20) DEFAULT NULL COMMENT '流程状态：running-运行中,completed-完成,terminated-终止',
  `current_task_id` varchar(64) DEFAULT NULL COMMENT '当前任务ID',
  `current_task_name` varchar(100) DEFAULT NULL COMMENT '当前任务名称',
  `current_assignee` varchar(64) DEFAULT NULL COMMENT '当前处理人',
  `process_start_time` datetime DEFAULT NULL COMMENT '流程开始时间',
  `process_end_time` datetime DEFAULT NULL COMMENT '流程结束时间',

  `issue_date` date DEFAULT NULL COMMENT '开票日期',
  `due_date` date DEFAULT NULL COMMENT '到期日期',
  `cancel_date` date DEFAULT NULL COMMENT '作废日期',
  `cancel_reason` varchar(500) DEFAULT NULL COMMENT '作废原因',
  
  -- === 其他信息 ===
  `currency` varchar(10) NOT NULL DEFAULT 'CNY' COMMENT '币种',
  `exchange_rate` decimal(10,6) DEFAULT '1.000000' COMMENT '汇率',
  `remarks` text COMMENT '备注',
  
  -- === 系统字段 ===
  `del_flag` char(1) NOT NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_invoice_no` (`invoice_no`),
  KEY `idx_invoices_customer` (`customer_id`),
  KEY `idx_invoices_contact` (`contact_id`),
  KEY `idx_invoices_quotation` (`quotation_id`),
  KEY `idx_invoices_contract` (`contract_id`),
  KEY `idx_invoices_responsible` (`responsible_person_id`),
  KEY `idx_invoices_status` (`status`),
  KEY `idx_invoices_approval_status` (`approval_status`),
  KEY `idx_invoices_process` (`process_instance_id`),
  KEY `idx_invoices_issue_date` (`issue_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票主表';

-- 4. 发票明细表
DROP TABLE IF EXISTS `crm_invoice_items`;
CREATE TABLE `crm_invoice_items` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `invoice_id` bigint(20) NOT NULL COMMENT '发票ID，外键关联crm_invoices.id',
  
  -- === 项目信息 ===
  `item_name` varchar(200) NOT NULL COMMENT '项目名称',
  `item_code` varchar(50) DEFAULT NULL COMMENT '项目编码',
  `specification` varchar(500) DEFAULT NULL COMMENT '规格型号',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  
  -- === 数量和金额 ===
  `quantity` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '数量',
  `unit_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '单价',
  `amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '金额',
  `tax_rate` decimal(5,4) NOT NULL DEFAULT '0.1300' COMMENT '税率',
  `tax_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '税额',
  
  -- === 其他信息 ===
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  
  PRIMARY KEY (`id`),
  KEY `idx_invoice_items_invoice` (`invoice_id`),
  KEY `idx_invoice_items_name` (`item_name`),
  CONSTRAINT `fk_invoice_items_invoice` FOREIGN KEY (`invoice_id`) REFERENCES `crm_invoices` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票明细表';

-- 5. 添加外键约束（可选，根据实际需要）
-- ALTER TABLE crm_quotations ADD CONSTRAINT fk_quotations_customer FOREIGN KEY (customer_id) REFERENCES crm_business_customers(id);
-- ALTER TABLE crm_quotations ADD CONSTRAINT fk_quotations_contact FOREIGN KEY (contact_id) REFERENCES crm_business_contacts(id);
-- ALTER TABLE crm_invoices ADD CONSTRAINT fk_invoices_customer FOREIGN KEY (customer_id) REFERENCES crm_business_customers(id);
-- ALTER TABLE crm_invoices ADD CONSTRAINT fk_invoices_contact FOREIGN KEY (contact_id) REFERENCES crm_business_contacts(id);
-- ALTER TABLE crm_invoices ADD CONSTRAINT fk_invoices_quotation FOREIGN KEY (quotation_id) REFERENCES crm_quotations(id);

-- 6. 初始化数据字典（可选）
-- 报价单状态字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('报价单状态', 'crm_quotation_status', '0', 'admin', NOW(), '报价单状态列表');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '草稿', 'draft', 'crm_quotation_status', '', 'info', 'Y', '0', 'admin', NOW(), '草稿状态'),
(2, '已提交', 'submitted', 'crm_quotation_status', '', 'warning', 'N', '0', 'admin', NOW(), '已提交审批'),
(3, '已审批', 'approved', 'crm_quotation_status', '', 'success', 'N', '0', 'admin', NOW(), '审批通过'),
(4, '已驳回', 'rejected', 'crm_quotation_status', '', 'danger', 'N', '0', 'admin', NOW(), '审批驳回'),
(5, '已取消', 'cancelled', 'crm_quotation_status', '', 'info', 'N', '0', 'admin', NOW(), '已取消');

-- 发票状态字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('发票状态', 'crm_invoice_status', '0', 'admin', NOW(), '发票状态列表');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '待开票', 'pending', 'crm_invoice_status', '', 'warning', 'Y', '0', 'admin', NOW(), '待开票'),
(2, '已开票', 'issued', 'crm_invoice_status', '', 'success', 'N', '0', 'admin', NOW(), '已开票'),
(3, '已作废', 'cancelled', 'crm_invoice_status', '', 'danger', 'N', '0', 'admin', NOW(), '已作废'),
(4, '已退票', 'returned', 'crm_invoice_status', '', 'info', 'N', '0', 'admin', NOW(), '已退票');

-- 发票类型字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('发票类型', 'crm_invoice_type', '0', 'admin', NOW(), '发票类型列表');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '增值税专用发票', 'special', 'crm_invoice_type', '', 'primary', 'Y', '0', 'admin', NOW(), '增值税专用发票'),
(2, '增值税普通发票', 'ordinary', 'crm_invoice_type', '', 'success', 'N', '0', 'admin', NOW(), '增值税普通发票'),
(3, '电子发票', 'electronic', 'crm_invoice_type', '', 'info', 'N', '0', 'admin', NOW(), '电子发票');

-- 5. 发票附件表（图片记录）
DROP TABLE IF EXISTS `crm_invoice_attachments`;
CREATE TABLE `crm_invoice_attachments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `invoice_id` bigint(20) NOT NULL COMMENT '发票ID，外键关联crm_invoices.id',

  -- === 文件信息 ===
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `file_original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件存储路径',
  `file_url` varchar(500) DEFAULT NULL COMMENT '文件访问URL',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型（MIME类型）',
  `file_extension` varchar(10) DEFAULT NULL COMMENT '文件扩展名',

  -- === 附件分类 ===
  `attachment_type` varchar(20) NOT NULL DEFAULT 'invoice_image' COMMENT '附件类型：invoice_image-发票图片,contract_scan-合同扫描件,other-其他',
  `attachment_category` varchar(50) DEFAULT NULL COMMENT '附件分类：正本、副本、电子版等',

  -- === 其他信息 ===
  `description` varchar(500) DEFAULT NULL COMMENT '附件描述',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `is_main` char(1) DEFAULT '0' COMMENT '是否主要附件（0否 1是）',

  -- === 系统字段 ===
  `del_flag` char(1) NOT NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',

  PRIMARY KEY (`id`),
  KEY `idx_invoice_attachments_invoice` (`invoice_id`),
  KEY `idx_invoice_attachments_type` (`attachment_type`),
  KEY `idx_invoice_attachments_main` (`is_main`),
  CONSTRAINT `fk_invoice_attachments_invoice` FOREIGN KEY (`invoice_id`) REFERENCES `crm_invoices` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票附件表';

-- 6. 添加外键约束（可选，根据实际需要）
-- ALTER TABLE crm_quotations ADD CONSTRAINT fk_quotations_customer FOREIGN KEY (customer_id) REFERENCES crm_business_customers(id);
-- ALTER TABLE crm_quotations ADD CONSTRAINT fk_quotations_contact FOREIGN KEY (contact_id) REFERENCES crm_business_contacts(id);
-- ALTER TABLE crm_invoices ADD CONSTRAINT fk_invoices_customer FOREIGN KEY (customer_id) REFERENCES crm_business_customers(id);
-- ALTER TABLE crm_invoices ADD CONSTRAINT fk_invoices_contact FOREIGN KEY (contact_id) REFERENCES crm_business_contacts(id);
-- ALTER TABLE crm_invoices ADD CONSTRAINT fk_invoices_quotation FOREIGN KEY (quotation_id) REFERENCES crm_quotations(id);
-- ALTER TABLE crm_invoices ADD CONSTRAINT fk_invoices_contract FOREIGN KEY (contract_id) REFERENCES crm_contracts(id);

-- 7. 更新数据字典 - 发票状态
UPDATE sys_dict_data SET dict_label='草稿', dict_value='draft' WHERE dict_type='crm_invoice_status' AND dict_sort=1;
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(2, '已提交', 'submitted', 'crm_invoice_status', '', 'warning', 'N', '0', 'admin', NOW(), '已提交审批'),
(3, '已审批', 'approved', 'crm_invoice_status', '', 'primary', 'N', '0', 'admin', NOW(), '审批通过'),
(4, '已驳回', 'rejected', 'crm_invoice_status', '', 'danger', 'N', '0', 'admin', NOW(), '审批驳回');

-- 发票审批状态字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark)
VALUES ('发票审批状态', 'crm_invoice_approval_status', '0', 'admin', NOW(), '发票审批状态列表');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '待审批', 'pending', 'crm_invoice_approval_status', '', 'warning', 'Y', '0', 'admin', NOW(), '待审批'),
(2, '审批通过', 'approved', 'crm_invoice_approval_status', '', 'success', 'N', '0', 'admin', NOW(), '审批通过'),
(3, '审批驳回', 'rejected', 'crm_invoice_approval_status', '', 'danger', 'N', '0', 'admin', NOW(), '审批驳回');

-- 发票附件类型字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark)
VALUES ('发票附件类型', 'crm_invoice_attachment_type', '0', 'admin', NOW(), '发票附件类型列表');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '发票图片', 'invoice_image', 'crm_invoice_attachment_type', '', 'primary', 'Y', '0', 'admin', NOW(), '发票图片'),
(2, '合同扫描件', 'contract_scan', 'crm_invoice_attachment_type', '', 'success', 'N', '0', 'admin', NOW(), '合同扫描件'),
(3, '其他附件', 'other', 'crm_invoice_attachment_type', '', 'info', 'N', '0', 'admin', NOW(), '其他附件');

-- 创建完成提示
SELECT '发票和报价单模块数据库表创建完成！（包含发票审批流程和附件管理）' as message;
