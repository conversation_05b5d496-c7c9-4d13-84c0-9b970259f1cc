import request from '@/utils/request'

// 查询业务列表
export function listBusiness(query) {
  return request({
    url: '/system/business/list',
    method: 'get',
    params: query
  })
}

// 查询业务详细
export function getBusiness(id) {
  return request({
    url: '/system/business/' + id,
    method: 'get'
  })
}

// 新增业务
export function addBusiness(data) {
  return request({
    url: '/system/business',
    method: 'post',
    data: data
  })
}

// 修改业务
export function updateBusiness(data) {
  return request({
    url: '/system/business',
    method: 'put',
    data: data
  })
}

// 删除业务
export function delBusiness(id) {
  return request({
    url: '/system/business/' + id,
    method: 'delete'
  })
}
