# CRM对账单系统时序图详细设计

## 📊 核心业务时序图

### 1. 完整业务流程时序图

```mermaid
%%{init: {'theme': 'default', 'sequence': {'actorMargin': 50}}%%
sequenceDiagram
    participant User as 业务员
    participant System as CRM系统
    participant OrderDB as 订单数据库
    participant RecDB as 对账数据库
    participant Finance as 财务人员
    participant InvoiceDB as 发票数据库
    participant PayDB as 回款数据库
    
    Note over User,PayDB: 完整对账单业务流程
    
    %% 1. 对账单创建阶段
    Note over User,RecDB: 阶段1: 对账单创建
    User->>+System: 进入对账单管理页面
    System->>+OrderDB: 查询待对账订单列表
    OrderDB-->>-System: 返回订单数据
    System-->>User: 展示订单选择界面
    
    User->>System: 选择订单明细
    System->>System: 计算订单总金额
    
    opt 添加手动明细
        User->>System: 添加手动对账项
        System->>System: 验证并添加明细
    end
    
    opt 使用预收款
        User->>System: 选择预收款抵扣
        System->>OrderDB: 查询客户预收款余额
        OrderDB-->>System: 返回预收款信息
        System->>System: 计算净对账金额
    end
    
    User->>System: 提交创建对账单
    System->>+RecDB: 保存对账单信息
    RecDB-->>-System: 返回对账单ID
    System-->>-User: 创建成功通知
    
    %% 2. 对账单审核阶段
    Note over User,RecDB: 阶段2: 对账单审核
    User->>+System: 提交对账单审核
    System->>RecDB: 更新对账单状态为"待审核"
    System->>Finance: 发送审核通知
    
    Finance->>+System: 查看对账单详情
    System->>+RecDB: 查询对账单完整信息
    RecDB-->>-System: 返回对账单数据
    System-->>Finance: 展示对账单详情
    
    alt 审核通过
        Finance->>System: 审核通过操作
        System->>RecDB: 更新状态为"已审核"
        System->>User: 发送审核通过通知
    else 审核驳回
        Finance->>System: 审核驳回操作
        System->>RecDB: 更新状态为"已驳回"
        System->>User: 发送驳回通知
        User->>System: 修改对账单重新提交
    end
    System-->>-Finance: 审核完成确认
    
    %% 3. 开票申请阶段
    Note over User,InvoiceDB: 阶段3: 开票申请
    User->>+System: 申请开票
    System->>+RecDB: 查询已审核对账单
    RecDB-->>-System: 返回对账单信息
    
    opt 合并多个对账单开票
        User->>System: 选择多个对账单
        System->>System: 计算合并开票金额
    end
    
    User->>System: 填写开票信息并提交
    System->>+InvoiceDB: 保存开票申请
    InvoiceDB-->>-System: 返回申请ID
    System->>Finance: 发送开票审批通知
    
    Finance->>+System: 审批开票申请
    System->>+InvoiceDB: 查询申请详情
    InvoiceDB-->>-System: 返回申请信息
    System-->>Finance: 展示申请详情
    
    alt 审批通过
        Finance->>System: 审批通过
        System->>InvoiceDB: 更新申请状态
        Finance->>System: 开具发票
        System->>InvoiceDB: 记录发票信息
        System->>User: 发送开票完成通知
    else 审批驳回
        Finance->>System: 审批驳回
        System->>InvoiceDB: 更新申请状态
        System->>User: 发送驳回通知
    end
    System-->>-Finance: 开票流程完成
    
    %% 4. 回款管理阶段
    Note over User,PayDB: 阶段4: 回款管理
    User->>+System: 进入回款管理
    System->>+PayDB: 查询待回款记录
    PayDB-->>-System: 返回待回款列表
    System-->>User: 展示回款界面
    
    alt 单笔订单回款
        User->>System: 选择单个订单回款
        User->>System: 输入回款金额和方式
        System->>System: 验证回款金额
        System->>+PayDB: 记录回款信息
        System->>+OrderDB: 更新订单回款状态
        OrderDB-->>-System: 更新成功
    else 批量回款
        User->>System: 选择多个对账单
        User->>System: 输入总回款金额
        System->>System: 按比例分配回款金额
        par 并行更新
            System->>PayDB: 批量记录回款
        and
            System->>OrderDB: 批量更新订单状态
        end
        PayDB-->>System: 记录完成
        OrderDB-->>System: 更新完成
    end
    
    System->>RecDB: 更新对账单回款状态
    PayDB-->>-System: 回款处理完成
    System-->>-User: 回款成功通知
```

### 2. 对账单创建详细时序图

```mermaid
%%{init: {'theme': 'default'}}%%
sequenceDiagram
    participant User as 业务员
    participant UI as 前端界面
    participant Controller as 控制器
    participant Service as 业务服务
    participant OrderService as 订单服务
    participant PrepayService as 预收款服务
    participant RecService as 对账服务
    participant DB as 数据库
    
    Note over User,DB: 对账单创建详细流程
    
    User->>+UI: 点击"新建对账单"
    UI->>+Controller: GET /reconciliation/create
    Controller->>+Service: 初始化创建页面
    
    par 并行加载数据
        Service->>+OrderService: 获取待对账订单
        OrderService->>+DB: 查询订单列表
        DB-->>-OrderService: 返回订单数据
        OrderService-->>-Service: 订单列表
    and
        Service->>+PrepayService: 获取客户预收款
        PrepayService->>+DB: 查询预收款余额
        DB-->>-PrepayService: 返回预收款数据
        PrepayService-->>-Service: 预收款列表
    end
    
    Service-->>-Controller: 页面初始化数据
    Controller-->>-UI: 返回创建页面数据
    UI-->>-User: 展示对账单创建界面
    
    %% 用户操作阶段
    User->>+UI: 选择订单明细
    UI->>UI: 计算选中订单金额
    UI->>User: 实时显示金额计算
    
    opt 添加手动明细
        User->>UI: 点击"添加明细"
        UI->>UI: 显示明细输入框
        User->>UI: 输入明细信息
        UI->>UI: 验证明细数据
        UI->>User: 更新明细列表
    end
    
    opt 使用预收款
        User->>UI: 选择预收款抵扣
        UI->>UI: 计算净对账金额
        UI->>User: 更新金额显示
    end
    
    User->>UI: 点击"创建对账单"
    UI->>+Controller: POST /reconciliation
    Controller->>+RecService: 创建对账单
    
    RecService->>RecService: 验证对账单数据
    RecService->>RecService: 生成对账单编号
    
    critical 数据库事务处理
        RecService->>+DB: 开始事务
        RecService->>DB: 插入对账单主表
        
        loop 处理每个明细
            RecService->>DB: 插入对账单明细
        end
        
        opt 预收款抵扣
            RecService->>PrepayService: 扣减预收款余额
            PrepayService->>DB: 更新预收款记录
        end
        
        RecService->>OrderService: 更新订单对账状态
        OrderService->>DB: 更新订单表
        
        DB->>DB: 提交事务
        DB-->>-RecService: 事务成功
    option 数据异常
        RecService->>DB: 回滚事务
        DB-->>RecService: 回滚完成
        RecService-->>Controller: 返回错误信息
    end
    
    RecService-->>-Controller: 创建成功
    Controller-->>-UI: 返回成功响应
    UI-->>-User: 显示创建成功提示
```

### 3. 开票申请审批时序图

```mermaid
%%{init: {'theme': 'base'}}%%
sequenceDiagram
    participant User as 业务员
    participant System as 系统
    participant Finance as 财务经理
    participant Workflow as 工作流引擎
    participant NotifyService as 通知服务
    participant DB as 数据库
    
    Note over User,DB: 开票申请审批流程
    
    User->>+System: 提交开票申请
    System->>+DB: 保存申请数据
    DB-->>-System: 保存成功
    
    System->>+Workflow: 启动审批流程
    Workflow->>Workflow: 创建审批任务
    Workflow->>+NotifyService: 发送审批通知
    NotifyService->>Finance: 邮件/短信通知
    NotifyService-->>-Workflow: 通知发送成功
    Workflow-->>-System: 流程启动成功
    System-->>-User: 申请提交成功
    
    %% 财务审批阶段
    Finance->>+System: 登录查看待审批任务
    System->>+DB: 查询待审批申请
    DB-->>-System: 返回申请列表
    System-->>Finance: 展示待审批列表
    
    Finance->>System: 点击审批申请
    System->>+DB: 查询申请详情
    DB-->>-System: 返回详细信息
    System-->>Finance: 展示申请详情
    
    alt 审批通过
        Finance->>+System: 点击"审批通过"
        System->>+Workflow: 完成审批任务
        Workflow->>DB: 更新申请状态为"已审批"
        Workflow->>+NotifyService: 发送通过通知
        NotifyService->>User: 审批通过通知
        NotifyService-->>-Workflow: 通知完成
        Workflow-->>-System: 审批流程完成
        
        %% 开具发票
        Finance->>System: 开具发票
        System->>+DB: 生成发票记录
        DB-->>-System: 发票创建成功
        System->>+NotifyService: 发送开票通知
        NotifyService->>User: 开票完成通知
        NotifyService-->>-System: 通知完成
        System-->>-Finance: 开票流程完成
        
    else 审批驳回
        Finance->>+System: 点击"审批驳回"
        Finance->>System: 填写驳回原因
        System->>+Workflow: 驳回审批任务
        Workflow->>DB: 更新申请状态为"已驳回"
        Workflow->>+NotifyService: 发送驳回通知
        NotifyService->>User: 审批驳回通知
        NotifyService-->>-Workflow: 通知完成
        Workflow-->>-System: 驳回流程完成
        System-->>-Finance: 驳回处理完成
        
        %% 用户重新申请
        User->>+System: 修改申请重新提交
        System->>DB: 更新申请信息
        System->>Workflow: 重新启动审批流程
        Workflow->>NotifyService: 重新发送审批通知
        NotifyService->>Finance: 新审批通知
        System-->>-User: 重新提交成功
    end
```

### 4. 批量回款处理时序图

```mermaid
%%{init: {'theme': 'base'}}%%
sequenceDiagram
    participant User as 业务员
    participant Controller as 控制器
    participant PaymentService as 回款服务
    participant RecService as 对账服务
    participant OrderService as 订单服务
    participant Calculator as 金额计算器
    participant DB as 数据库
    participant Cache as 缓存
    
    Note over User,Cache: 批量回款处理流程
    
    User->>+Controller: 进入批量回款页面
    Controller->>+PaymentService: 获取待回款数据
    PaymentService->>+DB: 查询待回款对账单
    DB-->>-PaymentService: 返回对账单列表
    PaymentService-->>-Controller: 待回款数据
    Controller-->>-User: 展示批量回款界面
    
    User->>+Controller: 选择多个对账单
    Controller->>+RecService: 获取对账单详情
    RecService->>+Cache: 查询缓存
    
    alt 缓存命中
        Cache-->>RecService: 返回缓存数据
    else 缓存未命中
        RecService->>+DB: 查询对账单详情
        DB-->>-RecService: 返回详情数据
        RecService->>Cache: 更新缓存
    end
    
    RecService-->>-Controller: 对账单详情
    Controller-->>User: 展示选中的对账单信息
    
    User->>Controller: 输入总回款金额
    Controller->>+Calculator: 计算金额分配
    Calculator->>Calculator: 按订单金额比例计算
    Calculator-->>-Controller: 返回分配结果
    Controller-->>User: 展示金额分配预览
    
    User->>+Controller: 确认批量回款
    Controller->>+PaymentService: 执行批量回款
    
    critical 批量回款事务处理
        PaymentService->>+DB: 开始分布式事务
        
        par 并行处理回款记录
            loop 每个对账单
                PaymentService->>DB: 插入回款记录
                PaymentService->>RecService: 更新对账单状态
                RecService->>DB: 更新对账单表
            end
        and 并行更新订单状态
            PaymentService->>+OrderService: 批量更新订单回款
            OrderService->>DB: 批量更新订单表
            OrderService-->>-PaymentService: 更新完成
        end
        
        PaymentService->>DB: 提交事务
        DB-->>PaymentService: 事务成功
        
    option 处理异常
        PaymentService->>DB: 回滚事务
        DB-->>PaymentService: 回滚完成
        PaymentService-->>Controller: 返回错误信息
        Controller-->>User: 显示错误提示
    end
    
    PaymentService->>Cache: 清除相关缓存
    PaymentService-->>-Controller: 批量回款成功
    Controller-->>-User: 显示成功提示
    
    %% 后续通知处理
    PaymentService->>+NotifyService: 发送回款通知
    NotifyService->>User: 回款成功通知
    NotifyService->>Finance: 财务记录通知
    NotifyService-->>-PaymentService: 通知发送完成
```

### 5. 异常处理时序图

```mermaid
%%{init: {'theme': 'dark'}}%%
sequenceDiagram
    participant User as 用户
    participant System as 系统
    participant ErrorHandler as 异常处理器
    participant Logger as 日志服务
    participant NotifyService as 通知服务
    participant DB as 数据库
    
    Note over User,DB: 系统异常处理流程
    
    User->>+System: 执行业务操作
    
    critical 业务处理
        System->>+DB: 执行数据库操作
        DB-->>System: 数据库异常
    option 连接超时
        System->>+ErrorHandler: 处理连接超时
        ErrorHandler->>+Logger: 记录超时日志
        Logger-->>-ErrorHandler: 日志记录完成
        ErrorHandler->>System: 返回友好错误信息
        ErrorHandler-->>-System: 建议重试操作
    option 数据冲突
        System->>+ErrorHandler: 处理数据冲突
        ErrorHandler->>+Logger: 记录冲突日志
        Logger-->>-ErrorHandler: 日志记录完成
        ErrorHandler->>System: 返回冲突解决方案
        ErrorHandler-->>-System: 提示用户刷新数据
    option 业务规则违反
        System->>+ErrorHandler: 处理业务异常
        ErrorHandler->>+Logger: 记录业务日志
        Logger-->>-ErrorHandler: 日志记录完成
        ErrorHandler->>System: 返回业务错误信息
        ErrorHandler-->>-System: 提供操作建议
    option 系统错误
        System->>+ErrorHandler: 处理系统异常
        ErrorHandler->>+Logger: 记录系统错误
        Logger-->>-ErrorHandler: 日志记录完成
        ErrorHandler->>+NotifyService: 发送告警通知
        NotifyService->>Admin: 系统异常告警
        NotifyService-->>-ErrorHandler: 通知发送完成
        ErrorHandler->>System: 返回系统维护提示
        ErrorHandler-->>-System: 建议稍后重试
    end
    
    System-->>-User: 返回处理结果或错误信息
    
    opt 错误恢复
        User->>+System: 根据建议重试操作
        System->>System: 重新执行业务逻辑
        System-->>-User: 返回操作结果
    end
```

## 📋 时序图说明

### 关键设计要点

1. **并行处理**：使用 `par...and...end` 语法展示并行执行的操作
2. **条件分支**：使用 `alt...else...end` 展示不同的处理路径
3. **循环处理**：使用 `loop...end` 展示批量数据处理
4. **异常处理**：使用 `critical...option...end` 展示异常处理机制
5. **可选操作**：使用 `opt...end` 展示可选的业务操作

### 时序图优势

1. **清晰展示交互**：明确显示各组件间的交互顺序
2. **异常流程可视化**：清楚展示异常情况的处理路径
3. **并发操作标识**：明确标识可以并行执行的操作
4. **时间顺序**：按照时间顺序展示业务流程
5. **责任边界**：清楚划分各组件的职责范围

### 实现注意事项

1. **事务管理**：关键业务操作需要事务保护
2. **异常处理**：完善的异常处理和回滚机制
3. **性能优化**：合理使用缓存和并行处理
4. **通知机制**：及时的业务状态通知
5. **日志记录**：完整的操作日志记录
