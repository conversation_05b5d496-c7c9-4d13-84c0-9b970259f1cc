<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CrmBusinessPaymentPlansMapper">
    
    <resultMap type="CrmBusinessPaymentPlans" id="CrmBusinessPaymentPlansResult">
        <result property="id"    column="id"    />
        <result property="managerId"    column="manager_id"    />
        <result property="paymentPlanName"    column="payment_plan_name"    />
        <result property="customerId"    column="customer_id"    />
        <result property="contractId"    column="contract_id"    />
        <result property="paymentPlanDetails"    column="payment_plan_details"    />
        <result property="paymentPlanDate"    column="payment_plan_date"    />
        <result property="paymentPlanAmount"    column="payment_plan_amount"    />
        <result property="paymentPlanMethod"    column="payment_plan_method"    />
        <result property="remarks"    column="remarks"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectCrmBusinessPaymentPlansVo">
        select id, manager_id, payment_plan_name, customer_id, contract_id, payment_plan_details, payment_plan_date, payment_plan_amount, payment_plan_method, remarks, created_at, updated_at from crm_business_payment_plans
    </sql>

    <select id="selectCrmBusinessPaymentPlansList" parameterType="CrmBusinessPaymentPlans" resultMap="CrmBusinessPaymentPlansResult">
        <include refid="selectCrmBusinessPaymentPlansVo"/>
        <where>  
            <if test="managerId != null "> and manager_id = #{managerId}</if>
            <if test="paymentPlanName != null  and paymentPlanName != ''"> and payment_plan_name like concat('%', #{paymentPlanName}, '%')</if>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="contractId != null "> and contract_id = #{contractId}</if>
            <if test="paymentPlanDetails != null  and paymentPlanDetails != ''"> and payment_plan_details = #{paymentPlanDetails}</if>
            <if test="paymentPlanDate != null "> and payment_plan_date = #{paymentPlanDate}</if>
            <if test="paymentPlanAmount != null "> and payment_plan_amount = #{paymentPlanAmount}</if>
            <if test="paymentPlanMethod != null  and paymentPlanMethod != ''"> and payment_plan_method = #{paymentPlanMethod}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectCrmBusinessPaymentPlansById" parameterType="Long" resultMap="CrmBusinessPaymentPlansResult">
        <include refid="selectCrmBusinessPaymentPlansVo"/>
        where id = #{id}
    </select>

    <insert id="insertCrmBusinessPaymentPlans" parameterType="CrmBusinessPaymentPlans" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_payment_plans
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="managerId != null">manager_id,</if>
            <if test="paymentPlanName != null and paymentPlanName != ''">payment_plan_name,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="contractId != null">contract_id,</if>
            <if test="paymentPlanDetails != null">payment_plan_details,</if>
            <if test="paymentPlanDate != null">payment_plan_date,</if>
            <if test="paymentPlanAmount != null">payment_plan_amount,</if>
            <if test="paymentPlanMethod != null">payment_plan_method,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="managerId != null">#{managerId},</if>
            <if test="paymentPlanName != null and paymentPlanName != ''">#{paymentPlanName},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="contractId != null">#{contractId},</if>
            <if test="paymentPlanDetails != null">#{paymentPlanDetails},</if>
            <if test="paymentPlanDate != null">#{paymentPlanDate},</if>
            <if test="paymentPlanAmount != null">#{paymentPlanAmount},</if>
            <if test="paymentPlanMethod != null">#{paymentPlanMethod},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateCrmBusinessPaymentPlans" parameterType="CrmBusinessPaymentPlans">
        update crm_business_payment_plans
        <trim prefix="SET" suffixOverrides=",">
            <if test="managerId != null">manager_id = #{managerId},</if>
            <if test="paymentPlanName != null and paymentPlanName != ''">payment_plan_name = #{paymentPlanName},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="paymentPlanDetails != null">payment_plan_details = #{paymentPlanDetails},</if>
            <if test="paymentPlanDate != null">payment_plan_date = #{paymentPlanDate},</if>
            <if test="paymentPlanAmount != null">payment_plan_amount = #{paymentPlanAmount},</if>
            <if test="paymentPlanMethod != null">payment_plan_method = #{paymentPlanMethod},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmBusinessPaymentPlansById" parameterType="Long">
        delete from crm_business_payment_plans where id = #{id}
    </delete>

    <delete id="deleteCrmBusinessPaymentPlansByIds" parameterType="String">
        delete from crm_business_payment_plans where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>