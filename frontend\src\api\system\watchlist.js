import request from '@/utils/request'

// 查询合同关注列表
export function listWatchlist(query) {
  return request({
    url: '/system/watchlist/list',
    method: 'get',
    params: query
  })
}

// 查询合同关注详细
export function getWatchlist(id) {
  return request({
    url: '/system/watchlist/' + id,
    method: 'get'
  })
}

// 新增合同关注
export function addWatchlist(data) {
  return request({
    url: '/system/watchlist',
    method: 'post',
    data: data
  })
}

// 修改合同关注
export function updateWatchlist(data) {
  return request({
    url: '/system/watchlist',
    method: 'put',
    data: data
  })
}

// 删除合同关注
export function delWatchlist(id) {
  return request({
    url: '/system/watchlist/' + id,
    method: 'delete'
  })
}
