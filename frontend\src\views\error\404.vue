<template>
  <div class="error-page">
    <div class="error-content">
      <h1>404</h1>
      <h2>抱歉，页面不存在</h2>
      <p>您访问的页面可能已被删除、名称已更改或暂时不可用</p>
      <div class="error-actions">
        <el-button type="primary" @click="goHome">返回首页</el-button>
        <el-button @click="goBack">返回上一页</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const goHome = () => {
  router.push('/');
};

const goBack = () => {
  router.go(-1);
};
</script>

<style scoped>
.error-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
}

.error-content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

h1 {
  font-size: 72px;
  color: #f56c6c;
  margin: 0 0 20px;
}

h2 {
  font-size: 24px;
  color: #303133;
  margin: 0 0 15px;
  font-weight: 500;
}

p {
  color: #606266;
  margin: 0 0 25px;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}
</style> 