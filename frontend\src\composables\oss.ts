export type OSSPolicy = {
  // 模拟OSS策略接口
  [key: string]: any
}

// 模拟获取OSS策略的函数
export async function getOSSPolicy(): Promise<OSSPolicy> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        key1: 'value1',
        key2: 'value2'
      })
    }, 1000)
  })
}

export async function uploadToOSS(file: File, policy: OSSPolicy): Promise<string> {
  return new Promise((resolve, reject) => {
    if (!file) {
      reject(new Error('文件对象不能为空'))
      return
    }

    // 使用 FileReader 读取文件并生成本地预览URL
    const reader = new FileReader()
    
    reader.onload = (e) => {
      const previewUrl = e.target?.result as string
      resolve(previewUrl)
    }

    reader.onerror = (e) => {
      reject(new Error(`文件读取失败: ${e.target?.error?.message || '未知错误'}`))
    }

    reader.onabort = () => {
      reject(new Error('文件读取被中止'))
    }

    try {
      reader.readAsDataURL(file)
    } catch (error) {
      const err = error as Error
      reject(new Error(`文件读取异常: ${err.message}`))
    }
  })
}
