# 测试规范

- **运行测试**: 当需要运行测试的时候，你需要在pom.xml中配置 `<skipTests>false</skipTests>`，这样才能确保测试代码被执行。
- **模拟用户**: 在测试代码中，你有两个选择：
  - 继承于 `BaseTestCase` 类，这个类中有验证登录的逻辑，可以直接使用。
  - 使用 `@WithMockUser` 注解来模拟用户登录状态，这样可以方便地进行权限相关的测试。
- **编译测试**:
  - **一定注意**: 不要动不动就整个项目test测试。直接指定测试类进行测试, 一整个测试就是5分钟，我有几个5分钟给你浪费。
  - 测试代码编译时，确保使用 `mvn clean test-compile` 命令，这样可以清理之前的编译结果并重新编译测试代码。
  - 测试代码编译时，确保使用 `mvn clean test` 命令，这样可以清理之前的编译结果并重新编译测试代码。