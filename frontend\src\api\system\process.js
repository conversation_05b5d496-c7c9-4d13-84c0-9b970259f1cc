import request from '@/utils/request'

// 查询审批处理列表
export function listProcess(query) {
  return request({
    url: '/system/process/list',
    method: 'get',
    params: query
  })
}

// 查询审批处理详细
export function getProcess(approvalId) {
  return request({
    url: '/system/process/' + approvalId,
    method: 'get'
  })
}

// 新增审批处理
export function addProcess(data) {
  return request({
    url: '/system/process',
    method: 'post',
    data: data
  })
}

// 修改审批处理
export function updateProcess(data) {
  return request({
    url: '/system/process',
    method: 'put',
    data: data
  })
}

// 删除审批处理
export function delProcess(approvalId) {
  return request({
    url: '/system/process/' + approvalId,
    method: 'delete'
  })
}
