<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM统一团队成员管理方案</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({ startOnLoad: true, theme: 'default' });
    </script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        h2 {
            margin-top: 40px;
            margin-bottom: 20px;
            border-left: 5px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            margin-top: 25px;
            margin-bottom: 15px;
            color: #2980b9;
        }
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
            background: #fff;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
        }
        .info-box {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        ul li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CRM统一团队成员管理方案</h1>

        <h2>1. 项目目标</h2>
        <div class="info-box">
            <p>为了解决当前系统仅支持单一行政层级、无法满足灵活的跨部门项目协作需求的痛点，本项目旨在引入一套全新的、独立的团队管理机制。该机制将与现有的 <code>crm_user_hierarchy</code> 行政层级体系并行，实现“行政”与“协作”两种关系的解耦，为CRM系统提供更强大、更灵活的团队协作能力。</p>
            <p><strong>核心目标：</strong></p>
            <ul>
                <li>实现有名、有主的独立团队创建与管理。</li>
                <li>支持一个用户同时加入多个团队。</li>
                <li>为未来的项目管理、任务分配和权限控制奠定坚实基础。</li>
            </ul>
        </div>

        <h2>2. 架构设计</h2>
        <h3>2.1. 数据模型设计</h3>
        <p>我们将引入两张新表：<code>crm_teams</code> (团队表) 和 <code>crm_team_members</code> (团队成员关系表)，并保留现有的 <code>crm_user_hierarchy</code> 表。</p>

        <div class="mermaid">
erDiagram
    sys_user {
        bigint user_id PK
        varchar user_name
        varchar nick_name
    }

    crm_teams {
        bigint id PK
        varchar team_name
        bigint leader_id FK
        text description
    }

    crm_team_members {
        bigint id PK
        bigint team_id FK
        bigint user_id FK
        varchar role_in_team
    }
    
    crm_user_hierarchy {
        bigint id PK
        bigint user_id FK
        bigint superior_id FK
    }

    sys_user ||--o{ crm_teams : "是负责人"
    sys_user ||--o{ crm_team_members : "是成员"
    crm_teams ||--|{ crm_team_members : "拥有"
    sys_user ||--o{ crm_user_hierarchy : "是下属"
    sys_user ||--o{ crm_user_hierarchy : "是上级"
        </div>
        
        <h3>2.2. SQL建表语句</h3>
        <h4>团队表 (crm_teams)</h4>
        <pre><code>CREATE TABLE `crm_teams` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '团队ID',
  `team_name` varchar(100) NOT NULL COMMENT '团队名称',
  `leader_id` bigint(20) DEFAULT NULL COMMENT '团队负责人ID (关联sys_user.user_id)',
  `description` text DEFAULT NULL COMMENT '团队描述',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_team_name` (`team_name`)
) ENGINE=InnoDB COMMENT='CRM团队表';</code></pre>

        <h4>团队成员表 (crm_team_members)</h4>
        <pre><code>CREATE TABLE `crm_team_members` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `team_id` bigint(20) NOT NULL COMMENT '团队ID (关联crm_teams.id)',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID (关联sys_user.user_id)',
  `role_in_team` varchar(50) DEFAULT 'member' COMMENT '在团队中的角色 (如: member, admin)',
  `join_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_team_user` (`team_id`, `user_id`)
) ENGINE=InnoDB COMMENT='CRM团队成员关系表';</code></pre>

        <h2>3. 实施计划</h2>
        <table>
            <thead>
                <tr>
                    <th>阶段</th>
                    <th>核心任务</th>
                    <th>产出物</th>
                    <th>预估时间</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>第一阶段：后端基础建设</strong></td>
                    <td>创建数据库表、实体类、Mapper和服务层接口</td>
                    <td>
                        <ul>
                            <li><code>crm_teams</code>, <code>crm_team_members</code> 表</li>
                            <li><code>CrmTeam.java</code>, <code>CrmTeamMember.java</code></li>
                            <li><code>CrmTeamMapper.java</code>, <code>CrmTeamMemberMapper.java</code> (及XML)</li>
                            <li><code>ICrmTeamService.java</code>, <code>CrmTeamServiceImpl.java</code></li>
                        </ul>
                    </td>
                    <td>2天</td>
                </tr>
                <tr>
                    <td><strong>第二阶段：后端API开发</strong></td>
                    <td>开发团队管理和成员管理的RESTful API</td>
                    <td>
                        <ul>
                            <li><code>CrmTeamController.java</code></li>
                            <li>团队CRUD接口</li>
                            <li>团队成员增删查改接口</li>
                        </ul>
                    </td>
                    <td>2天</td>
                </tr>
                <tr>
                    <td><strong>第三阶段：前端页面开发</strong></td>
                    <td>开发团队管理的UI界面</td>
                    <td>
                        <ul>
                            <li>团队管理列表页面</li>
                            <li>新建/编辑团队的对话框</li>
                            <li>团队详情页（含成员管理）</li>
                        </ul>
                    </td>
                    <td>3天</td>
                </tr>
                <tr>
                    <td><strong>第四阶段：测试与联调</strong></td>
                    <td>编写单元测试和集成测试，进行前后端联调</td>
                    <td>
                        <ul>
                            <li><code>CrmTeamServiceTest.java</code></li>
                            <li><code>CrmTeamControllerTest.java</code></li>
                            <li>Postman/Apifox测试集</li>
                        </ul>
                    </td>
                    <td>2天</td>
                </tr>
            </tbody>
        </table>

        <h2>4. 详细任务分解</h2>

        <h3>第一阶段：后端基础建设（已完成）</h3>
        <ul>
            <li><input type="checkbox" id="task1-1" checked><label for="task1-1">在数据库中执行建表SQL，创建 <code>crm_teams</code> 和 <code>crm_team_members</code> 表。</label></li>
            <li><input type="checkbox" id="task1-2" checked><label for="task1-2">创建 <code>com.ruoyi.common.domain.entity.CrmTeam</code> 实体类。</label></li>
            <li><input type="checkbox" id="task1-3" checked><label for="task1-3">创建 <code>com.ruoyi.common.domain.entity.CrmTeamMember</code> 实体类。</label></li>
            <li><input type="checkbox" id="task1-4" checked><label for="task1-4">创建 <code>com.ruoyi.common.mapper.CrmTeamMapper</code> 接口及对应的XML文件。</label></li>
            <li><input type="checkbox" id="task1-5" checked><label for="task1-5">创建 <code>com.ruoyi.common.mapper.CrmTeamMemberMapper</code> 接口及对应的XML文件。</label></li>
            <li><input type="checkbox" id="task1-6" checked><label for="task1-6">创建 <code>com.ruoyi.common.service.ICrmTeamService</code> 接口，定义团队管理的核心业务方法。</label></li>
            <li><input type="checkbox" id="task1-7" checked><label for="task1-7">创建 <code>com.ruoyi.common.service.impl.CrmTeamServiceImpl</code> 实现类。</label></li>
        </ul>

        <h3>第二阶段：后端API开发（已完成）</h3>
        <ul>
            <li><input type="checkbox" id="task2-1" checked><label for="task2-1">创建 <code>com.ruoyi.crm.controller.CrmTeamController</code> 控制器。</label></li>
            <li><input type="checkbox" id="task2-2" checked><label for="task2-2">实现创建团队的API (<code>POST /team</code>)。</label></li>
            <li><input type="checkbox" id="task2-3" checked><label for="task2-3">实现获取团队列表的API (<code>GET /team/list</code>)。</label></li>
            <li><input type="checkbox" id="task2-4" checked><label for="task2-4">实现获取单个团队详情的API (<code>GET /team/{id}</code>)。</label></li>
            <li><input type="checkbox" id="task2-5" checked><label for="task2-5">实现更新团队信息的API (<code>PUT /team</code>)。</label></li>
            <li><input type="checkbox" id="task2-6" checked><label for="task2-6">实现删除团队的API (<code>DELETE /team/{id}</code>)。</label></li>
            <li><input type="checkbox" id="task2-7" checked><label for="task2-7">实现向团队中添加成员的API (<code>POST /team/member</code>)。</label></li>
            <li><input type="checkbox" id="task2-8" checked><label for="task2-8">实现从团队中移除成员的API (<code>DELETE /team/member/{teamId}/{userId}</code>)。</label></li>
            <li><input type="checkbox" id="task2-9" checked><label for="task2-9">实现获取团队成员列表的API (<code>GET /team/{id}/members</code>)。</label></li>
        </ul>
        
        <h3>第三阶段：前端页面开发（已完成）</h3>
        <ul>
            <li><input type="checkbox" id="task3-1" checked><label for="task3-1">在前端菜单中添"团队管理"入口。</label></li>
            <li><input type="checkbox" id="task3-2" checked><label for="task3-2">创建团队管理列表页面，展示所有团队信息。</label></li>
            <li><input type="checkbox" id="task3-3" checked><label for="task3-3">实现"新建团队"按钮及弹窗表单。</label></li>
            <li><input type="checkbox" id="task3-4" checked><label for="task3-4">实现团队列表的搜索和分页功能。</label></li>
            <li><input type="checkbox" id="task3-5" checked><label for="task3-5">点击团队名称可进入团队详情页。</label></li>
            <li><input type="checkbox" id="task3-6" checked><label for="task3-6">在团队详情页中，展示团队成员列表。</label></li>
            <li><input type="checkbox" id="task3-7" checked><label for="task3-7">在详情页中，实现添加和移除团队成员的功能。</label></li>
        </ul>
        <p><strong>前端开发计划说明：</strong>前端页面已使用Vue.js框架结合Element Plus组件库完成开发。已实现团队管理列表页面、新建/编辑团队功能、团队成员管理功能，并已集成到CRM模块的路由系统中。前端开发遵循了公司现有的UI设计规范，确保与系统其他模块的风格保持一致。</p>

        <h3>第四阶段：测试与联调（进行中）</h3>
        <ul>
            <li><input type="checkbox" id="task4-1"><label for="task4-1">编写 <code>CrmTeamServiceTest.java</code> 单元测试。</label></li>
            <li><input type="checkbox" id="task4-2"><label for="task4-2">编写 <code>CrmTeamControllerTest.java</code> 集成测试。</label></li>
            <li><input type="checkbox" id="task4-3"><label for="task4-3">创建Postman/Apifox测试集，验证API功能。</label></li>
            <li><input type="checkbox" id="task4-4"><label for="task4-4">进行前后端联调测试。</label></li>
            <li><input type="checkbox" id="task4-5"><label for="task4-5">修复测试中发现的问题。</label></li>
        </ul>
        <p><strong>测试计划说明：</strong>前端和后端开发已完成，现在开始进行前后端联调和测试工作。将创建测试用例验证API功能，并进行全面的前后端联调测试，确保所有功能正常运行。发现的问题将及时修复，确保系统质量。</p>

        <h2>5. 风险评估</h2>
        <div class="info-box">
            <ul>
                <li><strong>数据权限风险</strong>：新的团队维度引入后，需要重新审视现有的数据权限逻辑。例如，一个联系人可能属于某个团队，团队成员是否应该都能看到这个联系人？这需要在后续的需求中明确。</li>
                <li><strong>逻辑复杂性</strong>：同时存在"行政层级"和"协作团队"两种关系，在未来的业务逻辑中需要清晰地区分和使用，避免混淆。</li>
            </ul>
        </div>

        <h2>6. 当前进度总结与后续计划</h2>
        <div class="info-box">
            <h3>当前进度</h3>
            <ul>
                <li><strong>第一阶段（已完成）</strong>：后端基础建设，包括数据库表、实体类、Mapper和服务层接口的创建。</li>
                <li><strong>第二阶段（已完成）</strong>：后端API开发，已完成所有团队管理和团队成员管理的API实现。</li>
                <li><strong>第三阶段（已完成）</strong>：前端页面开发，已完成团队管理列表页面、团队成员管理功能，并集成到CRM模块。</li>
            </ul>
            
            <h3>后续计划</h3>
            <ul>
                <li><strong>近期（1周内）</strong>：进行前后端联调，确保所有功能正常运行。</li>
                <li><strong>中期（2周内）</strong>：完成所有测试，修复发现的问题。</li>
                <li><strong>远期（3周内）</strong>：准备上线，编写用户文档和培训材料。</li>
            </ul>
            
            <h3>关键里程碑</h3>
            <table>
                <thead>
                    <tr>
                        <th>里程碑</th>
                        <th>计划完成时间</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>后端基础建设完成</td>
                        <td>2024-07-11</td>
                        <td>✅ 已完成</td>
                    </tr>
                    <tr>
                        <td>后端API开发完成</td>
                        <td>2024-07-15</td>
                        <td>✅ 已完成</td>
                    </tr>
                    <tr>
                        <td>前端页面开发完成</td>
                        <td>2024-07-22</td>
                        <td>✅ 已完成</td>
                    </tr>
                    <tr>
                        <td>测试与联调完成</td>
                        <td>2024-07-29</td>
                        <td>🔄 进行中</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
