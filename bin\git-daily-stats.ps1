# Git每日统计脚本
# 统计今天的提交次数、代码行数和提交内容

Write-Host "=== Git 每日代码统计 ===" -ForegroundColor Green
Write-Host "日期: $(Get-Date -Format 'yyyy-MM-dd')" -ForegroundColor Cyan
Write-Host ""

# 检查是否在Git仓库中
if (-not (Test-Path ".git")) {
    Write-Host "错误: 当前目录不是Git仓库" -ForegroundColor Red
    exit 1
}

# 获取今天的开始时间
$today = Get-Date -Format "yyyy-MM-dd"
$todayStart = "$today 00:00:00"

Write-Host "正在统计今天($today)的提交数据..." -ForegroundColor Yellow
Write-Host ""

# 1. 统计今天的提交次数
$commitCount = git log --since="$todayStart" --oneline | Measure-Object | Select-Object -ExpandProperty Count
Write-Host "📊 提交次数: $commitCount" -ForegroundColor Green

# 2. 获取今天的提交列表和详细信息
$commits = git log --since="$todayStart" --pretty=format:"%h|%an|%ad|%s" --date=format:"%H:%M:%S"

if ($commitCount -eq 0) {
    Write-Host "📝 今天还没有提交代码" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "💡 提示: 如果您今天有提交但没有显示，请检查Git的时区设置" -ForegroundColor Gray
    exit 0
}

# 3. 统计代码行数变化
Write-Host ""
Write-Host "📈 代码行数统计:" -ForegroundColor Green

$totalInsertions = 0
$totalDeletions = 0
$totalFiles = 0

# 获取每个提交的统计信息
$commitHashes = git log --since="$todayStart" --pretty=format:"%h"
foreach ($hash in $commitHashes) {
    $stats = git show --stat $hash --format=""
    foreach ($line in $stats) {
        if ($line -match "(\d+) files? changed") {
            $totalFiles += [int]$matches[1]
        }
        if ($line -match "(\d+) insertions?\(\+\)") {
            $totalInsertions += [int]$matches[1]
        }
        if ($line -match "(\d+) deletions?\(\-\)") {
            $totalDeletions += [int]$matches[1]
        }
    }
}

$netLines = $totalInsertions - $totalDeletions

Write-Host "  ✅ 新增行数: $totalInsertions" -ForegroundColor Green
Write-Host "  ❌ 删除行数: $totalDeletions" -ForegroundColor Red
Write-Host "  📊 净增行数: $netLines" -ForegroundColor $(if ($netLines -gt 0) { "Green" } elseif ($netLines -lt 0) { "Red" } else { "Yellow" })
Write-Host "  📁 影响文件: $totalFiles" -ForegroundColor Cyan

# 4. 显示提交内容列表
Write-Host ""
Write-Host "📋 提交内容列表:" -ForegroundColor Green
Write-Host "─────────────────────────────────────────────────────────────" -ForegroundColor Gray

$index = 1
foreach ($commit in $commits) {
    if ($commit) {
        $parts = $commit -split '\|'
        $hash = $parts[0]
        $author = $parts[1]
        $time = $parts[2]
        $message = $parts[3]
        
        Write-Host "$index. " -NoNewline -ForegroundColor Yellow
        Write-Host "[$hash] " -NoNewline -ForegroundColor Magenta
        Write-Host "$time " -NoNewline -ForegroundColor Gray
        Write-Host "- $message" -ForegroundColor White
        Write-Host "   作者: $author" -ForegroundColor Cyan
        
        # 显示该提交的文件变化概要
        $shortStat = git show --stat $hash --format="" | Select-Object -Last 1
        if ($shortStat -and $shortStat.Trim() -ne "") {
            Write-Host "   统计: $shortStat" -ForegroundColor Gray
        }
        Write-Host ""
        $index++
    }
}

Write-Host "─────────────────────────────────────────────────────────────" -ForegroundColor Gray

# 5. 生成简要汇总
Write-Host ""
Write-Host "📊 今日汇总:" -ForegroundColor Green
Write-Host "  🔢 总提交次数: $commitCount"
Write-Host "  📈 净增代码行: $netLines"
Write-Host "  ⏰ 统计时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

# 6. 可选：保存到文件
$saveToFile = Read-Host "`n是否保存统计结果到文件? (y/n)"
if ($saveToFile -eq 'y' -or $saveToFile -eq 'Y') {
    $fileName = "git-stats-$today.txt"
    
    # 重新运行统计并保存到文件
    @"
=== Git 每日代码统计 ===
日期: $today
统计时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

📊 提交次数: $commitCount
📈 代码行数统计:
  ✅ 新增行数: $totalInsertions
  ❌ 删除行数: $totalDeletions
  📊 净增行数: $netLines
  📁 影响文件: $totalFiles

📋 提交内容列表:
"@ | Out-File -FilePath $fileName -Encoding UTF8
    
    $index = 1
    foreach ($commit in $commits) {
        if ($commit) {
            $parts = $commit -split '\|'
            $hash = $parts[0]
            $author = $parts[1]
            $time = $parts[2]
            $message = $parts[3]
            
            "$index. [$hash] $time - $message" | Out-File -FilePath $fileName -Append -Encoding UTF8
            "   作者: $author" | Out-File -FilePath $fileName -Append -Encoding UTF8
            
            $shortStat = git show --stat $hash --format="" | Select-Object -Last 1
            if ($shortStat -and $shortStat.Trim() -ne "") {
                "   统计: $shortStat" | Out-File -FilePath $fileName -Append -Encoding UTF8
            }
            "" | Out-File -FilePath $fileName -Append -Encoding UTF8
            $index++
        }
    }
    
    Write-Host "✅ 统计结果已保存到: $fileName" -ForegroundColor Green
}

Write-Host ""
Write-Host "✨ 统计完成!" -ForegroundColor Green 