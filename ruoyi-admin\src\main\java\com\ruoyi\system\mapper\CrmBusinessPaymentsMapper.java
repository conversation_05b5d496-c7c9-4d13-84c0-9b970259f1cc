package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.CrmBusinessPayments;

/**
 * 回款Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface CrmBusinessPaymentsMapper 
{
    /**
     * 查询回款
     * 
     * @param id 回款主键
     * @return 回款
     */
    public CrmBusinessPayments selectCrmBusinessPaymentsById(Long id);

    /**
     * 查询回款列表
     * 
     * @param crmBusinessPayments 回款
     * @return 回款集合
     */
    public List<CrmBusinessPayments> selectCrmBusinessPaymentsList(CrmBusinessPayments crmBusinessPayments);

    /**
     * 新增回款
     * 
     * @param crmBusinessPayments 回款
     * @return 结果
     */
    public int insertCrmBusinessPayments(CrmBusinessPayments crmBusinessPayments);

    /**
     * 修改回款
     * 
     * @param crmBusinessPayments 回款
     * @return 结果
     */
    public int updateCrmBusinessPayments(CrmBusinessPayments crmBusinessPayments);

    /**
     * 删除回款
     * 
     * @param id 回款主键
     * @return 结果
     */
    public int deleteCrmBusinessPaymentsById(Long id);

    /**
     * 批量删除回款
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCrmBusinessPaymentsByIds(Long[] ids);
}
