# 项目相关配置
ruoyi:
  name: RuoYi
  version: 3.8.8
  copyrightYear: 2024
  # 生产环境文件路径
  profile: /home/<USER>/uploadPath
  addressEnabled: false
  captchaType: math

# 生产环境服务器配置
server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    accept-count: 1000
    threads:
      max: 800
      min-spare: 100
    # 生产环境增加额外的安全头
    remote-ip-header: X-Forwarded-For
    protocol-header: X-Forwarded-Proto

# 日志配置
logging:
  level:
    com.ruoyi: info
    org.springframework: warn
  file:
    path: /home/<USER>/logs

# Spring配置
spring:
  profiles:
    include: sharding  # 引入分表配置
  # 关闭开发者工具
  devtools:
    restart:
      enabled: false
  # redis生产环境配置
  redis:
    host: 127.0.0.1
    port: 6379
    database: 0
    password: your_redis_password    # 生产环境建议设置密码
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ${SPRING_DATASOURCE_DRUID_URL:**********************************************************************************************************************************************}
    username: ${SPRING_DATASOURCE_DRUID_USERNAME:root}
    password: ${SPRING_DATASOURCE_DRUID_PASSWORD:password}
    druid:
      # 生产环境连接池配置
      initialSize: ${SPRING_DATASOURCE_DRUID_INITIAL_SIZE:10}
      minIdle: ${SPRING_DATASOURCE_DRUID_MIN_IDLE:20}
      maxActive: ${SPRING_DATASOURCE_DRUID_MAX_ACTIVE:100}
      maxWait: 60000
      connectTimeout: 30000
      socketTimeout: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        allow: ${SPRING_DATASOURCE_DRUID_ALLOW:127.0.0.1}
        url-pattern: /druid/*
        login-username: ${SPRING_DATASOURCE_DRUID_ADMIN_USERNAME:admin}
        login-password: ${SPRING_DATASOURCE_DRUID_ADMIN_PASSWORD:admin}
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

# Token配置
token:
  header: Authorization
  secret: your_strong_token_secret_key_for_production
  expireTime: 30

# 关闭Swagger
swagger:
  enabled: false

# XSS防护
xss:
  enabled: true
  excludes: /system/notice
  urlPatterns: /system/*,/monitor/*,/tool/*

# 企业微信配置
wechatwork:
  corpId: ww7fd92d334aece720
  corpSecret: GA2vEgoZhptPp12sDXt5jU4nXL_dDKuXlhBlUmd4doc
  apiBaseUrl: https://api.wechat.com