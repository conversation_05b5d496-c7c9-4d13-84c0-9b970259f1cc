<template>
    <div class="chart-container">
        <el-select v-model="selectedMetric" placeholder="请选择" style="width: 300px; margin-top: 10px;">
            <el-option label="合同金额" value="contractAmount"></el-option>
            <!-- 可以添加其他选项 -->
        </el-select>
        <div ref="chartPerformanceComponent" class="performance-chart"></div>

        <el-table :data="tableData">
            <el-table-column prop="name" label="名称" />
            <el-table-column label="合同金额">
                <el-table-column prop="totalAmount.target" label="目标" />
                <el-table-column prop="totalAmount.completed" label="完成" />
                <el-table-column prop="totalAmount.completionRate" label="完成率" />
            </el-table-column>
            <el-table-column label="第一季度">
                <el-table-column label="1月">
                    <el-table-column prop="january.target" label="目标" />
                    <el-table-column prop="january.completed" label="完成" />
                    <el-table-column prop="january.completionRate" label="完成率" />
                </el-table-column>
                <el-table-column label="2月">
                    <el-table-column prop="february.target" label="目标" />
                    <el-table-column prop="february.completed" label="完成" />
                    <el-table-column prop="february.completionRate" label="完成率" />
                </el-table-column>
                <el-table-column label="3月">
                    <el-table-column prop="march.target" label="目标" />
                    <el-table-column prop="march.completed" label="完成" />
                    <el-table-column prop="march.completionRate" label="完成率" />
                </el-table-column>
            </el-table-column>
            <!-- 第二、三、四季度同样设置为多级表头 -->
        </el-table>
    </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    data() {
        return {
            selectedMetric: 'contractAmount',
            chart: null,
            tableData: [
                {
                    name: '3',
                    totalAmount: { target: 0, completed: 0, completionRate: '0.00' },
                    january: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    february: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    march: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    april: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    may: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    june: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    july: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    august: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    september: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    october: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    november: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    december: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                },
                {
                    name: '1',
                    totalAmount: { target: 0, completed: 0, completionRate: '0.00' },
                    january: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    february: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    march: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    april: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    may: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    june: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    july: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    august: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    september: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    october: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    november: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                    december: { target: Math.random() * 100, completed: Math.random() * 100, completionRate: (Math.random() * 100).toFixed(2) },
                }
            ],
            chartData: {
                months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                contracts: Array.from({ length: 12 }, () => Math.random() * 100),
                goals: Array.from({ length: 12 }, () => Math.random() * 100),
                completionRates: Array.from({ length: 12 }, () => (Math.random() * 100).toFixed(2)),
            }
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart();
            window.addEventListener('resize', this.handleResize);
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.chart) {
            this.chart.dispose();
            this.chart = null;
        }
    },
    methods: {
        handleResize() {
            if (this.chart) {
                this.chart.resize();
            }
        },
        initChart() {
            if (this.chart) {
                this.chart.dispose();
            }

            const chartDom = this.$refs.chartPerformanceComponent;
            if (!chartDom) return;

            this.chart = echarts.init(chartDom);
            const options = {
                title: {
                    text: '业务目标完成情况',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    data: this.chartData.months,
                },
                yAxis: {
                    type: 'value',
                },
                series: [
                    {
                        name: '合同金额',
                        data: this.chartData.contracts,
                        type: 'line',
                    },
                    {
                        name: '目标',
                        data: this.chartData.goals,
                        type: 'line',
                    },
                    {
                        name: '完成率',
                        data: this.chartData.completionRates,
                        type: 'line',
                    },
                ],
            };
            this.chart.setOption(options);
        }
    },
};
</script>

<style scoped>
.chart-container {
    width: 100%;
    height: 100%;
}
.performance-chart {
    width: 100%;
    height: 400px;
    margin-top: 20px;
}
</style>