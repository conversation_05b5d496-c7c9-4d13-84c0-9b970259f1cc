package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.CrmBusinessOpportunities;

/**
 * 商机Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface ICrmBusinessOpportunitiesService 
{
    /**
     * 查询商机
     * 
     * @param id 商机主键
     * @return 商机
     */
    public CrmBusinessOpportunities selectCrmBusinessOpportunitiesById(Long id);

    /**
     * 查询商机列表
     * 
     * @param crmBusinessOpportunities 商机
     * @return 商机集合
     */
    public List<CrmBusinessOpportunities> selectCrmBusinessOpportunitiesList(CrmBusinessOpportunities crmBusinessOpportunities);

    /**
     * 新增商机
     * 
     * @param crmBusinessOpportunities 商机
     * @return 结果
     */
    public int insertCrmBusinessOpportunities(CrmBusinessOpportunities crmBusinessOpportunities);

    /**
     * 修改商机
     * 
     * @param crmBusinessOpportunities 商机
     * @return 结果
     */
    public int updateCrmBusinessOpportunities(CrmBusinessOpportunities crmBusinessOpportunities);

    /**
     * 批量删除商机
     * 
     * @param ids 需要删除的商机主键集合
     * @return 结果
     */
    public int deleteCrmBusinessOpportunitiesByIds(Long[] ids);

    /**
     * 删除商机信息
     * 
     * @param id 商机主键
     * @return 结果
     */
    public int deleteCrmBusinessOpportunitiesById(Long id);
}
