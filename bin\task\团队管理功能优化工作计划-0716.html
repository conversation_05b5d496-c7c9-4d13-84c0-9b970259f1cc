<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM团队管理功能优化工作计划</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif; line-height: 1.6; color: #333; max-width: 960px; margin: 20px auto; padding: 0 20px; }
        h1, h2, h3 { color: #2c3e50; border-bottom: 2px solid #eaecef; padding-bottom: 0.3em; }
        h1 { font-size: 2em; }
        h2 { font-size: 1.5em; }
        h3 { font-size: 1.2em; }
        code { background-color: #f6f8fa; padding: 0.2em 0.4em; margin: 0; font-size: 85%; border-radius: 3px; }
        pre { background-color: #f6f8fa; padding: 16px; overflow: auto; border-radius: 6px; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 1em; }
        th, td { border: 1px solid #dfe2e5; padding: 8px 12px; }
        th { background-color: #f6f8fa; font-weight: bold; }
        .task-list { list-style-type: none; padding-left: 0; }
        .task-list-item { margin-bottom: 0.5em; }
        .task-list-item input { margin-right: 0.5em; }
        .label { display: inline-block; padding: .2em .6em .3em; font-size: 75%; font-weight: 700; line-height: 1; color: #fff; text-align: center; white-space: nowrap; vertical-align: baseline; border-radius: .25em; }
        .label-backend { background-color: #3498db; }
        .label-frontend { background-color: #2ecc71; }
        .label-db { background-color: #f39c12; }
        .status { font-weight: bold; }
        .status-pending { color: #e67e22; }
        .status-done { color: #27ae60; }
        .section { border: 1px solid #ddd; padding: 15px; border-radius: 8px; margin-bottom: 20px; background-color: #fff; }
    </style>
</head>
<body>

    <h1>CRM团队管理功能优化工作计划</h1>
    <p><strong>日期:</strong> 2025年7月16日</p>
    <p><strong>负责人:</strong> Cline</p>
    <p><strong>状态:</strong> <span class="status status-pending">规划中</span></p>

    <h2>概述</h2>
    <p>本次工作旨在解决当前团队管理模块存在的两个核心问题：一是团队负责人身份在成员列表中不显示；二是关联对象功能缺失，无法将团队与商机、联系人等业务实体进行关联。本计划将详细分解任务，明确前后端职责，确保功能完整、稳定地交付。</p>

    <div class="section">
        <h2>问题一：团队成员身份显示缺失</h2>
        <h3>1.1 问题描述</h3>
        <p>在创建团队并指定负责人后，进入团队详情页查看成员列表时，该负责人的“身份”字段显示为空。期望行为是正确显示“负责人”。</p>

        <h3>1.2 目标</h3>
        <p>确保团队成员列表能准确、清晰地展示每个成员的身份，特别是“负责人”身份。</p>

        <h3>1.3 任务分解</h3>
        <table id="task-table-1">
            <thead>
                <tr>
                    <th>模块</th>
                    <th>任务项</th>
                    <th>技术方案/关键点</th>
                    <th>负责人</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="label label-backend">后端</span></td>
                    <td>检查并修复团队成员列表接口</td>
                    <td>确认 <code>/crm/team/listMembers</code> (或类似接口) 的返回数据中是否包含成员身份（<code>member_role</code>）字段。如果缺失，需要修改Mapper查询和Service逻辑，确保能正确关联并返回身份信息。</td>
                    <td>Cline</td>
                    <td><span class="status status-pending">待办</span></td>
                </tr>
                <tr>
                    <td><span class="label label-backend">后端</span></td>
                    <td>确认团队创建逻辑</td>
                    <td>验证创建团队时，指定的负责人信息是否已正确存入 <code>crm_team_member</code> 表，并且 <code>member_role</code> 字段被设置为'负责人'或对应的枚举值。</td>
                    <td>Cline</td>
                    <td><span class="status status-pending">待办</span></td>
                </tr>
                <tr>
                    <td><span class="label label-frontend">前端</span></td>
                    <td>修改团队成员列表页面</td>
                    <td>在Vue组件中，检查表格列的定义，确保正确绑定并展示后端返回的身份字段。可能需要调整 <code>el-table-column</code> 的 <code>prop</code> 属性。</td>
                    <td>Cline</td>
                    <td><span class="status status-pending">待办</span></td>
                </tr>
                 <tr>
                    <td><span class="label label-backend">测试</span></td>
                    <td>编写集成测试</td>
                    <td>为团队成员列表接口编写或更新集成测试，断言返回结果中包含正确的身份信息。</td>
                    <td>Cline</td>
                    <td><span class="status status-pending">待办</span></td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>问题二：关联对象业务逻辑缺失</h2>
        <h3>2.1 问题描述</h3>
        <p>“关联对象”功能目前仅能展示不同业务模块（如联系人、商机）的列表，但缺少将列表中的具体条目与当前团队进行关联的操作机制。</p>

        <h3>2.2 目标</h3>
        <p>实现完整的关联对象功能，允许用户选择一个业务模块，从列表中选择一个或多个条目，并将其与当前团队建立双向关联关系。</p>

        <h3>2.3 任务分解</h3>
        <table id="task-table-2">
            <thead>
                <tr>
                    <th>模块</th>
                    <th>任务项</th>
                    <th>技术方案/关键点</th>
                    <th>负责人</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="label label-db">数据库</span></td>
                    <td>设计并创建关联关系表</td>
                    <td>创建新表 <code>crm_team_relation</code> 用于存储团队与其他业务对象的关系。字段应包括：<code>id</code>, <code>team_id</code>, <code>relation_type</code> (如 'contact', 'opportunity'), <code>relation_id</code>, <code>create_time</code> 等。</td>
                    <td>Cline</td>
                    <td><span class="status status-pending">待办</span></td>
                </tr>
                <tr>
                    <td><span class="label label-backend">后端</span></td>
                    <td>开发关联关系API</td>
                    <td>
                        创建新的Controller和Service来处理关联逻辑：
                        <ul>
                            <li><code>POST /crm/team/relation</code>: 批量创建关联关系。</li>
                            <li><code>DELETE /crm/team/relation</code>: 批量删除关联关系。</li>
                            <li><code>GET /crm/team/relation/{teamId}</code>: 获取指定团队的所有关联对象。</li>
                        </ul>
                    </td>
                    <td>Cline</td>
                    <td><span class="status status-pending">待办</span></td>
                </tr>
                <tr>
                    <td><span class="label label-frontend">前端</span></td>
                    <td>实现关联对象选择器UI</td>
                    <td>
                        在关联对象弹窗的列表中，增加操作列或复选框。
                        <ul>
                            <li>使用 <code>el-table</code> 的 <code>selection-change</code> 事件来跟踪用户选择。</li>
                            <li>添加“确认关联”按钮。</li>
                        </ul>
                    </td>
                    <td>Cline</td>
                    <td><span class="status status-pending">待办</span></td>
                </tr>
                <tr>
                    <td><span class="label label-frontend">前端</span></td>
                    <td>实现关联逻辑</td>
                    <td>
                        点击“确认关联”按钮后，调用后端创建关联关系的API。
                        <ul>
                            <li>获取当前团队ID和用户选择的对象ID列表。</li>
                            <li>发送POST请求到 <code>/crm/team/relation</code>。</li>
                            <li>成功后关闭弹窗并刷新团队详情页的关联列表。</li>
                        </ul>
                    </td>
                    <td>Cline</td>
                    <td><span class="status status-pending">待办</span></td>
                </tr>
                <tr>
                    <td><span class="label label-frontend">前端</span></td>
                    <td>展示已关联对象</td>
                    <td>在团队详情页，增加一个区域（如Tab或Card），用于展示已关联的对象列表。调用 <code>GET /crm/team/relation/{teamId}</code> 接口获取数据。</td>
                    <td>Cline</td>
                    <td><span class="status status-pending">待办</span></td>
                </tr>
                <tr>
                    <td><span class="label label-backend">测试</span></td>
                    <td>编写完整的集成测试</td>
                    <td>为新的关联关系API编写全面的CRUD集成测试，覆盖各种边界情况。</td>
                    <td>Cline</td>
                    <td><span class="status status-pending">待办</span></td>
                </tr>
            </tbody>
        </table>
    </div>

    <h2>验收标准</h2>
    <ul class="task-list">
        <li class="task-list-item"><input type="checkbox" disabled> <strong>问题一：</strong> 在团队成员列表中，负责人的“身份”列明确显示为“负责人”。</li>
        <li class="task-list-item"><input type="checkbox" disabled> <strong>问题二：</strong> 用户可以从“关联对象”弹窗中选择联系人或商机，并成功将其与团队关联。</li>
        <li class="task-list-item"><input type="checkbox" disabled> <strong>问题二：</strong> 在团队详情页，能够清晰地看到所有已关联的对象列表，并可以取消关联。</li>
        <li class="task-list-item"><input type="checkbox" disabled> <strong>通用：</strong> 所有新功能都有对应的单元测试和集成测试覆盖。</li>
        <li class="task-list-item"><input type="checkbox" disabled> <strong>通用：</strong> 功能操作流畅，无明显BUG，符合现有UI/UX风格。</li>
    </ul>

</body>
</html>
