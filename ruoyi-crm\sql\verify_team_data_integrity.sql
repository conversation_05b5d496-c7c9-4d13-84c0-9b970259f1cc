-- ========================================
-- 团队分配数据完整性验证脚本
-- ========================================

-- 1. 检查表是否存在
SELECT 
    table_name AS '表名',
    table_comment AS '表注释',
    table_rows AS '记录数'
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
  AND table_name IN ('crm_teams', 'crm_team_relation', 'crm_team_members')
ORDER BY table_name;

-- 2. 检查团队表数据
SELECT 
    COUNT(*) as '团队总数',
    COUNT(CASE WHEN status = '0' THEN 1 END) as '正常团队数',
    COUNT(CASE WHEN leader_id IS NOT NULL THEN 1 END) as '有负责人的团队数'
FROM crm_teams;

-- 3. 检查团队关联表数据
SELECT 
    COUNT(*) as '关联记录总数',
    COUNT(DISTINCT team_id) as '涉及团队数',
    COUNT(DISTINCT CONCAT(relation_type, ':', relation_id)) as '涉及业务对象数'
FROM crm_team_relation;

-- 4. 按业务类型统计关联数据
SELECT 
    relation_type as '业务类型',
    COUNT(*) as '关联数量',
    COUNT(DISTINCT team_id) as '涉及团队数',
    COUNT(DISTINCT relation_id) as '涉及业务对象数'
FROM crm_team_relation
GROUP BY relation_type
ORDER BY COUNT(*) DESC;

-- 5. 检查外键完整性 - 团队关联表中的团队ID是否都存在于团队表中
SELECT 
    '团队关联表中的无效团队ID' as '检查项',
    COUNT(*) as '问题数量'
FROM crm_team_relation tr
LEFT JOIN crm_teams t ON tr.team_id = t.id
WHERE t.id IS NULL;

-- 6. 检查团队负责人数据完整性
SELECT 
    '团队表中的无效负责人ID' as '检查项',
    COUNT(*) as '问题数量'
FROM crm_teams t
LEFT JOIN sys_user u ON t.leader_id = u.user_id
WHERE t.leader_id IS NOT NULL AND u.user_id IS NULL;

-- 7. 测试关联查询 - 验证修复后的查询是否正常工作
SELECT 
    tr.id,
    tr.team_id,
    tr.relation_type,
    tr.relation_id,
    t.team_name,
    IFNULL(u.nick_name, '--') as leader_name,
    tr.create_time
FROM crm_team_relation tr
LEFT JOIN crm_teams t ON tr.team_id = t.id
LEFT JOIN sys_user u ON t.leader_id = u.user_id
LIMIT 5;

-- 8. 检查是否有重复的业务对象分配
SELECT 
    relation_type,
    relation_id,
    COUNT(*) as '分配次数',
    GROUP_CONCAT(team_id) as '分配的团队ID'
FROM crm_team_relation
GROUP BY relation_type, relation_id
HAVING COUNT(*) > 1;

-- 9. 检查团队成员数据
SELECT 
    COUNT(*) as '团队成员总数',
    COUNT(CASE WHEN status = '0' THEN 1 END) as '正常成员数',
    COUNT(DISTINCT team_id) as '涉及团队数',
    COUNT(DISTINCT user_id) as '涉及用户数'
FROM crm_team_members;

-- 10. 检查团队成员表中的外键完整性
SELECT 
    '团队成员表中的无效团队ID' as '检查项',
    COUNT(*) as '问题数量'
FROM crm_team_members tm
LEFT JOIN crm_teams t ON tm.team_id = t.id
WHERE t.id IS NULL;

SELECT 
    '团队成员表中的无效用户ID' as '检查项',
    COUNT(*) as '问题数量'
FROM crm_team_members tm
LEFT JOIN sys_user u ON tm.user_id = u.user_id
WHERE u.user_id IS NULL;

-- 11. 示例：查看具体的团队分配情况
SELECT 
    '联系人团队分配示例' as '说明',
    tr.relation_id as '联系人ID',
    t.team_name as '分配的团队',
    u.nick_name as '团队负责人',
    tr.create_time as '分配时间'
FROM crm_team_relation tr
LEFT JOIN crm_teams t ON tr.team_id = t.id
LEFT JOIN sys_user u ON t.leader_id = u.user_id
WHERE tr.relation_type = 'CONTACT'
LIMIT 3;

-- 12. 检查是否有孤立的团队（没有成员的团队）
SELECT 
    t.id as '团队ID',
    t.team_name as '团队名称',
    t.leader_id as '负责人ID',
    IFNULL(u.nick_name, '--') as '负责人姓名'
FROM crm_teams t
LEFT JOIN sys_user u ON t.leader_id = u.user_id
LEFT JOIN crm_team_members tm ON t.id = tm.team_id AND tm.status = '0'
WHERE tm.team_id IS NULL
  AND t.status = '0'
ORDER BY t.id;
