<template>
  <el-container class="business-opportunity-management">
    <!-- 主内容区域容器 -->
    <el-container class="main-container">
      <!-- 页面头部，包含标题和操作按钮 -->
      <el-header class="header">
        <h1>商机管理</h1>
        <div class="header-actions">
          <el-button 
            type="primary" 
            size="small"
            @click="openOpportunityDialog"
            class="action-btn primary-btn"
          >
            <el-icon><Plus /></el-icon>
            新建商机
          </el-button>
          <el-button 
            type="primary"
            plain
            size="small"
            @click="handleExport"
            class="action-btn"  
          >
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
      </el-header>

      <el-main class="main-content">
        <!-- 搜索和筛选组件 -->
        <CommonFilter
          :config="opportunityFilterConfig"
          v-model:filter-type="filterType"
          v-model:search-input="searchInput"
          @search="handleSearch"
          @filter-change="handleFilterChange"
          class="filter-section"
        />

        <!-- 商机数据表格 -->
        <div class="table-container">
          <el-table
            v-loading="loading"
            :data="opportunities"
            border
            class="opportunities-table"
            @sort-change="handleSortChange"
          >
      <el-table-column prop="name" label="商机名称" sortable="custom" min-width="150" />
      <el-table-column prop="customerName" label="所属客户" min-width="150" />
      <el-table-column prop="stage" label="商机阶段" min-width="120">
        <template #default="{ row }">
          <el-tag :type="(getStageTagType(row.stage) as any)">{{ getStageLabel(row.stage) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="预计金额" sortable="custom" min-width="120">
        <template #default="{ row }">
          {{ formatAmount(row.amount) }}
        </template>
      </el-table-column>
      <el-table-column prop="probability" label="成交概率" sortable="custom" min-width="120">
        <template #default="{ row }">
          <el-progress :percentage="row.probability" />
        </template>
      </el-table-column>
      <el-table-column prop="expectedClosingDate" label="预计成交日期" sortable="custom" min-width="120" />
      <el-table-column prop="ownerName" label="负责人" min-width="120" />
      <el-table-column prop="createTime" label="创建时间" sortable="custom" min-width="160" />
      <el-table-column fixed="right" label="操作" width="280">
        <template #default="{ row }">
          <el-button link type="primary" @click="openDrawer(row)">查看</el-button>
          <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
          <!-- 团队分配按钮 -->
          <TeamAssignButton
            :biz-id="row.id"
            :biz-name="row.name"
            biz-type="OPPORTUNITY"
            button-link
            size="small"
            text="分配"
            show-current-team
            @success="handleTeamAssignSuccess"
          />
          <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
          </el-table>
        </div>
        
        <!-- 分页组件 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :total="totalOpportunities"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-main>
    </el-container>

    <CommonFormDialog
      v-model="opportunityDialogVisible"
      :title="dialogTitle"
      :config="newOpportunityFormConfig"
      :form-data="currentOpportunity"
      @cancel="cancelOpportunity"
      @submit="handleOpportunitySubmit"
    />

    <el-drawer
      v-model="drawerVisible"
      :title="'商机详情'"
      size="50%"
    >
      <OpportunityDetail
        v-if="drawerVisible"
        :opportunity="currentOpportunity"
        @update="handleOpportunityUpdate"
      />
    </el-drawer>
  </el-container>
</template>

<script setup lang="ts">
import TeamAssignButton from '@/components/TeamAssignButton.vue';
import type { FilterType, FormData, OpportunityData, QueryParams } from '@/types';
import { Download, Plus } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, ref } from 'vue';
import {
  addOpportunity,
  deleteOpportunities,
  exportOpportunities,
  getOpportunity,
  listOpportunities,
  updateOpportunity
} from './api';
import { opportunityFilterConfig } from './config/filterConfig';
import { newOpportunityFormConfig } from './config/formConfig';

// 状态数据
const loading = ref(false);
const filterType = ref<FilterType>('all');
const searchInput = ref('');
const totalOpportunities = ref(0);
const opportunities = ref<OpportunityData[]>([]);
const queryParams = ref<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  sortField: '',
  sortOrder: '',
  keyword: '',
  filterType: 'all'
});

// 弹窗控制
const opportunityDialogVisible = ref(false);
const drawerVisible = ref(false);
const dialogTitle = ref('新建商机');
const currentOpportunity = ref<FormData>({});

// 获取商机列表
const getList = async () => {
  try {
    loading.value = true;
    const res = await listOpportunities(queryParams.value);
    opportunities.value = res?.rows || [];
    totalOpportunities.value = res?.total || 0;
    console.log("====== " + JSON.stringify(res, null, 2));
  } catch (error) {
    console.error('获取商机列表失败:', error);
    ElMessage.error('获取商机列表失败');
  } finally {
    loading.value = false;
  }
};

// 商机表单处理
const openOpportunityDialog = () => {
  dialogTitle.value = '新建商机';
  currentOpportunity.value = {};
  opportunityDialogVisible.value = true;
};

const cancelOpportunity = () => {
  opportunityDialogVisible.value = false;
  currentOpportunity.value = {};
};

const handleOpportunitySubmit = async (formData: FormData) => {
  try {
    if (formData.id) {
      await updateOpportunity(formData as OpportunityData);
      ElMessage.success('更新商机成功');
    } else {
      await addOpportunity(formData);
      ElMessage.success('创建商机成功');
    }
    opportunityDialogVisible.value = false;
    getList();
  } catch (error) {
    console.error('保存商机失败:', error);
    ElMessage.error('保存商机失败');
  }
};

// 商机详情抽屉
const openDrawer = async (row: OpportunityData) => {
  try {
    const res = await getOpportunity(row.id);
    if (res && res.data) {
      currentOpportunity.value = res.data as FormData;
      drawerVisible.value = true;
    } else {
      ElMessage.error('未获取到商机详情数据');
    }
  } catch (error) {
    console.error('获取商机详情失败:', error);
    ElMessage.error('获取商机详情失败');
  }
};

const handleOpportunityUpdate = () => {
  getList();
};

// 编辑商机
const handleEdit = (row: OpportunityData) => {
  dialogTitle.value = '编辑商机';
  currentOpportunity.value = { ...row };
  opportunityDialogVisible.value = true;
};

// 删除商机
const handleDelete = (row: OpportunityData) => {
  ElMessageBox.confirm('确认删除该商机吗？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteOpportunities([row.id]);
      ElMessage.success('删除成功');
      getList();
    } catch (error) {
      console.error('删除商机失败:', error);
      ElMessage.error('删除商机失败');
    }
  }).catch(() => {});
};

// 导出商机
const handleExport = async () => {
  try {
    await exportOpportunities(queryParams.value);
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出商机失败:', error);
    ElMessage.error('导出商机失败');
  }
};

// 表格操作
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  queryParams.value.sortField = prop;
  queryParams.value.sortOrder = order === 'ascending' ? 'asc' : 'desc';
  getList();
};

const handleSizeChange = (val: number) => {
  queryParams.value.pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  queryParams.value.pageNum = val;
  getList();
};

// 筛选操作
const handleSearch = (value: string) => {
  queryParams.value.keyword = value;
  queryParams.value.pageNum = 1;
  getList();
};

const handleFilterChange = (type: FilterType) => {
  queryParams.value.filterType = type;
  queryParams.value.pageNum = 1;
  getList();
};

// 工具函数
const getStageLabel = (stage: string) => {
  const stageMap: Record<string, string> = {
    initial: '初步接触',
    requirement: '需求确认',
    solution: '方案制定',
    negotiation: '商务谈判',
    contract: '合同签订'
  };
  return stageMap[stage] || stage;
};

const getStageTagType = (stage: string) => {
  const typeMap: Record<string, string> = {
    initial: 'primary',
    requirement: 'info',
    solution: 'warning',
    negotiation: 'success',
    contract: 'danger'
  };
  return typeMap[stage] || 'primary';
};

const formatAmount = (amount: number) => {
  return amount?.toLocaleString('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }) || '¥0.00';
};

/**
 * 处理团队分配成功
 */
const handleTeamAssignSuccess = (): void => {
  ElMessage.success('团队分配成功');
  // 刷新列表以更新团队信息
  getList();
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.business-opportunity-management {
  // padding: 20px;

  .table-operations {
    margin-bottom: 16px;
    text-align: right;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
/* 商机管理容器样式 */
.business-opportunity-management {
    background-color: #fff;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 主容器样式 */
.main-container {

    padding: 0 20px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    box-shadow: none;
    border-bottom: 1px solid #f0f0f0;
    height: 56px;
    flex-shrink: 0;
}

.header h1 {
    font-weight: 500;
    font-size: 18px;
    color: #303133;
    margin: 0;
}

/* 头部操作区域样式 */
.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 400;
    font-size: var(--ep-font-size-base);
    transition: all 0.2s ease;
}

.action-btn .el-icon {
    margin-right: 5px;
    font-size: var(--ep-font-size-base);
}

.primary-btn {
    font-weight: 500;
}

/* 主内容区域 */
.main-content {

    display: flex;
    flex-direction: column;
    padding: 16px 20px 16px;
    overflow: hidden;
    min-height: 0;
}

/* 筛选区域样式 */
.filter-section {
    flex-shrink: 0;
    margin-bottom: 16px;
}

/* 表格容器样式 */
.table-container {
 
    display: flex;
    flex-direction: column;
    min-height: 0;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 16px;
}

/* 表格样式 */
.opportunities-table {
    flex: 1;
    border-radius: 8px;
}

/* 分页区域样式 */
.pagination-section {
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    padding: 12px 0;
    border-top: 1px solid #f0f2f5;
}
</style>
