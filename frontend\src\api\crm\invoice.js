import request from '@/utils/request'

// 查询发票列表
export function listInvoice(query) {
  return request({
    url: '/crm/invoice/list',
    method: 'get',
    params: query
  })
}

// 查询发票详细
export function getInvoice(id) {
  return request({
    url: '/crm/invoice/' + id,
    method: 'get'
  })
}

// 根据发票编号查询发票
export function getInvoiceByNo(invoiceNo) {
  return request({
    url: '/crm/invoice/invoiceNo/' + invoiceNo,
    method: 'get'
  })
}

// 根据报价单ID查询发票列表
export function getInvoicesByQuotationId(quotationId) {
  return request({
    url: '/crm/invoice/quotation/' + quotationId,
    method: 'get'
  })
}

// 新增发票
export function addInvoice(data) {
  return request({
    url: '/crm/invoice',
    method: 'post',
    data: data
  })
}

// 修改发票
export function updateInvoice(data) {
  return request({
    url: '/crm/invoice',
    method: 'put',
    data: data
  })
}

// 删除发票
export function delInvoice(ids) {
  return request({
    url: '/crm/invoice/' + ids,
    method: 'delete'
  })
}

// 从报价单创建发票
export function createInvoiceFromQuotation(quotationId) {
  return request({
    url: '/crm/invoice/createFromQuotation/' + quotationId,
    method: 'post'
  })
}

// 提交发票审批
export function submitInvoiceApproval(id) {
  return request({
    url: '/crm/invoice/submit/' + id,
    method: 'post'
  })
}

// 审批发票
export function approveInvoice(data) {
  return request({
    url: '/crm/invoice/approve',
    method: 'post',
    data: data
  })
}

// 开票
export function issueInvoice(id) {
  return request({
    url: '/crm/invoice/issue/' + id,
    method: 'post'
  })
}

// 作废发票
export function cancelInvoice(id, data) {
  return request({
    url: '/crm/invoice/cancel/' + id,
    method: 'post',
    data: data
  })
}

// 生成发票编号
export function generateInvoiceNo() {
  return request({
    url: '/crm/invoice/generateNo',
    method: 'get'
  })
}