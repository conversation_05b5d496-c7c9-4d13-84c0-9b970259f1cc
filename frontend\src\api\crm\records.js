import request from '@/utils/request'

// 查询跟进记录列表
export function listRecords(query) {
  return request({
    url: '/crm/records/list',
    method: 'get',
    params: query
  })
}

// 查询跟进记录详细
export function getRecords(id) {
  return request({
    url: '/crm/records/' + id,
    method: 'get'
  })
}

// 新增跟进记录
export function addRecords(data) {
  return request({
    url: '/crm/records',
    method: 'post',
    data: data
  })
}

// 修改跟进记录
export function updateRecords(data) {
  return request({
    url: '/crm/records',
    method: 'put',
    data: data
  })
}

// 删除跟进记录
export function delRecords(id) {
  return request({
    url: '/crm/records/' + id,
    method: 'delete'
  })
}
