package com.ruoyi.system.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.domain.entity.CrmBusinessApMyApplication;
import com.ruoyi.system.service.ICrmBusinessApMyApplicationService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 我的申请Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Api(tags = "我的申请管理")
@RestController
@RequestMapping("/system/application")
public class CrmBusinessApMyApplicationController extends BaseController {
    @Autowired
    private ICrmBusinessApMyApplicationService crmBusinessApMyApplicationService;

    /**
     * 查询我的申请列表
     */
    @ApiOperation("查询我的申请列表")
    @PreAuthorize("@ss.hasPermi('system:application:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusinessApMyApplication crmBusinessApMyApplication) {
        startPage();
        List<CrmBusinessApMyApplication> list = crmBusinessApMyApplicationService
                .selectCrmBusinessApMyApplicationList(crmBusinessApMyApplication);
        return getDataTable(list);
    }

    /**
     * 导出我的申请列表
     */
    @ApiOperation("导出我的申请列表")
    @PreAuthorize("@ss.hasPermi('system:application:export')")
    @Log(title = "我的申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmBusinessApMyApplication crmBusinessApMyApplication) {
        List<CrmBusinessApMyApplication> list = crmBusinessApMyApplicationService
                .selectCrmBusinessApMyApplicationList(crmBusinessApMyApplication);
        ExcelUtil<CrmBusinessApMyApplication> util = new ExcelUtil<>(CrmBusinessApMyApplication.class);
        util.exportExcel(response, list, "我的申请数据");
    }

    /**
     * 获取我的申请详细信息
     */
    @ApiOperation("获取我的申请详细信息")
    @PreAuthorize("@ss.hasPermi('system:application:query')")
    @GetMapping(value = "/{applicationId}")
    public AjaxResult getInfo(@ApiParam("我的申请ID") @PathVariable("applicationId") Long applicationId) {
        return success(
                crmBusinessApMyApplicationService.selectCrmBusinessApMyApplicationByApplicationId(applicationId));
    }

    /**
     * 新增我的申请
     */
    @ApiOperation("新增我的申请")
    @PreAuthorize("@ss.hasPermi('system:application:add')")
    @Log(title = "我的申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("我的申请信息") @RequestBody CrmBusinessApMyApplication crmBusinessApMyApplication) {
        return toAjax(crmBusinessApMyApplicationService.insertCrmBusinessApMyApplication(crmBusinessApMyApplication));
    }

    /**
     * 修改我的申请
     */
    @ApiOperation("修改我的申请")
    @PreAuthorize("@ss.hasPermi('system:application:edit')")
    @Log(title = "我的申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("我的申请信息") @RequestBody CrmBusinessApMyApplication crmBusinessApMyApplication) {
        return toAjax(crmBusinessApMyApplicationService.updateCrmBusinessApMyApplication(crmBusinessApMyApplication));
    }

    /**
     * 删除我的申请
     */
    @ApiOperation("删除我的申请")
    @PreAuthorize("@ss.hasPermi('system:application:remove')")
    @Log(title = "我的申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{applicationIds}")
    public AjaxResult remove(@ApiParam("我的申请ID数组") @PathVariable Long[] applicationIds) {
        return toAjax(
                crmBusinessApMyApplicationService.deleteCrmBusinessApMyApplicationByApplicationIds(applicationIds));
    }
}
