package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CrmBusinessApMyApplicationMapper;
import com.ruoyi.common.domain.entity.CrmBusinessApMyApplication;
import com.ruoyi.system.service.ICrmBusinessApMyApplicationService;

/**
 * 我的申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class CrmBusinessApMyApplicationServiceImpl implements ICrmBusinessApMyApplicationService 
{
    @Autowired
    private CrmBusinessApMyApplicationMapper crmBusinessApMyApplicationMapper;

    /**
     * 查询我的申请
     * 
     * @param applicationId 我的申请主键
     * @return 我的申请
     */
    @Override
    public CrmBusinessApMyApplication selectCrmBusinessApMyApplicationByApplicationId(Long applicationId)
    {
        return crmBusinessApMyApplicationMapper.selectCrmBusinessApMyApplicationByApplicationId(applicationId);
    }

    /**
     * 查询我的申请列表
     * 
     * @param crmBusinessApMyApplication 我的申请
     * @return 我的申请
     */
    @Override
    public List<CrmBusinessApMyApplication> selectCrmBusinessApMyApplicationList(CrmBusinessApMyApplication crmBusinessApMyApplication)
    {
        return crmBusinessApMyApplicationMapper.selectCrmBusinessApMyApplicationList(crmBusinessApMyApplication);
    }

    /**
     * 新增我的申请
     * 
     * @param crmBusinessApMyApplication 我的申请
     * @return 结果
     */
    @Override
    public int insertCrmBusinessApMyApplication(CrmBusinessApMyApplication crmBusinessApMyApplication)
    {
        return crmBusinessApMyApplicationMapper.insertCrmBusinessApMyApplication(crmBusinessApMyApplication);
    }

    /**
     * 修改我的申请
     * 
     * @param crmBusinessApMyApplication 我的申请
     * @return 结果
     */
    @Override
    public int updateCrmBusinessApMyApplication(CrmBusinessApMyApplication crmBusinessApMyApplication)
    {
        return crmBusinessApMyApplicationMapper.updateCrmBusinessApMyApplication(crmBusinessApMyApplication);
    }

    /**
     * 批量删除我的申请
     * 
     * @param applicationIds 需要删除的我的申请主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessApMyApplicationByApplicationIds(Long[] applicationIds)
    {
        return crmBusinessApMyApplicationMapper.deleteCrmBusinessApMyApplicationByApplicationIds(applicationIds);
    }

    /**
     * 删除我的申请信息
     * 
     * @param applicationId 我的申请主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessApMyApplicationByApplicationId(Long applicationId)
    {
        return crmBusinessApMyApplicationMapper.deleteCrmBusinessApMyApplicationByApplicationId(applicationId);
    }
}
