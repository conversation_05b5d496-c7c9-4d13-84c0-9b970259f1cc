# 分表系统组件说明

## 添加的文件和组件清单

### 1. 依赖配置
- **文件**: `ruoyi-crm/pom.xml`
- **修改**: 添加MyBatis-Plus依赖
- **作用**: 提供动态表名功能支持

```xml
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.5.3.1</version>
</dependency>
```

### 2. 核心组件

#### 2.1 MyBatis-Plus配置类
- **文件**: `MybatisPlusConfig.java`
- **位置**: `com.ruoyi.crm.config`
- **核心功能**:
  - 注册动态表名插件
  - 实现表名处理逻辑
  - 提供ThreadLocal表名上下文

```java
@Configuration
public class MybatisPlusConfig {
    // 动态表名插件配置
    // 表名处理器实现
    // ThreadLocal上下文管理
}
```

#### 2.2 分表管理器
- **文件**: `TableShardingManager.java`
- **位置**: `com.ruoyi.crm.config`
- **核心功能**:
  - 根据日期生成分表名
  - 自动创建分表
  - 管理分表生命周期

```java
@Component
public class TableShardingManager {
    // 获取线索表名
    public String getLeadsTableName(LocalDate date)
    
    // 获取客户表名
    public String getCustomersTableName(LocalDate date)
    
    // 获取操作日志表名
    public String getOperationLogTableName(String module, LocalDate date)
    
    // 确保表存在
    private void ensureTableExists(String targetTableName, String templateTableName)
}
```

#### 2.3 分表服务接口
- **文件**: `ITableShardingService.java`
- **位置**: `com.ruoyi.crm.service`
- **核心功能**: 定义分表操作接口

```java
public interface ITableShardingService {
    // 跨表查询线索
    List<Map<String, Object>> queryLeadsAcrossTables(...)
    
    // 跨表查询客户
    List<Map<String, Object>> queryCustomersAcrossTables(...)
    
    // 跨表查询操作日志
    List<Map<String, Object>> queryOperationLogsAcrossTables(...)
    
    // 其他管理功能...
}
```

#### 2.4 分表服务实现
- **文件**: `TableShardingServiceImpl.java`
- **位置**: `com.ruoyi.crm.service.impl`
- **核心功能**:
  - 实现跨表查询逻辑
  - 数据迁移功能
  - 定时清理任务
  - 表统计信息

```java
@Service
public class TableShardingServiceImpl implements ITableShardingService {
    // 跨表查询实现
    // 定时清理任务
    // 数据迁移逻辑
    // 统计信息收集
}
```

#### 2.5 分表配置类
- **文件**: `ShardingConfiguration.java`
- **位置**: `com.ruoyi.crm.config`
- **核心功能**: 管理分表配置参数

```java
@Configuration
@ConfigurationProperties(prefix = "crm.sharding")
public class ShardingConfiguration {
    private boolean enabled;              // 是否启用分表
    private int dataRetentionMonths;      // 数据保留月数
    private int preCreateMonths;          // 预创建月数
    private String cleanupCron;           // 清理任务时间
    private Map<String, ShardingStrategy> strategies; // 分表策略
}
```

#### 2.6 分表管理控制器
- **文件**: `TableShardingController.java`
- **位置**: `com.ruoyi.crm.controller`
- **核心功能**: 提供分表管理API

```java
@RestController
@RequestMapping("/crm/table-sharding")
public class TableShardingController {
    // 获取配置信息
    @GetMapping("/config")
    
    // 预创建分表
    @PostMapping("/pre-create")
    
    // 清理过期分表
    @PostMapping("/cleanup")
    
    // 跨表查询
    @GetMapping("/leads/query")
    @GetMapping("/customers/query")
    
    // 其他管理接口...
}
```

### 3. 配置文件

#### 3.1 分表配置
- **文件**: `application-sharding.yml`
- **位置**: `ruoyi-crm/src/main/resources`
- **作用**: 分表功能配置参数

```yaml
crm:
  sharding:
    enabled: true
    data-retention-months: 36
    pre-create-months: 3
    cleanup-cron: "0 0 2 1 * ?"
    strategies:
      crm_business_leads:
        type: MONTH
        sharding-column: create_time
        enabled: true
```

### 4. 文档

#### 4.1 使用指南
- **文件**: `table-sharding-guide.md`
- **位置**: `ruoyi-crm/doc`
- **内容**: 分表功能使用说明

#### 4.2 架构文档
- **文件**: `table-sharding-architecture.md`
- **位置**: `ruoyi-crm/doc`
- **内容**: 系统架构和工作原理

## 工作原理简述

### 1. 初始化阶段
1. Spring Boot启动时加载配置
2. 注册MyBatis-Plus动态表名插件
3. 初始化分表管理器和服务

### 2. 运行时阶段
1. **业务操作**: 业务代码调用Mapper方法
2. **SQL拦截**: MyBatis-Plus拦截SQL执行
3. **表名替换**: 动态表名插件替换表名
4. **自动创建**: 如果分表不存在则自动创建
5. **执行SQL**: 在正确的分表上执行操作

### 3. 维护阶段
1. **定时清理**: 自动删除过期分表
2. **预创建**: 提前创建未来分表
3. **监控统计**: 收集分表使用情况

## 关键特性

### ✅ 透明化
- 业务代码无需修改
- 自动路由到正确分表
- 支持所有CRUD操作

### ✅ 自动化
- 自动创建分表
- 自动清理过期表
- 自动生成表名

### ✅ 可配置
- 灵活的分表策略
- 可调整的保留期限
- 自定义清理时间

### ✅ 高性能
- 减少单表数据量
- 优化索引效果
- 支持并行查询

### ✅ 易维护
- 完整的管理API
- 详细的日志记录
- 统计信息监控

## 使用场景

### 适用场景
- 数据量大且持续增长的表
- 按时间维度查询的业务
- 需要定期清理历史数据的场景
- 对查询性能有较高要求的系统

### 不适用场景
- 数据量较小的表
- 需要频繁跨表关联的业务
- 对事务一致性要求极高的场景

## 部署和配置

### 1. 启用分表功能
在主配置文件中引入分表配置：
```yaml
spring:
  profiles:
    include: sharding  # 引入application-sharding.yml
```

### 2. 调整分表策略
根据业务需要修改`application-sharding.yml`中的配置

### 3. 初始化分表
首次部署时调用预创建接口：
```bash
curl -X POST "http://localhost:8080/crm/table-sharding/pre-create?months=3"
```

### 4. 监控分表状态
定期检查分表状态和统计信息：
```bash
curl "http://localhost:8080/crm/table-sharding/config"
curl "http://localhost:8080/crm/table-sharding/current-tables"
```

---

这套分表系统通过模块化的设计，实现了功能的清晰分离和高度的可配置性，为CRM系统提供了可靠的大数据量处理能力。 