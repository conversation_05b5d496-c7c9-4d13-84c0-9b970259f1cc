# ChatMessageController

基础路径：`/wxcom/message`

## 接口列表

### 同步聊天记录
- **方法**：POST
- **路径**：`/sync/{chatId}`
- **参数**：
  - `chatId` (路径参数)：聊天ID
- **功能**：同步指定聊天ID的聊天记录

### 获取单聊记录  
- **方法**：GET
- **路径**：`/messages`
- **参数**：
  - `fromUserId`：发送方用户ID
  - `toUserId`：接收方用户ID
  - `startTime`：开始时间 (格式：yyyy-MM-dd HH:mm:ss)
  - `endTime`：结束时间 (格式：yyyy-MM-dd HH:mm:ss)
- **功能**：获取两个用户之间的单聊记录

### 获取群聊记录
- **方法**：GET
- **路径**：`/room/messages`
- **参数**：
  - `roomId`：群聊ID
  - `startTime`：开始时间 (格式：yyyy-MM-dd HH:mm:ss)
  - `endTime`：结束时间 (格式：yyyy-MM-dd HH:mm:ss)
- **功能**：获取指定群聊的聊天记录
