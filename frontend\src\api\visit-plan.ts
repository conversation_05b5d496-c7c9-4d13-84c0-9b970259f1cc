import request from '@/utils/request';
import type { VisitPlan, VisitPlanStatistics, PostponeRequest, CancelRequest, CompleteRequest } from '@/types/visit-plan';

// 查询拜访计划列表
export function getVisitPlanList(params: any) {
  return request({
    url: '/crm/visitPlan/list',
    method: 'get',
    params
  });
}

// 根据关联对象查询拜访计划
export function getVisitPlanByObject(objectType: string, objectId: number) {
  return request({
    url: '/crm/visitPlan/listByObject',
    method: 'get',
    params: { objectType, objectId }
  });
}

// 查询拜访计划详情
export function getVisitPlan(id: number) {
  return request({
    url: `/crm/visitPlan/${id}`,
    method: 'get'
  });
}

// 新增拜访计划
export function addVisitPlan(data: Partial<VisitPlan>) {
  return request({
    url: '/crm/visitPlan',
    method: 'post',
    data
  });
}

// 修改拜访计划
export function updateVisitPlan(data: VisitPlan) {
  return request({
    url: '/crm/visitPlan',
    method: 'put',
    data
  });
}

// 删除拜访计划
export function deleteVisitPlan(id: number | number[]) {
  return request({
    url: `/crm/visitPlan/${id}`,
    method: 'delete'
  });
}

// 延期拜访计划
export function postponeVisitPlan(id: number, data: PostponeRequest) {
  return request({
    url: `/crm/visitPlan/postpone/${id}`,
    method: 'post',
    data
  });
}

// 取消拜访计划
export function cancelVisitPlan(id: number, data: CancelRequest) {
  return request({
    url: `/crm/visitPlan/cancel/${id}`,
    method: 'post',
    data
  });
}

// 完成拜访计划
export function completeVisitPlan(id: number, data: CompleteRequest) {
  return request({
    url: `/crm/visitPlan/complete/${id}`,
    method: 'post',
    data
  });
}

// 获取统计信息
export function getVisitPlanStatistics(dateRange?: string): Promise<{ data: VisitPlanStatistics }> {
  return request({
    url: '/crm/visitPlan/statistics',
    method: 'get',
    params: { dateRange }
  });
}
