<template>
  <div class="opportunity-detail">
    <!-- Tab导航 -->
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="详细资料" name="details">
        <OpportunityDetailsTab :opportunity-data="opportunityData" />
      </el-tab-pane>

      <el-tab-pane label="团队成员" name="team">
        <OpportunityTeamTab
          :entity-data="{ id: opportunityData.id, name: opportunityData.name }"
          :readonly="readonly"
        />
      </el-tab-pane>

      <el-tab-pane label="活动记录" name="activity">
        <div class="tab-content">
          <el-empty description="活动记录功能开发中..." />
        </div>
      </el-tab-pane>

      <el-tab-pane label="附件" name="attachments">
        <div class="tab-content">
          <el-empty description="附件功能开发中..." />
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 底部操作按钮 -->
    <div class="detail-footer">
      <el-button type="primary" @click="handleEdit">编辑商机</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, ref } from 'vue'
import OpportunityDetailsTab from '../tabs/OpportunityDetailsTab.vue'
import OpportunityTeamTab from '../tabs/OpportunityTeamTab.vue'

interface OpportunityData {
  id?: number
  name?: string
  customerName?: string
  stage?: string
  stageName?: string
  expectedAmount?: number
  probability?: number
  expectedClosingDate?: string
  ownerName?: string
  createTime?: string
  updateTime?: string
  remarks?: string
}

interface Props {
  opportunityData: OpportunityData
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

const emit = defineEmits(['edit'])

// 响应式数据
const activeTab = ref('details')

// 方法
const handleEdit = () => {
  emit('edit')
}
</script>

<style scoped>
.opportunity-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-content {
  padding: 16px;
}

.detail-footer {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  text-align: right;
  margin-top: auto;
}
</style>

<style scoped>
.drawer-footer {
  padding: 16px;
  text-align: right;
}
</style> 