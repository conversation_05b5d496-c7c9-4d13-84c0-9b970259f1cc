package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmBusinessApApprovalHistory;

/**
 * 审批历史Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface CrmBusinessApApprovalHistoryMapper 
{
    /**
     * 查询审批历史
     * 
     * @param historyId 审批历史主键
     * @return 审批历史
     */
    public CrmBusinessApApprovalHistory selectCrmBusinessApApprovalHistoryByHistoryId(Long historyId);

    /**
     * 查询审批历史列表
     * 
     * @param crmBusinessApApprovalHistory 审批历史
     * @return 审批历史集合
     */
    public List<CrmBusinessApApprovalHistory> selectCrmBusinessApApprovalHistoryList(CrmBusinessApApprovalHistory crmBusinessApApprovalHistory);

    /**
     * 新增审批历史
     * 
     * @param crmBusinessApApprovalHistory 审批历史
     * @return 结果
     */
    public int insertCrmBusinessApApprovalHistory(CrmBusinessApApprovalHistory crmBusinessApApprovalHistory);

    /**
     * 修改审批历史
     * 
     * @param crmBusinessApApprovalHistory 审批历史
     * @return 结果
     */
    public int updateCrmBusinessApApprovalHistory(CrmBusinessApApprovalHistory crmBusinessApApprovalHistory);

    /**
     * 删除审批历史
     * 
     * @param historyId 审批历史主键
     * @return 结果
     */
    public int deleteCrmBusinessApApprovalHistoryByHistoryId(Long historyId);

    /**
     * 批量删除审批历史
     * 
     * @param historyIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCrmBusinessApApprovalHistoryByHistoryIds(Long[] historyIds);
}
