<template>
  <div class="team-assign-button">
    <!-- 单个分配按钮 -->
    <el-button
      v-if="!isBatchMode"
      :type="buttonType"
      :size="size"
      :icon="icon"
      @click="openDialog"
      :loading="loading"
    >
      {{ buttonText }}
    </el-button>

    <!-- 批量分配按钮 -->
    <el-dropdown v-else @command="handleCommand">
      <el-button :type="buttonType" :size="size" :loading="loading">
        批量操作
        <el-icon class="el-icon--right"><arrow-down /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="assign">
            <el-icon><UserFilled /></el-icon>
            分配团队
          </el-dropdown-item>
          <el-dropdown-item command="unassign" divided>
            <el-icon><Remove /></el-icon>
            取消分配
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 当前团队信息显示 -->
    <div v-if="showCurrentTeam && currentTeam" class="current-team-info">
      <el-tag size="small" type="info">
        <el-icon><User /></el-icon>
        {{ currentTeam.teamName }}
      </el-tag>
    </div>

    <!-- 团队分配对话框 -->
    <TeamAssignDialog
      v-model:visible="dialogVisible"
      :biz-id="bizId"
      :biz-type="bizType"
      :biz-ids="bizIds"
      :biz-names="bizNames"
      :title="dialogTitle"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import {
  batchUnassignTeamFromBiz,
  BizType,
  getTeamByBiz
} from '@/api/team-relation'
import {
  ArrowDown,
  Remove,
  User,
  UserFilled
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import TeamAssignDialog from './TeamAssignDialog.vue'

// 定义接口
interface TeamInfo {
  teamId: number
  teamName: string
  leaderName?: string
}

interface Props {
  // 单个对象模式
  bizId?: number
  bizType?: string
  bizName?: string
  
  // 批量模式
  bizIds?: number[]
  bizNames?: string[]
  
  // 按钮样式
  buttonType?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | 'default'
  size?: 'large' | 'default' | 'small'
  icon?: any
  text?: string
  
  // 功能配置
  showCurrentTeam?: boolean
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  buttonType: 'primary',
  size: 'default',
  text: '分配团队',
  showCurrentTeam: false,
  readonly: false
})

const emit = defineEmits<{
  success: []
  change: [teamInfo: TeamInfo | null]
}>()

// 响应式数据
const dialogVisible = ref(false)
const currentTeam = ref<TeamInfo | null>(null)
const loading = ref(false)

// 计算属性
const isBatchMode = computed(() => {
  return props.bizIds && props.bizIds.length > 0
})

const buttonText = computed(() => {
  if (isBatchMode.value) {
    return `批量分配 (${props.bizIds?.length || 0})`
  }
  
  if (currentTeam.value) {
    return props.text.replace('分配', '重新分配')
  }
  
  return props.text
})

const dialogTitle = computed(() => {
  if (isBatchMode.value) {
    return `批量分配团队 (${props.bizIds?.length || 0} 个对象)`
  }
  
  const bizName = props.bizName || getBizTypeName(props.bizType)
  return `分配团队 - ${bizName}`
})

// 方法
const openDialog = () => {
  if (props.readonly) {
    ElMessage.warning('当前状态下不允许分配团队')
    return
  }
  dialogVisible.value = true
}

const handleCommand = (command: string) => {
  if (props.readonly) {
    ElMessage.warning('当前状态下不允许操作')
    return
  }

  if (command === 'assign') {
    openDialog()
  } else if (command === 'unassign') {
    handleBatchUnassign()
  }
}

const handleBatchUnassign = async () => {
  if (!props.bizIds || !props.bizType) return

  try {
    await ElMessageBox.confirm(
      `确定要取消选中的 ${props.bizIds.length} 个对象的团队分配吗？`,
      '确认批量取消分配',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.value = true
    await batchUnassignTeamFromBiz(props.bizIds, props.bizType)
    ElMessage.success(`成功取消 ${props.bizIds.length} 个对象的团队分配`)
    
    emit('success')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量取消分配失败:', error)
      ElMessage.error('批量取消分配失败')
    }
  } finally {
    loading.value = false
  }
}

const loadCurrentTeam = async () => {
  if (!props.bizId || !props.bizType || isBatchMode.value) {
    currentTeam.value = null
    return
  }

  try {
    const response = await getTeamByBiz(props.bizId, props.bizType)
    currentTeam.value = response.data ? {
      teamId: response.data.teamId,
      teamName: response.data.teamName,
      leaderName: response.data.leaderName
    } : null

    emit('change', currentTeam.value)
  } catch (error: any) {
    console.error('加载当前团队信息失败:', error)
    currentTeam.value = null
  }
}

const handleSuccess = () => {
  emit('success')
  loadCurrentTeam() // 重新加载当前团队信息
}

const getBizTypeName = (bizType?: string) => {
  const typeMap: Record<string, string> = {
    [BizType.CONTACT]: '联系人',
    [BizType.LEAD]: '线索',
    [BizType.CUSTOMER]: '客户',
    [BizType.OPPORTUNITY]: '商机',
    [BizType.CONTRACT]: '合同',
    [BizType.VISIT_PLAN]: '拜访计划'
  }
  return typeMap[bizType || ''] || '业务对象'
}

// 生命周期
onMounted(() => {
  if (props.showCurrentTeam) {
    loadCurrentTeam()
  }
})

// 暴露方法给父组件
defineExpose({
  loadCurrentTeam,
  openDialog
})
</script>

<style scoped>
.team-assign-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.current-team-info {
  margin-left: 8px;
}

.current-team-info .el-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 批量操作按钮样式 */
.el-dropdown {
  vertical-align: top;
}

.el-dropdown-menu__item {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
