import mysql from 'mysql2/promise';
import config from './config.js';

// 创建连接池
const pool = mysql.createPool({
  ...config.db,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 连接池监控
setInterval(async () => {
  try {
    const connection = await pool.getConnection();
    const stats = {
      freeConnections: pool.pool.freeConnections.length,
      allConnections: pool.pool.allConnections.length,
      connectionLimit: pool.pool.config.connectionLimit
    };
    console.log('连接池状态:', stats);
    connection.release();
  } catch (error) {
    console.error('连接池监控错误:', error.message);
  }
}, 60000); // 每分钟监控一次

/**
 * 检查指定表是否存在于crm41数据库中
 * 
 * @param {string} tableName - 要检查的表名
 * @returns {Promise<boolean>} - 返回一个Promise，解析为布尔值：
 *   - true: 表存在
 *   - false: 表不存在
 * 
 * @description
 * 该函数通过查询information_schema.tables系统表来检查指定表是否存在。
 * 查询条件包括：
 *   - 数据库模式为'crm41'
 *   - 表名与传入参数匹配
 * 如果查询结果count > 0，则表示表存在
 * 
 * 实现原理：
 * 1. 查询information_schema.tables系统表
 * 2. 过滤条件：table_schema = 'crm41' AND table_name = ?
 * 3. 使用COUNT(*)统计匹配的记录数
 * 4. 如果count > 0表示表存在
 * 
 * 注意：
 * - 该函数仅检查crm41数据库中的表
 * - 表名区分大小写
 */
export 
async function checkTableExists(tableName) {
  const [rows] = await pool.query(`
    SELECT COUNT(*) as count 
    FROM information_schema.tables
    WHERE table_schema = 'crm41' 
    AND table_name = ?
  `, [tableName]);
  return rows[0].count > 0;
}

// 单条插入
export async function insertProduct(product) {
  const sql = `
    INSERT INTO crm_products (
      name, price, image_url, product_link, 
      material_properties, material_process,
      tech_specs, advantages, disadvantages,
      application_areas, type
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
      price = VALUES(price),
      image_url = VALUES(image_url),
      product_link = VALUES(product_link),
      material_properties = VALUES(material_properties),
      material_process = VALUES(material_process),
      tech_specs = VALUES(tech_specs),
      advantages = VALUES(advantages),
      disadvantages = VALUES(disadvantages),
      application_areas = VALUES(application_areas),
      type = VALUES(type)
  `;
  
  const values = [
    product.name,
    product.price === '' || isNaN(product.price) ? null : parseFloat(product.price),
    product.productImages?.length > 0 ? JSON.stringify(product.productImages) : null,
    product.link,
    product.materialProperties,
    product.materialProcess,
    JSON.stringify(product.techSpecs),
    product.materialEvaluation.advantages,
    product.materialEvaluation.disadvantages,
    product.applicationAreas.join(','),
    product.type
  ];

  try {
    await pool.query(sql, values);
  } catch (err) {
    if (err.code === 'ER_DUP_ENTRY') {
      console.log(`Product "${product.name}" already exists, skipping insert`);
      return;
    }
    throw err;
  }
}

// 批量插入
export async function batchInsertProducts(products) {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();

    const sql = `
      INSERT INTO crm_products (
        name, price, image_url, product_link, 
        material_properties, material_process,
        tech_specs, advantages, disadvantages,
        application_areas, type
      ) VALUES ?
      ON DUPLICATE KEY UPDATE
        price = VALUES(price),
        image_url = VALUES(image_url),
        product_link = VALUES(product_link),
        material_properties = VALUES(material_properties),
        material_process = VALUES(material_process),
        tech_specs = VALUES(tech_specs),
        advantages = VALUES(advantages),
        disadvantages = VALUES(disadvantages),
        application_areas = VALUES(application_areas),
        type = VALUES(type)
    `;

    const values = products.map(product => [
      product.name,
      product.price === '' || isNaN(product.price) ? null : parseFloat(product.price),
      product.productImages?.length > 0 ? JSON.stringify(product.productImages) : null,
      product.link,
      product.materialProperties,
      product.materialProcess,
      JSON.stringify(product.techSpecs),
      product.materialEvaluation.advantages,
      product.materialEvaluation.disadvantages,
      product.applicationAreas.join(','),
      product.type
    ]);

    await connection.query(sql, [values]);
    await connection.commit();
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

// 关闭连接
export async function closeConnection() {
  await pool.end();
}
