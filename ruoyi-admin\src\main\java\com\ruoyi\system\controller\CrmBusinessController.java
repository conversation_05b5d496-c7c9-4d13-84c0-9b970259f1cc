package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.domain.entity.CrmBusiness;
import com.ruoyi.system.service.ICrmBusinessService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 业务Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Api(tags = "业务管理")
@RestController
@RequestMapping("/system/business")
public class CrmBusinessController extends BaseController {
    @Autowired
    private ICrmBusinessService crmBusinessService;

    /**
     * 查询业务列表
     */
    @ApiOperation("查询业务列表")
    @PreAuthorize("@ss.hasPermi('system:business:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusiness crmBusiness) {
        startPage();
        List<CrmBusiness> list = crmBusinessService.selectCrmBusinessList(crmBusiness);
        return getDataTable(list);
    }

    /**
     * 导出业务列表
     */
    @ApiOperation("导出业务列表")
    @PreAuthorize("@ss.hasPermi('system:business:export')")
    @Log(title = "业务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmBusiness crmBusiness) {
        List<CrmBusiness> list = crmBusinessService.selectCrmBusinessList(crmBusiness);
        ExcelUtil<CrmBusiness> util = new ExcelUtil<CrmBusiness>(CrmBusiness.class);
        util.exportExcel(response, list, "业务数据");
    }

    /**
     * 获取业务详细信息
     */
    @ApiOperation("获取业务详细信息")
    @PreAuthorize("@ss.hasPermi('system:business:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(
            @ApiParam("业务ID") @PathVariable("id") Long id) {
        return success(crmBusinessService.selectCrmBusinessById(id));
    }

    /**
     * 新增业务
     */
    @ApiOperation("新增业务")
    @PreAuthorize("@ss.hasPermi('system:business:add')")
    @Log(title = "业务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(
            @ApiParam("业务信息") @RequestBody CrmBusiness crmBusiness) {
        return toAjax(crmBusinessService.insertCrmBusiness(crmBusiness));
    }

    /**
     * 修改业务
     */
    @ApiOperation("修改业务")
    @PreAuthorize("@ss.hasPermi('system:business:edit')")
    @Log(title = "业务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(
            @ApiParam("业务信息") @RequestBody CrmBusiness crmBusiness) {
        return toAjax(crmBusinessService.updateCrmBusiness(crmBusiness));
    }

    /**
     * 删除业务
     */
    @ApiOperation("删除业务")
    @PreAuthorize("@ss.hasPermi('system:business:remove')")
    @Log(title = "业务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(
            @ApiParam("业务ID数组") @PathVariable Long[] ids) {
        return toAjax(crmBusinessService.deleteCrmBusinessByIds(ids));
    }
}
