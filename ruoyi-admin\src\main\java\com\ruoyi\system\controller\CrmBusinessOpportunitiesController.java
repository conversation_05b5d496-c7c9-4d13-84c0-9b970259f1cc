package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.CrmBusinessOpportunities;
import com.ruoyi.system.service.ICrmBusinessOpportunitiesService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 商机Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Api(tags = "商机管理")
@RestController
@RequestMapping("/system/opportunities")
public class CrmBusinessOpportunitiesController extends BaseController {
    @Autowired
    private ICrmBusinessOpportunitiesService crmBusinessOpportunitiesService;

    /**
     * 查询商机列表
     */
    @ApiOperation("查询商机列表")
    @PreAuthorize("@ss.hasPermi('system:opportunities:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusinessOpportunities crmBusinessOpportunities) {
        startPage();
        List<CrmBusinessOpportunities> list = crmBusinessOpportunitiesService
                .selectCrmBusinessOpportunitiesList(crmBusinessOpportunities);
        return getDataTable(list);
    }

    /**
     * 导出商机列表
     */
    @ApiOperation("导出商机列表")
    @PreAuthorize("@ss.hasPermi('system:opportunities:export')")
    @Log(title = "商机", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmBusinessOpportunities crmBusinessOpportunities) {
        List<CrmBusinessOpportunities> list = crmBusinessOpportunitiesService
                .selectCrmBusinessOpportunitiesList(crmBusinessOpportunities);
        ExcelUtil<CrmBusinessOpportunities> util = new ExcelUtil<CrmBusinessOpportunities>(
                CrmBusinessOpportunities.class);
        util.exportExcel(response, list, "商机数据");
    }

    /**
     * 获取商机详细信息
     */
    @ApiOperation("获取商机详细信息")
    @PreAuthorize("@ss.hasPermi('system:opportunities:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("商机ID") @PathVariable("id") Long id) {
        return success(crmBusinessOpportunitiesService.selectCrmBusinessOpportunitiesById(id));
    }

    /**
     * 新增商机
     */
    @ApiOperation("新增商机")
    @PreAuthorize("@ss.hasPermi('system:opportunities:add')")
    @Log(title = "商机", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmBusinessOpportunities crmBusinessOpportunities) {
        return toAjax(crmBusinessOpportunitiesService.insertCrmBusinessOpportunities(crmBusinessOpportunities));
    }

    /**
     * 修改商机
     */
    @ApiOperation("修改商机")
    @PreAuthorize("@ss.hasPermi('system:opportunities:edit')")
    @Log(title = "商机", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmBusinessOpportunities crmBusinessOpportunities) {
        return toAjax(crmBusinessOpportunitiesService.updateCrmBusinessOpportunities(crmBusinessOpportunities));
    }

    /**
     * 删除商机
     */
    @ApiOperation("删除商机")
    @PreAuthorize("@ss.hasPermi('system:opportunities:remove')")
    @Log(title = "商机", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("商机ID数组") @PathVariable Long[] ids) {
        return toAjax(crmBusinessOpportunitiesService.deleteCrmBusinessOpportunitiesByIds(ids));
    }
}
