<template>
  <div class="customer-reminder-list">
    <div class="reminder-tabs">
      <div 
        class="tab-item" 
        :class="{ active: activeTab === tab.key }" 
        v-for="tab in tabs" 
        :key="tab.key"
        @click="activeTab = tab.key"
      >
        <span class="tab-label">{{ tab.label }}</span>
        <span class="tab-count">{{ tab.count }}</span>
      </div>
    </div>
    
    <div class="reminder-content">
      <div class="empty-state" v-if="currentTabData.length === 0">
        <el-icon class="empty-icon"><Clock /></el-icon>
        <div class="empty-text">{{ getEmptyText() }}</div>
      </div>
      
      <div class="reminder-list" v-else>
        <div 
          class="reminder-item" 
          v-for="(item, index) in currentTabData" 
          :key="item.id || index"
        >
          <div class="customer-info">
            <div class="customer-avatar">
              {{ item.customerName?.charAt(0) || 'C' }}
            </div>
            <div class="customer-details">
              <div class="customer-name">{{ item.customerName || '-' }}</div>
              <div class="last-contact">{{ item.lastContactInfo || '-' }}</div>
            </div>
          </div>
          <div class="reminder-actions">
            <el-button size="small" type="primary" text>
              立即联系
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { Clock } from '@element-plus/icons-vue'

interface CustomerReminder {
  id: string
  customerName: string
  lastContactInfo: string
  daysOverdue: number
}

// Tab 配置
const tabs = ref([
  { key: 'over7days', label: '超过7天未联系', count: 1 },
  { key: 'over15days', label: '超过15天未联系', count: 1 },
  { key: 'over30days', label: '超过30天未联系', count: 1 }
])

const activeTab = ref('over7days')

// 模拟数据
const reminderData = ref<Record<string, CustomerReminder[]>>({
  over7days: [
    {
      id: '1',
      customerName: 'wolXttDgAADwyIZ7EieQ_oP3GS5x8QuQ',
      lastContactInfo: '-',
      daysOverdue: 8
    }
  ],
  over15days: [
    {
      id: '2', 
      customerName: 'CustomerName15',
      lastContactInfo: '上次联系：15天前',
      daysOverdue: 16
    }
  ],
  over30days: [
    {
      id: '3',
      customerName: 'CustomerName30', 
      lastContactInfo: '上次联系：30天前',
      daysOverdue: 32
    }
  ]
})

// 当前Tab的数据
const currentTabData = computed(() => {
  return reminderData.value[activeTab.value] || []
})

// 获取空状态文本
const getEmptyText = () => {
  const tabLabels: Record<string, string> = {
    over7days: '暂无超过7天未联系的客户',
    over15days: '暂无超过15天未联系的客户',
    over30days: '暂无超过30天未联系的客户'
  }
  return tabLabels[activeTab.value] || '暂无数据'
}
</script>

<style lang="scss" scoped>
.customer-reminder-list {
  padding: 24px;
  
  .reminder-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
    
    .tab-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      font-size: 13px;
      color: #646a73;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: all 0.3s ease;
      
      &:hover {
        color: #1677ff;
      }
      
      &.active {
        color: #1677ff;
        border-bottom-color: #1677ff;
        font-weight: 500;
      }
      
      .tab-label {
        white-space: nowrap;
      }
      
      .tab-count {
        background: #f0f0f0;
        color: #86909c;
        font-size: 11px;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 16px;
        text-align: center;
        
        .tab-item.active & {
          background: #e6f4ff;
          color: #1677ff;
        }
      }
    }
  }
  
  .reminder-content {
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #86909c;
      
      .empty-icon {
        font-size: 36px;
        margin-bottom: 12px;
        opacity: 0.5;
      }
      
      .empty-text {
        font-size: 13px;
      }
    }
    
    .reminder-list {
      .reminder-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
        margin-bottom: 8px;
        transition: all 0.3s ease;
        
        &:hover {
          background: #fafbfc;
          border-color: #d6e4ff;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .customer-info {
          display: flex;
          align-items: center;
          gap: 12px;
          flex: 1;
          
          .customer-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #ff7875;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
          }
          
          .customer-details {
            flex: 1;
            min-width: 0;
            
            .customer-name {
              font-size: 14px;
              font-weight: 500;
              color: #1f2329;
              margin-bottom: 4px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            
            .last-contact {
              font-size: 12px;
              color: #86909c;
            }
          }
        }
        
        .reminder-actions {
          flex-shrink: 0;
        }
      }
    }
  }
}
</style> 