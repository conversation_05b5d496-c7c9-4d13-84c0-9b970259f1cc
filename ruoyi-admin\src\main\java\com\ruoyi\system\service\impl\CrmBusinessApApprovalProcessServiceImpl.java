package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CrmBusinessApApprovalProcessMapper;
import com.ruoyi.common.domain.entity.CrmBusinessApApprovalProcess;
import com.ruoyi.system.service.ICrmBusinessApApprovalProcessService;

/**
 * 审批处理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class CrmBusinessApApprovalProcessServiceImpl implements ICrmBusinessApApprovalProcessService 
{
    @Autowired
    private CrmBusinessApApprovalProcessMapper crmBusinessApApprovalProcessMapper;

    /**
     * 查询审批处理
     * 
     * @param approvalId 审批处理主键
     * @return 审批处理
     */
    @Override
    public CrmBusinessApApprovalProcess selectCrmBusinessApApprovalProcessByApprovalId(Long approvalId)
    {
        return crmBusinessApApprovalProcessMapper.selectCrmBusinessApApprovalProcessByApprovalId(approvalId);
    }

    /**
     * 查询审批处理列表
     * 
     * @param crmBusinessApApprovalProcess 审批处理
     * @return 审批处理
     */
    @Override
    public List<CrmBusinessApApprovalProcess> selectCrmBusinessApApprovalProcessList(CrmBusinessApApprovalProcess crmBusinessApApprovalProcess)
    {
        return crmBusinessApApprovalProcessMapper.selectCrmBusinessApApprovalProcessList(crmBusinessApApprovalProcess);
    }

    /**
     * 新增审批处理
     * 
     * @param crmBusinessApApprovalProcess 审批处理
     * @return 结果
     */
    @Override
    public int insertCrmBusinessApApprovalProcess(CrmBusinessApApprovalProcess crmBusinessApApprovalProcess)
    {
        return crmBusinessApApprovalProcessMapper.insertCrmBusinessApApprovalProcess(crmBusinessApApprovalProcess);
    }

    /**
     * 修改审批处理
     * 
     * @param crmBusinessApApprovalProcess 审批处理
     * @return 结果
     */
    @Override
    public int updateCrmBusinessApApprovalProcess(CrmBusinessApApprovalProcess crmBusinessApApprovalProcess)
    {
        return crmBusinessApApprovalProcessMapper.updateCrmBusinessApApprovalProcess(crmBusinessApApprovalProcess);
    }

    /**
     * 批量删除审批处理
     * 
     * @param approvalIds 需要删除的审批处理主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessApApprovalProcessByApprovalIds(Long[] approvalIds)
    {
        return crmBusinessApApprovalProcessMapper.deleteCrmBusinessApApprovalProcessByApprovalIds(approvalIds);
    }

    /**
     * 删除审批处理信息
     * 
     * @param approvalId 审批处理主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessApApprovalProcessByApprovalId(Long approvalId)
    {
        return crmBusinessApApprovalProcessMapper.deleteCrmBusinessApApprovalProcessByApprovalId(approvalId);
    }
}
