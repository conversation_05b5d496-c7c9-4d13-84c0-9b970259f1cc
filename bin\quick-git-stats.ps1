# 快速Git统计脚本
param(
    [string]$Date = (Get-Date -Format "yyyy-MM-dd")
)

Write-Host "=== Git 今日统计 ($Date) ===" -ForegroundColor Cyan

# 统计提交次数
$commitCount = (git log --since="$Date 00:00:00" --oneline).Count
Write-Host "提交次数: $commitCount" -ForegroundColor Green

if ($commitCount -eq 0) {
    Write-Host "今天还没有提交" -ForegroundColor Yellow
    exit
}

# 统计代码行数
$stats = git log --since="$Date 00:00:00" --stat --format="" | 
    Select-String "(\d+) insertion|(\d+) deletion" | 
    ForEach-Object {
        if ($_ -match "(\d+) insertion") { [int]$matches[1] }
        if ($_ -match "(\d+) deletion") { -[int]$matches[1] }
    } | Measure-Object -Sum

$totalLines = if ($stats.Sum) { $stats.Sum } else { 0 }
Write-Host "净增代码行: $totalLines" -ForegroundColor $(if ($totalLines -gt 0) { "Green" } else { "Red" })

# 提交列表
Write-Host "`n提交列表:" -ForegroundColor Yellow
git log --since="$Date 00:00:00" --pretty=format:"%C(yellow)%h %C(cyan)%ad %C(reset)%s %C(green)(%an)" --date=format:"%H:%M"

Write-Host "" 