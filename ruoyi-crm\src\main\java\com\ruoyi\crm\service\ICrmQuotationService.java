package com.ruoyi.crm.service;

import java.util.List;
import com.ruoyi.common.domain.CrmQuotation;

/**
 * 报价单Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface ICrmQuotationService {
    /**
     * 查询报价单
     * 
     * @param id 报价单主键
     * @return 报价单
     */
    public CrmQuotation selectCrmQuotationById(Long id);

    /**
     * 查询报价单列表
     * 
     * @param crmQuotation 报价单
     * @return 报价单集合
     */
    public List<CrmQuotation> selectCrmQuotationList(CrmQuotation crmQuotation);

    /**
     * 新增报价单
     * 
     * @param crmQuotation 报价单
     * @return 结果
     */
    public int insertCrmQuotation(CrmQuotation crmQuotation);

    /**
     * 修改报价单
     * 
     * @param crmQuotation 报价单
     * @return 结果
     */
    public int updateCrmQuotation(CrmQuotation crmQuotation);

    /**
     * 批量删除报价单
     * 
     * @param ids 需要删除的报价单主键集合
     * @return 结果
     */
    public int deleteCrmQuotationByIds(Long[] ids);

    /**
     * 删除报价单信息
     * 
     * @param id 报价单主键
     * @return 结果
     */
    public int deleteCrmQuotationById(Long id);

    /**
     * 根据报价单编号查询报价单
     * 
     * @param quotationNo 报价单编号
     * @return 报价单
     */
    public CrmQuotation selectCrmQuotationByQuotationNo(String quotationNo);

    /**
     * 提交报价单审批
     * 
     * @param quotationId 报价单ID
     * @param processDefinitionKey 流程定义Key
     * @return 结果
     */
    public int submitQuotationApproval(Long quotationId, String processDefinitionKey);

    /**
     * 审批报价单
     * 
     * @param taskId 任务ID
     * @param approved 是否通过
     * @param comment 审批意见
     * @return 结果
     */
    public int approveQuotation(String taskId, boolean approved, String comment);

    /**
     * 撤销报价单审批
     * 
     * @param quotationId 报价单ID
     * @param reason 撤销原因
     * @return 结果
     */
    public int cancelQuotationApproval(Long quotationId, String reason);

    /**
     * 生成报价单编号
     * 
     * @return 报价单编号
     */
    public String generateQuotationNo();

    /**
     * 计算报价单总金额
     * 
     * @param crmQuotation 报价单
     * @return 总金额
     */
    public void calculateTotalAmount(CrmQuotation crmQuotation);
}