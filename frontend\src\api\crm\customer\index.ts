import request from '@/utils/request';
import type { CustomerData, CustomerSearchParams } from './types';

// 搜索客户
export function searchCustomers(params: CustomerSearchParams) {
    return request({
        url: '/front/crm/customer/search',
        method: 'get',
        params
    });
}

// 获取客户列表
export function listCustomers(params: any) {
    return request({
        url: '/front/crm/customer/list',
        method: 'get',
        params
    });
}

// 获取客户详情
export function getCustomer(id: number) {
    return request<CustomerData>({
        url: `/front/crm/customer/${id}`,
        method: 'get'
    });
}

// 创建客户
export function createCustomer(data: Partial<CustomerData>) {
    return request({
        url: '/front/crm/customer',
        method: 'post',
        data
    });
}

// 更新客户
export function updateCustomer(id: number, data: Partial<CustomerData>) {
    return request({
        url: `/front/crm/customer/${id}`,
        method: 'put',
        data
    });
}

// 删除客户
export function deleteCustomer(id: number) {
    return request({
        url: `/front/crm/customer/${id}`,
        method: 'delete'
    });
} 