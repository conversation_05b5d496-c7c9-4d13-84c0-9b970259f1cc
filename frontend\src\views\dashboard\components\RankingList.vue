<template>
  <div class="ranking-list">
    <div class="ranking-tabs">
      <div 
        class="tab-item" 
        :class="{ active: activeTab === tab.key }" 
        v-for="tab in tabs" 
        :key="tab.key"
        @click="activeTab = tab.key"
      >
        {{ tab.label }}
      </div>
    </div>
    
    <div class="ranking-content">
      <div class="ranking-header">
        <div class="header-item">排名</div>
        <div class="header-item">姓名</div>
        <div class="header-item">{{ currentTabData.unit }}</div>
        <div class="header-item">目标完成率 (%)</div>
      </div>
      
      <div class="ranking-body">
        <div class="empty-state" v-if="currentTabData.data.length === 0">
          <el-icon class="empty-icon"><Document /></el-icon>
          <div class="empty-text">暂无数据</div>
        </div>
        
        <div 
          class="ranking-item" 
          v-for="(item, index) in currentTabData.data" 
          :key="item.id || index"
        >
          <div class="rank-number">
            <div class="rank-badge" :class="getRankClass(index + 1)">
              {{ index + 1 }}
            </div>
          </div>
          <div class="user-info">
            <div class="user-avatar">
              {{ item.name?.charAt(0) || 'U' }}
            </div>
            <div class="user-name">{{ item.name || '-' }}</div>
          </div>
          <div class="amount-info">
            <div class="amount-value">{{ item.amount || 0 }}</div>
          </div>
          <div class="completion-info">
            <div class="completion-rate">{{ item.completionRate || 0 }}%</div>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${Math.min(item.completionRate || 0, 100)}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { Document } from '@element-plus/icons-vue'

// Tab 配置
const tabs = ref([
  { key: 'payment', label: '回款金额' },
  { key: 'contract', label: '合同金额' },
  { key: 'opportunity', label: '商机数量' }
])

const activeTab = ref('payment')

interface RankingItem {
  id: number | string
  name: string
  amount: number
  completionRate: number
}

// 模拟数据
const rankingData = ref<Record<string, { unit: string; data: RankingItem[] }>>({
  payment: {
    unit: '回款金额 (元)',
    data: [
      // 这里可以放置实际的排行榜数据
      // { id: 1, name: '张三', amount: 100000, completionRate: 85 }
    ]
  },
  contract: {
    unit: '合同金额 (元)', 
    data: []
  },
  opportunity: {
    unit: '商机数量 (个)',
    data: []
  }
})

// 当前Tab的数据
const currentTabData = computed(() => {
  return rankingData.value[activeTab.value as keyof typeof rankingData.value] || { unit: '', data: [] }
})

// 获取排名样式类
const getRankClass = (rank: number) => {
  if (rank === 1) return 'rank-gold'
  if (rank === 2) return 'rank-silver' 
  if (rank === 3) return 'rank-bronze'
  return 'rank-normal'
}
</script>

<style lang="scss" scoped>
.ranking-list {
  padding: 24px;
  
  .ranking-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
    
    .tab-item {
      padding: 12px 20px;
      font-size: 14px;
      color: #646a73;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: all 0.3s ease;
      
      &:hover {
        color: #1677ff;
      }
      
      &.active {
        color: #1677ff;
        border-bottom-color: #1677ff;
        font-weight: 500;
      }
    }
  }
  
  .ranking-content {
    .ranking-header {
      display: grid;
      grid-template-columns: 60px 1fr 120px 140px;
      gap: 16px;
      padding: 12px 16px;
      background: #fafbfc;
      border-radius: 6px;
      margin-bottom: 12px;
      
      .header-item {
        font-size: 12px;
        font-weight: 600;
        color: #86909c;
        text-align: center;
        
        &:first-child,
        &:nth-child(2) {
          text-align: left;
        }
      }
    }
    
    .ranking-body {
      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 20px;
        color: #86909c;
        
        .empty-icon {
          font-size: 48px;
          margin-bottom: 16px;
          opacity: 0.5;
        }
        
        .empty-text {
          font-size: 14px;
        }
      }
      
      .ranking-item {
        display: grid;
        grid-template-columns: 60px 1fr 120px 140px;
        gap: 16px;
        align-items: center;
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
        margin-bottom: 8px;
        transition: all 0.3s ease;
        
        &:hover {
          background: #fafbfc;
          border-color: #d6e4ff;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .rank-number {
          display: flex;
          justify-content: center;
          
          .rank-badge {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            color: #fff;
            
            &.rank-gold {
              background: linear-gradient(135deg, #ffd700, #ffb300);
            }
            
            &.rank-silver {
              background: linear-gradient(135deg, #c0c0c0, #a0a0a0);
            }
            
            &.rank-bronze {
              background: linear-gradient(135deg, #cd7f32, #b8860b);
            }
            
            &.rank-normal {
              background: linear-gradient(135deg, #86909c, #6b7280);
            }
          }
        }
        
        .user-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #1677ff;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
          }
          
          .user-name {
            font-size: 14px;
            font-weight: 500;
            color: #1f2329;
          }
        }
        
        .amount-info {
          text-align: center;
          
          .amount-value {
            font-size: 16px;
            font-weight: 600;
            color: #1f2329;
          }
        }
        
        .completion-info {
          display: flex;
          flex-direction: column;
          gap: 8px;
          
          .completion-rate {
            font-size: 14px;
            font-weight: 600;
            color: #1f2329;
            text-align: center;
          }
          
          .progress-bar {
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
            
            .progress-fill {
              height: 100%;
              background: linear-gradient(90deg, #1677ff, #40a9ff);
              border-radius: 3px;
              transition: width 0.3s ease;
            }
          }
        }
      }
    }
  }
}
</style> 