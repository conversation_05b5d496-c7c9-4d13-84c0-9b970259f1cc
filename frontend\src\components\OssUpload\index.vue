<template>
  <div class="oss-upload">
    <el-upload :auto-upload="true" :before-upload="handleBeforeUpload" :http-request="customUpload" v-bind="$attrs">
      <slot>
        <el-button type="primary">点击上传</el-button>
      </slot>
    </el-upload>
  </div>
</template>

<script setup lang="ts">
import type { UploadProps, UploadRequestOptions } from 'element-plus';
import { ElMessage } from 'element-plus';
// 组件中
import { uploadToOSS } from '../../composables/oss';

const props = withDefaults(defineProps<{
  maxSize?: number // MB
}>(), {
  maxSize: 100
})

const emit = defineEmits<{
  (e: 'success', data: { url: string; file: File }): void
  (e: 'error', error: Error): void
  (e: 'http-request', options: UploadRequestOptions): void
}>()

// 上传前的处理
const handleBeforeUpload: UploadProps['beforeUpload'] = (file) => {
  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLtMaxSize) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB!`)
    return false
  }
  return true
}

// 自定义上传方法
import { getOSSPolicy } from '../../composables/oss';

const customUpload = async (options: UploadRequestOptions) => {
  try {
    const file = options.file as File
    console.log('[oss] Uploading file:', file)
    const policy = await getOSSPolicy()
    console.log('[oss] Received OSS policy:', policy)

    // 只负责上传到OSS
    const ossResult = await uploadToOSS(file, policy)
    console.log('[oss] Upload result:', ossResult)

    const result = {
      url: ossResult,
      file: file
    }

    if (options.onSuccess) {
      options.onSuccess(result)
    }
    emit('success', result)
    console.log('[oss] Upload success:', result)
  } catch (error) {
    console.error('[oss] Upload error:', error)
    if (options.onError) {
      options.onError(error as Error)
    }
    emit('error', error as Error)
    ElMessage.error('[oss] Upload 上传失败: ' + ((error as Error).message || '未知错误'))
  }
}

// 上传成功的回调
const handleUploadSuccess = (response: { url: string; file: File }) => {
  ElMessage.success('上传成功')
  emit('success', {
    url: response.url,
    file: response.file
  })
}

// 上传失败的回调
const handleUploadError = (error: Error) => {
  ElMessage.error('---- 上传失败: ' + (error.message || '未知错误'))
  emit('error', error)
}
</script>

<style scoped></style>
