<template>
    <div class="contact-attachments-tab">
        <!-- 文件上传区域 -->
        <div class="upload-section">
            <el-card class="upload-card">
                <template #header>
                    <div class="upload-header">
                        <el-icon class="header-icon"><Folder /></el-icon>
                        <span>文件上传</span>
                    </div>
                </template>
                
                <el-upload
                    ref="uploadRef"
                    class="upload-area"
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    :data="uploadData"
                    :on-success="handleUploadSuccess"
                    :on-error="handleUploadError"
                    :on-progress="handleUploadProgress"
                    :before-upload="beforeUpload"
                    :file-list="fileList"
                    multiple
                    drag
                >
                    <div class="upload-content">
                        <el-icon class="upload-icon"><UploadFilled /></el-icon>
                        <div class="upload-text">
                            <p>将文件拖拽到此处，或<em>点击上传</em></p>
                            <p class="upload-tip">支持 PDF、Word、Excel、PPT、图片等格式，单个文件不超过 50MB</p>
                        </div>
                    </div>
                </el-upload>
            </el-card>
        </div>

        <!-- 文件列表区域 -->
        <div class="attachments-section">
            <el-card class="attachments-card">
                <template #header>
                    <div class="attachments-header">
                        <el-icon class="header-icon"><Document /></el-icon>
                        <span>附件列表 ({{ attachments.length }})</span>
                        <div class="header-actions">
                            <el-button size="small" @click="refreshAttachments">
                                <el-icon><Refresh /></el-icon>
                                刷新
                            </el-button>
                        </div>
                    </div>
                </template>
                
                <div class="attachments-container" v-loading="loadingAttachments">
                    <div v-if="attachments.length === 0" class="empty-state">
                        <el-empty description="暂无附件" />
                    </div>
                    
                    <div v-else class="attachments-grid">
                        <div
                            v-for="attachment in attachments"
                            :key="attachment.id"
                            class="attachment-item"
                        >
                            <div class="attachment-icon">
                                <el-icon :class="getFileIconClass(attachment.fileName)">
                                    <component :is="getFileIcon(attachment.fileName)" />
                                </el-icon>
                            </div>
                            
                            <div class="attachment-info">
                                <div class="attachment-name" :title="attachment.fileName">
                                    {{ attachment.fileName }}
                                </div>
                                <div class="attachment-meta">
                                    <span class="file-size">{{ formatFileSize(attachment.fileSize) }}</span>
                                    <span class="upload-time">{{ formatDate(attachment.createTime) }}</span>
                                </div>
                                <div class="attachment-uploader">
                                    上传者: {{ attachment.createBy || '未知' }}
                                </div>
                            </div>
                            
                            <div class="attachment-actions">
                                <el-button 
                                    size="small" 
                                    type="primary" 
                                    link 
                                    @click="handlePreviewFile(attachment)"
                                    v-if="canPreview(attachment.fileName)"
                                >
                                    <el-icon><View /></el-icon>
                                    预览
                                </el-button>
                                <el-button 
                                    size="small" 
                                    type="success" 
                                    link 
                                    @click="downloadFile(attachment)"
                                >
                                    <el-icon><Download /></el-icon>
                                    下载
                                </el-button>
                                <el-button 
                                    size="small" 
                                    type="danger" 
                                    link 
                                    @click="deleteFile(attachment)"
                                >
                                    <el-icon><Delete /></el-icon>
                                    删除
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </el-card>
        </div>

        <!-- 文件预览对话框 -->
        <el-dialog
            v-model="previewVisible"
            title="文件预览"
            width="80%"
            top="5vh"
            class="preview-dialog"
        >
            <div class="preview-container" v-if="previewFile">
                <div class="preview-header">
                    <span class="preview-filename">{{ previewFile.fileName }}</span>
                    <el-button type="primary" @click="downloadFile(previewFile)">
                        <el-icon><Download /></el-icon>
                        下载
                    </el-button>
                </div>
                
                <!-- 图片预览 -->
                <div v-if="isImageFile(previewFile.fileName)" class="image-preview">
                    <el-image 
                        :src="previewFile.fileUrl" 
                        fit="contain"
                        style="width: 100%; height: 500px;"
                    />
                </div>
                
                <!-- PDF预览 -->
                <div v-else-if="isPdfFile(previewFile.fileName)" class="pdf-preview">
                    <iframe 
                        :src="previewFile.fileUrl" 
                        style="width: 100%; height: 500px; border: none;"
                    />
                </div>
                
                <!-- 其他文件类型 -->
                <div v-else class="unsupported-preview">
                    <el-result
                        icon="warning"
                        title="此文件类型不支持在线预览"
                        sub-title="请下载后查看"
                    >
                        <template #extra>
                            <el-button type="primary" @click="downloadFile(previewFile)">
                                立即下载
                            </el-button>
                        </template>
                    </el-result>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ContactEntity } from '@/views/ContactManagement/types';
import { Delete, Document, Download, Folder, Refresh, UploadFilled, View } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed, onMounted, ref } from 'vue';

defineOptions({
  name: 'ContactAttachmentsTab'
});
interface ContactAttachment {
    id: number;
    contactId: number;
    fileName: string;
    fileSize: number;
    fileUrl: string;
    fileType: string;
    createBy?: string;
    createTime: string;
}

interface Props {
    entityData: ContactEntity;
}

const props = defineProps<Props>();

const emit = defineEmits<{
    (e: 'update:entity', value: Record<string, any>): void;
}>();

// 状态管理
const attachments = ref<ContactAttachment[]>([]);
const loadingAttachments = ref(false);
const uploadRef = ref();
const fileList = ref([]);
const previewVisible = ref(false);
const previewFile = ref<ContactAttachment | null>(null);

// 上传配置
const uploadUrl = ref('/api/upload');
const uploadHeaders = computed(() => ({
    'Authorization': `Bearer ${localStorage.getItem('token')}`
}));
const uploadData = computed(() => ({
    contactId: props.entityData.id,
    type: 'contact_attachment'
}));

// 组件挂载时加载数据
onMounted(() => {
    loadAttachments();
});

// 加载附件列表
const loadAttachments = async () => {
    if (!props.entityData.id) return;
    
    try {
        loadingAttachments.value = true;
        // TODO: 调用API获取联系人附件
        // const response = await getContactAttachments(props.entityData.id);
        // attachments.value = response.data || [];
        
        // 模拟数据
        attachments.value = [
            {
                id: 1,
                contactId: props.entityData.id!,
                fileName: '客户需求分析.pdf',
                fileSize: 2048576,
                fileUrl: '/files/contact/requirements.pdf',
                fileType: 'pdf',
                createBy: '张三',
                createTime: '2024-12-20 10:30:00'
            },
            {
                id: 2,
                contactId: props.entityData.id!,
                fileName: '产品介绍PPT.pptx',
                fileSize: 5242880,
                fileUrl: '/files/contact/product-intro.pptx',
                fileType: 'pptx',
                createBy: '李四',
                createTime: '2024-12-18 15:20:00'
            }
        ];
    } catch (error) {
        console.error('加载附件失败:', error);
        ElMessage.error('加载附件失败');
    } finally {
        loadingAttachments.value = false;
    }
};

// 文件上传前的校验
const beforeUpload = (file: File) => {
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
        ElMessage.error('文件大小不能超过50MB');
        return false;
    }
    return true;
};

// 上传成功回调
const handleUploadSuccess = (response: any, file: any) => {
    ElMessage.success('文件上传成功');
    loadAttachments(); // 重新加载附件列表
    fileList.value = []; // 清空文件列表
};

// 上传失败回调
const handleUploadError = (error: any, file: any) => {
    ElMessage.error('文件上传失败');
    console.error('Upload error:', error);
};

// 上传进度回调
const handleUploadProgress = (event: any, file: any) => {
    // 可以在这里显示上传进度
};

// 删除文件
const deleteFile = async (attachment: ContactAttachment) => {
    try {
        await ElMessageBox.confirm('确定要删除这个附件吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });
        
        // TODO: 调用API删除附件
        attachments.value = attachments.value.filter(item => item.id !== attachment.id);
        ElMessage.success('删除成功');
    } catch (error) {
        // 用户取消删除
    }
};

// 下载文件
const downloadFile = (attachment: ContactAttachment) => {
    // 创建下载链接
    const link = document.createElement('a');
    link.href = attachment.fileUrl;
    link.download = attachment.fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

// 预览文件
const handlePreviewFile = (attachment: ContactAttachment) => {
    previewFile.value = attachment;
    previewVisible.value = true;
};

// 刷新附件列表
const refreshAttachments = () => {
    loadAttachments();
};

// 获取文件图标
const getFileIcon = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
        case 'pdf':
            return Document;
        case 'doc':
        case 'docx':
            return Document;
        case 'xls':
        case 'xlsx':
            return Document;
        case 'ppt':
        case 'pptx':
            return Document;
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
            return View;
        default:
            return Document;
    }
};

// 获取文件图标样式类
const getFileIconClass = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
        case 'pdf':
            return 'file-icon-pdf';
        case 'doc':
        case 'docx':
            return 'file-icon-word';
        case 'xls':
        case 'xlsx':
            return 'file-icon-excel';
        case 'ppt':
        case 'pptx':
            return 'file-icon-ppt';
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
            return 'file-icon-image';
        default:
            return 'file-icon-default';
    }
};

// 判断是否可以预览
const canPreview = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    return ['pdf', 'jpg', 'jpeg', 'png', 'gif'].includes(ext || '');
};

// 判断是否为图片文件
const isImageFile = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif'].includes(ext || '');
};

// 判断是否为PDF文件
const isPdfFile = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    return ext === 'pdf';
};

// 格式化文件大小
const formatFileSize = (size: number) => {
    if (size < 1024) return size + ' B';
    if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB';
    if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + ' MB';
    return (size / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
};

// 格式化日期
const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleString();
};
</script>

<style scoped>
.contact-attachments-tab {
    padding: 20px;
}

.upload-section {
    margin-bottom: 24px;
}

.upload-card,
.attachments-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.06);
}

.upload-header,
.attachments-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #333;
}

.header-icon {
    font-size: 18px;
    color: var(--el-color-primary);
}

.header-actions {
    margin-left: auto;
}

.upload-area {
    width: 100%;
}

:deep(.el-upload) {
    width: 100%;
}

:deep(.el-upload-dragger) {
    width: 100%;
    height: 180px;
    border: 2px dashed var(--el-color-primary-light-5);
    border-radius: 12px;
    background: rgba(64, 158, 255, 0.02);
    transition: all 0.3s ease;
}

:deep(.el-upload-dragger:hover) {
    border-color: var(--el-color-primary);
    background: rgba(64, 158, 255, 0.05);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.upload-icon {
    font-size: 48px;
    color: var(--el-color-primary-light-3);
    margin-bottom: 16px;
}

.upload-text p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.upload-text em {
    color: var(--el-color-primary);
    font-style: normal;
    text-decoration: underline;
}

.upload-tip {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
}

.attachments-container {
    min-height: 200px;
}

.empty-state {
    text-align: center;
    padding: 40px 0;
}

.attachments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
}

.attachment-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.01);
    transition: all 0.3s ease;
}

.attachment-item:hover {
    border-color: var(--el-color-primary-light-5);
    background: rgba(64, 158, 255, 0.02);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.attachment-icon {
    margin-right: 12px;
    font-size: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background: rgba(64, 158, 255, 0.1);
}

.file-icon-pdf {
    color: #f56565;
}

.file-icon-word {
    color: #2563eb;
}

.file-icon-excel {
    color: #059669;
}

.file-icon-ppt {
    color: #dc2626;
}

.file-icon-image {
    color: #7c3aed;
}

.file-icon-default {
    color: #6b7280;
}

.attachment-info {
    flex: 1;
    min-width: 0;
}

.attachment-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.attachment-meta {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.attachment-uploader {
    font-size: 12px;
    color: #999;
}

.attachment-actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-left: 12px;
}

.preview-dialog {
    border-radius: 12px;
}

.preview-container {
    max-height: 80vh;
    overflow: hidden;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.preview-filename {
    font-weight: 500;
    color: #333;
}

.image-preview,
.pdf-preview {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.06);
}

.unsupported-preview {
    text-align: center;
    padding: 40px 20px;
}

@media (max-width: 768px) {
    .contact-attachments-tab {
        padding: 12px;
    }
    
    .attachments-grid {
        grid-template-columns: 1fr;
    }
    
    .attachment-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .attachment-actions {
        flex-direction: row;
        align-self: stretch;
        justify-content: flex-end;
        margin-left: 0;
    }
    
    .preview-dialog {
        width: 95% !important;
    }
}
</style>