import { DrawerConfig } from "~/components/CommonDrawer/types";
import { markRaw } from 'vue';
import LeadActivityTab from '../tabs/LeadActivityTab.vue';
import LeadAttachmentsTab from '../tabs/LeadAttachmentsTab.vue';
import LeadDetailsTab from '../tabs/LeadDetailsTab.vue';
import LeadOperationsTab from '../tabs/LeadOperationsTab.vue';

// 表格列配置接口
export interface TableColumn {
    type?: 'selection' | 'index' | 'expand';
    prop?: string;
    label?: string;
    width?: number | string;
    sortable?: boolean;
    fixed?: boolean | 'left' | 'right';
    link?: boolean;
}

// 表单字段配置接口
export interface FormField {
    label: string;
    field: string;
    component: string;
    colSpan: number;
    props?: Record<string, any>;
}

// 表单布局配置接口
export interface FormLayout {
    labelPosition: 'top' | 'left' | 'right';
    size: 'default' | 'small' | 'large';
    columns?: number;
}

// 表单配置接口
export interface FormConfig {
    layout: FormLayout;
    fields: FormField[];
}

// 抽屉字段配置接口
export interface DrawerField {
    label: string;
    field: string;
}

// 抽屉操作按钮配置接口
export interface DrawerAction {
    label: string;
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
    icon?: string;
    handler: (data: any) => void;
}

// 导航菜单项配置接口
export interface NavMenuItem {
    key: string;
    label: string;
    icon: string;
}

// 导航配置接口
export interface NavConfig {
    title: string;
    menuItems: NavMenuItem[];
}

// 表格列配置
export const tableColumns: TableColumn[] = [
    { type: 'selection', width: 55 },
    { prop: 'leadName', link: true, label: '线索名称', width: 180, sortable: true },
    // { prop: 'customerName', label: '客户名称', width: 180, sortable: true },
    { prop: 'leadSource', label: '线索来源', width: 180, sortable: true },
    // { prop: 'status', label: '线索状态', width: 180, sortable: true },
    { prop: 'phone', label: '手机', width: 180, sortable: true },
    { prop: 'email', label: '邮箱', width: 180, sortable: true },
    { prop: 'customerIndustry', label: '行业', width: 180, sortable: true },
    { prop: 'remarks', label: '备注', sortable: true }
];

// 新建线索表单配置
export const newLeadFormConfig: FormConfig = {
    layout: {
        labelPosition: 'top',
        size: 'default',
        columns: 2
    },
    fields: [
        { label: '线索名称', field: 'name', component: 'el-input', colSpan: 12 },
        { label: '客户名称', field: 'customerName', component: 'el-input', colSpan: 12 },
        { 
            label: '线索来源', 
            field: 'source', 
            component: 'el-select', 
            colSpan: 12,
            props: {
                options: [
                    { label: '广告', value: '广告' },
                    { label: '推荐', value: '推荐' },
                    { label: '搜索', value: '搜索' }
                ]
            }
        },
        { label: '手机', field: 'phone', component: 'el-input', colSpan: 12 },
        { 
            label: '状态', 
            field: 'status', 
            component: 'el-select', 
            colSpan: 12,
            props: {
                options: [
                    { label: '新线索', value: '新线索' },
                    { label: '跟进中', value: '跟进中' }
                ]
            }
        },
        { label: '邮箱', field: 'email', component: 'el-input', colSpan: 12 },
        { 
            label: '行业', 
            field: 'industry', 
            component: 'el-select', 
            colSpan: 12,
            props: {
                options: [
                    { label: 'IT', value: 'IT' },
                    { label: '教育', value: '教育' }
                ]
            }
        }
    ]
};

// 抽屉配置
export const drawerConfig: DrawerConfig = {
    menuItems: [
        {
            key: 'activity',
            label: '跟进记录',
            icon: 'Timer',
            component: markRaw(LeadActivityTab)
        },
        {
            key: 'details',
            label: '详细资料',
            icon: 'Document',
            component: markRaw(LeadDetailsTab)
        },
        {
            key: 'attachments',
            label: '附件',
            icon: 'Paperclip',
            component: markRaw(LeadAttachmentsTab),
            badge: true
        },
        {
            key: 'operations',
            label: '操作记录',
            icon: 'List',
            component: markRaw(LeadOperationsTab)
        }
    ]
}; 

export const navConfig: NavConfig = {
    title: '线索管理',
    menuItems: [
        {
            key: 'leads',
            label: '线索',
            icon: 'Edit'
        }
    ]
}; 