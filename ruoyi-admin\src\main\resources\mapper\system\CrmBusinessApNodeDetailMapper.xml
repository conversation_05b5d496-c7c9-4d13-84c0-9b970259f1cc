<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CrmBusinessApNodeDetailMapper">
    
    <resultMap type="CrmBusinessApNodeDetail" id="CrmBusinessApNodeDetailResult">
        <result property="detailId"    column="detail_id"    />
        <result property="nodeId"    column="node_id"    />
        <result property="displayX"    column="display_x"    />
        <result property="displayY"    column="display_y"    />
        <result property="width"    column="width"    />
        <result property="height"    column="height"    />
        <result property="nodeType"    column="node_type"    />
        <result property="isRecursive"    column="is_recursive"    />
        <result property="additionalInfo"    column="additional_info"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deptIds"    column="dept_ids"    />
        <result property="userIds"    column="user_ids"    />
        <result property="canEditArticle"    column="can_edit_article"    />
        <result property="canEditFee"    column="can_edit_fee"    />
    </resultMap>

    <sql id="selectCrmBusinessApNodeDetailVo">
        select detail_id, node_id, display_x, display_y, width, height, node_type, is_recursive, additional_info, create_time, update_time, dept_ids, user_ids, can_edit_article, can_edit_fee from crm_business_ap_node_detail
    </sql>

    <select id="selectCrmBusinessApNodeDetailList" parameterType="CrmBusinessApNodeDetail" resultMap="CrmBusinessApNodeDetailResult">
        <include refid="selectCrmBusinessApNodeDetailVo"/>
        <where>  
            <if test="nodeId != null "> and node_id = #{nodeId}</if>
            <if test="displayX != null "> and display_x = #{displayX}</if>
            <if test="displayY != null "> and display_y = #{displayY}</if>
            <if test="width != null "> and width = #{width}</if>
            <if test="height != null "> and height = #{height}</if>
            <if test="nodeType != null  and nodeType != ''"> and node_type = #{nodeType}</if>
            <if test="isRecursive != null "> and is_recursive = #{isRecursive}</if>
            <if test="additionalInfo != null  and additionalInfo != ''"> and additional_info = #{additionalInfo}</if>
            <if test="deptIds != null  and deptIds != ''"> and dept_ids = #{deptIds}</if>
            <if test="userIds != null  and userIds != ''"> and user_ids = #{userIds}</if>
            <if test="canEditArticle != null "> and can_edit_article = #{canEditArticle}</if>
            <if test="canEditFee != null "> and can_edit_fee = #{canEditFee}</if>
        </where>
    </select>
    
    <select id="selectCrmBusinessApNodeDetailByDetailId" parameterType="Long" resultMap="CrmBusinessApNodeDetailResult">
        <include refid="selectCrmBusinessApNodeDetailVo"/>
        where detail_id = #{detailId}
    </select>

    <insert id="insertCrmBusinessApNodeDetail" parameterType="CrmBusinessApNodeDetail" useGeneratedKeys="true" keyProperty="detailId">
        insert into crm_business_ap_node_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nodeId != null">node_id,</if>
            <if test="displayX != null">display_x,</if>
            <if test="displayY != null">display_y,</if>
            <if test="width != null">width,</if>
            <if test="height != null">height,</if>
            <if test="nodeType != null and nodeType != ''">node_type,</if>
            <if test="isRecursive != null">is_recursive,</if>
            <if test="additionalInfo != null">additional_info,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deptIds != null">dept_ids,</if>
            <if test="userIds != null">user_ids,</if>
            <if test="canEditArticle != null">can_edit_article,</if>
            <if test="canEditFee != null">can_edit_fee,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nodeId != null">#{nodeId},</if>
            <if test="displayX != null">#{displayX},</if>
            <if test="displayY != null">#{displayY},</if>
            <if test="width != null">#{width},</if>
            <if test="height != null">#{height},</if>
            <if test="nodeType != null and nodeType != ''">#{nodeType},</if>
            <if test="isRecursive != null">#{isRecursive},</if>
            <if test="additionalInfo != null">#{additionalInfo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deptIds != null">#{deptIds},</if>
            <if test="userIds != null">#{userIds},</if>
            <if test="canEditArticle != null">#{canEditArticle},</if>
            <if test="canEditFee != null">#{canEditFee},</if>
         </trim>
    </insert>

    <update id="updateCrmBusinessApNodeDetail" parameterType="CrmBusinessApNodeDetail">
        update crm_business_ap_node_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="nodeId != null">node_id = #{nodeId},</if>
            <if test="displayX != null">display_x = #{displayX},</if>
            <if test="displayY != null">display_y = #{displayY},</if>
            <if test="width != null">width = #{width},</if>
            <if test="height != null">height = #{height},</if>
            <if test="nodeType != null and nodeType != ''">node_type = #{nodeType},</if>
            <if test="isRecursive != null">is_recursive = #{isRecursive},</if>
            <if test="additionalInfo != null">additional_info = #{additionalInfo},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deptIds != null">dept_ids = #{deptIds},</if>
            <if test="userIds != null">user_ids = #{userIds},</if>
            <if test="canEditArticle != null">can_edit_article = #{canEditArticle},</if>
            <if test="canEditFee != null">can_edit_fee = #{canEditFee},</if>
        </trim>
        where detail_id = #{detailId}
    </update>

    <delete id="deleteCrmBusinessApNodeDetailByDetailId" parameterType="Long">
        delete from crm_business_ap_node_detail where detail_id = #{detailId}
    </delete>

    <delete id="deleteCrmBusinessApNodeDetailByDetailIds" parameterType="String">
        delete from crm_business_ap_node_detail where detail_id in 
        <foreach item="detailId" collection="array" open="(" separator="," close=")">
            #{detailId}
        </foreach>
    </delete>
</mapper>