package com.ruoyi.system.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.CrmBusinessPaymentDetails;
import com.ruoyi.system.service.ICrmBusinessPaymentDetailsService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * 回款明细Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Api(tags = "回款明细管理")
@RestController
@RequestMapping("/system/details")
public class CrmBusinessPaymentDetailsController extends BaseController {
    @Autowired
    private ICrmBusinessPaymentDetailsService crmBusinessPaymentDetailsService;

    /**
     * 查询回款明细列表
     */
    @ApiOperation(value = "查询回款明细列表", notes = "获取所有回款明细的列表")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "成功获取回款明细列表"),
        @ApiResponse(code = 401, message = "未授权"),
        @ApiResponse(code = 403, message = "禁止访问"),
        @ApiResponse(code = 404, message = "未找到")
    })
    @PreAuthorize("@ss.hasPermi('system:details:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam(value = "回款明细过滤条件") CrmBusinessPaymentDetails crmBusinessPaymentDetails) {
        startPage();
        List<CrmBusinessPaymentDetails> list = crmBusinessPaymentDetailsService
                .selectCrmBusinessPaymentDetailsList(crmBusinessPaymentDetails);
        return getDataTable(list);
    }

    /**
     * 导出回款明细列表
     */
    @ApiOperation(value = "导出回款明细列表", notes = "导出所有回款明细的列表到Excel")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "成功导出回款明细列表"),
        @ApiResponse(code = 401, message = "未授权"),
        @ApiResponse(code = 403, message = "禁止访问"),
        @ApiResponse(code = 404, message = "未找到")
    })
    @PreAuthorize("@ss.hasPermi('system:details:export')")
    @Log(title = "回款明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam(value = "回款明细过滤条件") CrmBusinessPaymentDetails crmBusinessPaymentDetails) {
        List<CrmBusinessPaymentDetails> list = crmBusinessPaymentDetailsService
                .selectCrmBusinessPaymentDetailsList(crmBusinessPaymentDetails);
        ExcelUtil<CrmBusinessPaymentDetails> util = new ExcelUtil<CrmBusinessPaymentDetails>(
                CrmBusinessPaymentDetails.class);
        util.exportExcel(response, list, "回款明细数据");
    }

    /**
     * 获取回款明细详细信息
     */
    @ApiOperation(value = "获取回款明细详细信息", notes = "根据ID获取回款明细的详细信息")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "成功获取回款明细详细信息"),
        @ApiResponse(code = 401, message = "未授权"),
        @ApiResponse(code = 403, message = "禁止访问"),
        @ApiResponse(code = 404, message = "未找到")
    })
    @PreAuthorize("@ss.hasPermi('system:details:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(value = "回款明细ID", required = true) @PathVariable("id") Long id) {
        return success(crmBusinessPaymentDetailsService.selectCrmBusinessPaymentDetailsById(id));
    }

    /**
     * 新增回款明细
     */
    @ApiOperation(value = "新增回款明细", notes = "新增一个回款明细")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "成功新增回款明细"),
        @ApiResponse(code = 401, message = "未授权"),
        @ApiResponse(code = 403, message = "禁止访问"),
        @ApiResponse(code = 404, message = "未找到")
    })
    @PreAuthorize("@ss.hasPermi('system:details:add')")
    @Log(title = "回款明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam(value = "回款明细实体", required = true) @RequestBody CrmBusinessPaymentDetails crmBusinessPaymentDetails) {
        return toAjax(crmBusinessPaymentDetailsService.insertCrmBusinessPaymentDetails(crmBusinessPaymentDetails));
    }

    /**
     * 修改回款明细
     */
    @ApiOperation(value = "修改回款明细", notes = "修改一个回款明细")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "成功修改回款明细"),
        @ApiResponse(code = 401, message = "未授权"),
        @ApiResponse(code = 403, message = "禁止访问"),
        @ApiResponse(code = 404, message = "未找到")
    })
    @PreAuthorize("@ss.hasPermi('system:details:edit')")
    @Log(title = "回款明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam(value = "回款明细实体", required = true) @RequestBody CrmBusinessPaymentDetails crmBusinessPaymentDetails) {
        return toAjax(crmBusinessPaymentDetailsService.updateCrmBusinessPaymentDetails(crmBusinessPaymentDetails));
    }

    /**
     * 删除回款明细
     */
    @ApiOperation(value = "删除回款明细", notes = "根据ID数组删除回款明细")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "成功删除回款明细"),
        @ApiResponse(code = 401, message = "未授权"),
        @ApiResponse(code = 403, message = "禁止访问"),
        @ApiResponse(code = 404, message = "未找到")
    })
    @PreAuthorize("@ss.hasPermi('system:details:remove')")
    @Log(title = "回款明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam(value = "回款明细ID数组", required = true) @PathVariable Long[] ids) {
        return toAjax(crmBusinessPaymentDetailsService.deleteCrmBusinessPaymentDetailsByIds(ids));
    }
}
