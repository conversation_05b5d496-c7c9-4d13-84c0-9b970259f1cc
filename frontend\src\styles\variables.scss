// 主题色
$color-primary: #409EFF !default;
$color-success: #67C23A !default;
$color-warning: #E6A23C !default;
$color-danger: #F56C6C !default;
$color-info: #909399 !default;

// 文字颜色
$color-text-primary: #303133 !default;
$color-text-regular: #606266 !default;
$color-text-secondary: #909399 !default;
$color-text-placeholder: #C0C4CC !default;

// 边框颜色
$border-color-base: #DCDFE6 !default;
$border-color-light: #E4E7ED !default;
$border-color-lighter: #EBEEF5 !default;
$border-color-extra-light: #F2F6FC !default;

// 背景颜色
$background-color-base: #F5F7FA !default;

// 渐变色背景
$gradient-background-login: linear-gradient(135deg, #E8F3FF 0%, #F0F7FF 50%, #E6F1FF 100%) !default;
$gradient-background-header: linear-gradient(135deg, #b0d6fc 0%, #F0F7FF 50%,  #FFFFFF 100%) !default;
$gradient-background-main: linear-gradient(135deg, #F0F7FF 0%, #FFFFFF 100%) !default;
$linear-gradient-background-login: linear-gradient(135deg, #E8F3FF 0%, #F0F7FF 50%, #E6F1FF 100%);
$linear-gradient-background-header: linear-gradient(135deg, #b0d6fc 0%, #F0F7FF 50%,  #FFFFFF 100%);
$linear-gradient-background-main: linear-gradient(135deg, #6bb5ff 0%, #68f3fd 100%);
// 字体大小
$font-size-extra-large: 20px !default;
$font-size-large: 18px !default;
$font-size-medium: 16px !default;
$font-size-base: 14px !default;
$font-size-small: 13px !default;
$font-size-extra-small: 12px !default;

// 边框圆角
$border-radius-base: 4px !default;
$border-radius-small: 2px !default;
$border-radius-circle: 100% !default;

// 间距
$spacing-base: 8px !default;
$spacing-large: 16px !default;
$spacing-extra-large: 24px !default;

// 阴影
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04) !default;
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !default;

:export {
  colorPrimary: $color-primary;
  colorSuccess: $color-success;
  colorWarning: $color-warning;
  colorDanger: $color-danger;
  colorInfo: $color-info;
  gradientBackgroundLogin: $gradient-background-login;
  gradientBackgroundHeader: $gradient-background-header;
  gradientBackgroundMain: $gradient-background-main;
} 

// Element Plus CSS 变量覆盖
:root {
  // 头部样式 - 恢复合理的值
  // --ep-header-padding: 0 30px !important;
  --ep-header-height: 60px;
  
  // 主色调
  --ep-color-primary: #409EFF;
  
  // 表格样式
  --ep-table-border-color: #EBEEF5;
  --ep-table-header-bg-color: #FAFAFA;
  
  // 按钮样式
  --ep-button-border-radius: 4px;
  
  // 字体大小
  --ep-font-size-base: 14px;
  --ep-font-size-medium: 16px;
  --ep-font-size-large: 18px;
}