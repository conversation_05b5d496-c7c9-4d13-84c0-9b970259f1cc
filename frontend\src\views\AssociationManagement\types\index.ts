import type { FormConfig } from '@/types';
import { FilterType } from '@/types';
import { LeadEntity } from '@/types/entity';
import { drawerConfig, navConfig, tableColumns, tableOperations } from '../config';
import { leadsFilterConfig } from '../config/filterConfig';


export interface LeadsManagementState {
    activeTab: string;
    filterType: FilterType;
    searchInput: string;
    totalLeads: number;
    queryParams: {
        pageNum: number;
        pageSize: number;
        searchKeyword: string;
        filterType: FilterType;
    };
    leads: LeadEntity[];
    leadDialogVisible: boolean;
    newLead: LeadEntity;
    drawerVisible: boolean;
    currentLead: LeadEntity;
    tableColumns: typeof tableColumns;
    newLeadFormConfig: FormConfig;
    drawerConfig: typeof drawerConfig;
    tableOperations: typeof tableOperations;
    navConfig: typeof navConfig;
    leadsFilterConfig: typeof leadsFilterConfig;
    loading: boolean;
}

export const DEFAULT_LEAD: LeadEntity = {
    id: 0,
    name: '',
    customerName: '',
    source: '',
    status: '',
    phone: '',
    email: '',
    industry: '',
    owner: 0
}; 