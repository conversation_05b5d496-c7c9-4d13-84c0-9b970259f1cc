import type { CommonFilterConfig } from '@/types';

// 筛选类型选项
const filterOptions = [
    { label: '全部商机', value: 'all' },
    { label: '我负责的', value: 'mine' },
    { label: '下属负责的', value: 'subordinate' },
    { label: '我关注的商机', value: 'following' }
];

// 商机筛选配置
export const opportunityFilterConfig: CommonFilterConfig = {
    search: {
        placeholder: '商机名称/客户名称',
        width: '240px',
        icon: 'Search',
        debounceTime: 300
    },
    filter: {
        label: '显示：',
        options: filterOptions,
        buttonStyle: true,
        size: 'default'
    }
}; 