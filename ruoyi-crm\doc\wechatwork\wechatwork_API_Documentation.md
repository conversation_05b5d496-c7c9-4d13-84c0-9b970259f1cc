# 控制器接口文档

## ChatMessageController
基础路径：`/wxcom/message`

### 接口列表
- **同步聊天记录**
  - 方法：POST
  - 路径：`/sync/{chatId}`
  - 参数：`chatId` (路径参数)
  - 功能：同步指定聊天ID的聊天记录

- **获取单聊记录**
  - 方法：GET
  - 路径：`/messages`
  - 参数：`fromUserId`, `toUserId`, `startTime`, `endTime`
  - 功能：获取两个用户之间的单聊记录

- **获取群聊记录**
  - 方法：GET
  - 路径：`/room/messages`
  - 参数：`roomId`, `startTime`, `endTime`
  - 功能：获取指定群聊的聊天记录

## MessagePushController
基础路径：`/wecom/message`

### 消息结构体

#### MessageRequest
```java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MessageRequest {
    private String touser;  // 接收人
    private String toparty; // 接收部门
    private String totag;   // 接收标签
    private String msgtype; // 消息类型
    private Integer agentid; // 应用ID
    private MessageContent text; // 文本消息
    private MessageContent image; // 图片消息
    private MessageContent video; // 视频消息
    private MessageContent file;  // 文件消息
    private NewsContent news;     // 图文消息
    private Integer safe; // 是否保密
    private Integer enable_id_trans; // 是否开启ID转译
    private Integer enable_duplicate_check; // 是否开启重复检查
    private Integer duplicate_check_interval; // 重复检查间隔
}
```

#### MessageContent
```java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MessageContent {
    private String content;      // 文本消息内容
    private String media_id;     // 媒体文件ID
    private String title;        // 视频消息标题
    private String description;  // 视频消息描述
}
```

#### NewsContent
```java
@Data
@Builder
public class NewsContent {
    private List<NewsArticle> articles; // 图文消息列表
}

@Data
@Builder
class NewsArticle {
    private String title;       // 图文消息标题
    private String description; // 图文消息描述
    private String url;         // 图文消息链接
    private String picurl;      // 图文消息图片链接
}
```

### 接口列表
- **发送消息**
  - 方法：POST
  - 路径：`/send`
  - 参数：`request` (请求体)
  - 功能：发送消息

- **发送文本消息**
  - 方法：POST
  - 路径：`/send/text`
  - 参数：`touser`, `content`
  - 功能：发送文本消息

- **发送图片消息**
  - 方法：POST
  - 路径：`/send/image`
  - 参数：`touser`, `mediaId`
  - 功能：发送图片消息

- **发送视频消息**
  - 方法：POST
  - 路径：`/send/video`
  - 参数：`touser`, `mediaId`, `title`, `description`
  - 功能：发送视频消息

- **发送文件消息**
  - 方法：POST
  - 路径：`/send/file`
  - 参数：`touser`, `mediaId`
  - 功能：发送文件消息

- **发送图文消息**
  - 方法：POST
  - 路径：`/send/news`
  - 参数：`touser`, `title`, `description`, `url`, `picurl`
  - 功能：发送图文消息

## UUTController
基础路径：`/wxcom/user`

### 接口列表
- **创建用户**
  - 方法：POST
  - 路径：`/create`
  - 参数：`userData` (请求体)
  - 功能：创建新用户

- **获取用户信息**
  - 方法：GET
  - 路径：`/info/{userId}`
  - 参数：`userId` (路径参数)
  - 功能：获取指定用户的信息

- **获取用户列表**
  - 方法：GET
  - 路径：`/list`
  - 参数：`departmentId` (默认值：1), `fetchChild` (默认值：true)
  - 功能：获取用户列表

- **更新用户信息**
  - 方法：PUT
  - 路径：`/update`
  - 参数：`userData` (请求体)
  - 功能：更新用户信息

- **删除用户**
  - 方法：DELETE
  - 路径：`/delete/{userId}`
  - 参数：`userId` (路径参数)
  - 功能：删除指定用户

## WeComDepartmentController
基础路径：`/wecom/department`

### 接口列表
- **获取子部门列表**
  - 方法：GET
  - 路径：`/list`
  - 参数：`id` (可选)
  - 功能：获取子部门列表
