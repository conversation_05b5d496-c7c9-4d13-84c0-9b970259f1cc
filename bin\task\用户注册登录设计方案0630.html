<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM系统用户注册登录设计方案</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 20px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 25px;
        }
        .mermaid {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .scenario {
            background-color: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
        }
        .issue {
            background-color: #ffeaa7;
            border-left: 4px solid #fdcb6e;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({startOnLoad:true});
    </script>
</head>
<body>
    <div class="container">
        <h1>CRM系统用户注册登录设计方案</h1>
        
        <h2>1. 系统概述</h2>
        <p>CRM系统需要支持多种用户注册和登录方式，包括内部工作人员（销售、财务等）和外部客户用户。系统设计需要满足以下需求：</p>
        <ul>
            <li><strong>内部员工：</strong>通过企业微信扫码登录/注册，并绑定手机号</li>
            <li><strong>外部客户：</strong>通过手机短信验证码"润物无声"式注册</li>
            <li><strong>统一管理：</strong>所有用户都需要手机号作为最终绑定标识</li>
        </ul>

        <h2>2. 现有表结构分析</h2>
        <p>根据现有数据库表分析，我们已有以下相关表：</p>
        <table>
            <tr>
                <th>表名</th>
                <th>用途</th>
                <th>关键字段</th>
            </tr>
            <tr>
                <td>sys_user</td>
                <td>系统用户基础信息</td>
                <td>user_id, user_name, phonenumber, email</td>
            </tr>
            <tr>
                <td>crm_thirdparty_wechat</td>
                <td>企业微信第三方登录关联</td>
                <td>user_id, wecom_user_id</td>
            </tr>
            <tr>
                <td>crm_wecom_config</td>
                <td>企业微信配置</td>
                <td>corp_id, corp_secret, agent_id</td>
            </tr>
        </table>

        <h2>3. 新增表设计方案</h2>
        
        <h3>3.1 用户注册表 (crm_user_registration)</h3>
        <div class="code-block">
CREATE TABLE `crm_user_registration` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '注册记录ID',
  `phone_number` varchar(20) NOT NULL COMMENT '手机号码',
  `registration_type` varchar(20) NOT NULL COMMENT '注册类型：wechat-企业微信,sms-短信验证',
  `wechat_union_id` varchar(100) DEFAULT NULL COMMENT '企业微信唯一ID',
  `verification_code` varchar(10) DEFAULT NULL COMMENT '验证码',
  `verification_expire_time` datetime DEFAULT NULL COMMENT '验证码过期时间',
  `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否已验证',
  `user_id` bigint(20) DEFAULT NULL COMMENT '关联的用户ID',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态：pending-待处理,verified-已验证,completed-已完成',
  `user_type` varchar(20) DEFAULT 'customer' COMMENT '用户类型：employee-员工,customer-客户',
  `source_data` json DEFAULT NULL COMMENT '来源数据(如企业微信用户信息)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone_type` (`phone_number`, `registration_type`),
  KEY `idx_wechat_union_id` (`wechat_union_id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='用户注册记录表';
        </div>

        <h3>3.2 用户认证方式表 (crm_user_auth_methods)</h3>
        <div class="code-block">
CREATE TABLE `crm_user_auth_methods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '认证方式ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `auth_type` varchar(20) NOT NULL COMMENT '认证类型：wechat-企业微信,phone-手机号',
  `auth_identifier` varchar(100) NOT NULL COMMENT '认证标识(企业微信ID或手机号)',
  `is_primary` tinyint(1) DEFAULT 0 COMMENT '是否主要认证方式',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否有效',
  `bind_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `metadata` json DEFAULT NULL COMMENT '附加元数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_auth_type` (`user_id`, `auth_type`),
  UNIQUE KEY `uk_auth_identifier` (`auth_type`, `auth_identifier`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_auth_type` (`auth_type`)
) ENGINE=InnoDB COMMENT='用户认证方式表';
        </div>

        <h3>3.3 客户线索表增强 (crm_customer_leads)</h3>
        <div class="code-block">
CREATE TABLE `crm_customer_leads` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '线索ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '关联用户ID(如果已注册)',
  `phone_number` varchar(20) NOT NULL COMMENT '手机号码',
  `source_type` varchar(50) NOT NULL COMMENT '来源类型：3d_upload-3D文件上传,website-官网,other-其他',
  `lead_data` json DEFAULT NULL COMMENT '线索数据(如上传的3D文件信息)',
  `status` varchar(20) DEFAULT 'new' COMMENT '状态：new-新建,contacted-已联系,converted-已转化,closed-已关闭',
  `assigned_to` bigint(20) DEFAULT NULL COMMENT '分配给的销售人员ID',
  `evaluation_result` text DEFAULT NULL COMMENT '评估结果',
  `contact_notes` text DEFAULT NULL COMMENT '联系记录',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_assigned_to` (`assigned_to`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='客户线索表';
        </div>

        <h2>4. 业务流程设计</h2>

        <h3>4.1 企业微信员工注册登录流程</h3>
        <div class="mermaid">
flowchart TD
    A[员工打开CRM系统] --> B[点击企业微信登录]
    B --> C[跳转到企业微信授权页面]
    C --> D[用户确认授权]
    D --> E[获取企业微信用户信息]
    E --> F{是否已注册?}
    
    F -->|是| G[直接登录成功]
    F -->|否| H[创建注册记录]
    
    H --> I[提示绑定手机号]
    I --> J[用户输入手机号]
    J --> K[发送短信验证码]
    K --> L[用户输入验证码]
    L --> M{验证码正确?}
    
    M -->|否| N[提示错误，重新输入]
    N --> L
    
    M -->|是| O[创建用户账户]
    O --> P[绑定企业微信和手机号]
    P --> Q[注册完成，自动登录]
    
    G --> R[进入系统首页]
    Q --> R
</div>

        <h3>4.2 客户用户"润物无声"注册流程</h3>
        <div class="mermaid">
flowchart TD
    A[客户访问前端上传页面] --> B[选择3D文件上传]
    B --> C[填写基本信息]
    C --> D[输入手机号码]
    D --> E[点击获取验证码]
    E --> F[系统发送短信验证码]
    F --> G[系统自动创建注册记录]
    G --> H[用户输入验证码]
    H --> I{验证码正确?}
    
    I -->|否| J[提示错误]
    J --> H
    
    I -->|是| K[系统自动创建用户账户]
    K --> L[创建客户线索记录]
    L --> M[文件上传成功]
    M --> N[显示上传成功页面]
    
    N --> O[后台销售人员收到线索通知]
    O --> P[销售人员查看线索详情]
    P --> Q[销售人员进行评估]
    Q --> R[通过注册手机号联系客户]
</div>

        <h3>4.3 后台销售处理客户线索流程</h3>
        <div class="mermaid">
flowchart TD
    A[系统接收到客户线索] --> B[自动分配给销售人员]
    B --> C[销售人员登录系统]
    C --> D[查看待处理线索列表]
    D --> E[点击查看线索详情]
    E --> F[查看客户上传的3D文件]
    F --> G[进行技术评估]
    G --> H[填写评估报告]
    H --> I{是否有合作机会?}
    
    I -->|否| J[标记线索为已关闭]
    I -->|是| K[通过手机号联系客户]
    
    K --> L[电话沟通需求]
    L --> M[记录沟通内容]
    M --> N[安排后续跟进]
    N --> O[线索转化为商机]
    
    J --> P[线索处理完成]
    O --> P
</div>

        <h2>5. 时序图设计</h2>

        <h3>5.1 企业微信登录时序图</h3>
        <div class="mermaid">
sequenceDiagram
    participant U as 用户
    participant F as 前端页面
    participant B as 后端服务
    participant W as 企业微信API
    participant D as 数据库
    
    U->>F: 点击企业微信登录
    F->>W: 跳转到企业微信授权页面
    W->>U: 显示授权确认页面
    U->>W: 确认授权
    W->>F: 返回授权码
    F->>B: 发送授权码
    B->>W: 使用授权码获取用户信息
    W->>B: 返回用户信息(unionid等)
    B->>D: 查询crm_thirdparty_wechat表
    
    alt 用户已存在
        D->>B: 返回用户信息
        B->>F: 登录成功，返回JWT token
        F->>U: 跳转到系统首页
    else 用户不存在
        B->>D: 创建注册记录到crm_user_registration
        B->>F: 返回需要绑定手机号提示
        F->>U: 显示手机号绑定页面
        U->>F: 输入手机号
        F->>B: 提交手机号
        B->>B: 发送短信验证码
        B->>F: 返回验证码已发送
        F->>U: 显示验证码输入框
        U->>F: 输入验证码
        F->>B: 提交验证码
        B->>B: 验证码校验
        B->>D: 创建sys_user记录
        B->>D: 创建crm_thirdparty_wechat关联
        B->>D: 创建crm_user_auth_methods记录
        B->>F: 注册成功，返回JWT token
        F->>U: 跳转到系统首页
    end
</div>

        <h3>5.2 客户上传文件注册时序图</h3>
        <div class="mermaid">
sequenceDiagram
    participant C as 客户
    participant F as 前端页面
    participant B as 后端服务
    participant S as 短信服务
    participant D as 数据库
    participant Sales as 销售系统
    
    C->>F: 访问文件上传页面
    F->>C: 显示上传表单
    C->>F: 选择3D文件并填写信息
    C->>F: 输入手机号
    F->>B: 提交手机号请求验证码
    B->>S: 发送短信验证码
    B->>D: 创建注册记录(pending状态)
    B->>F: 返回验证码已发送
    F->>C: 显示验证码输入框
    C->>F: 输入验证码
    F->>B: 提交验证码和文件
    B->>B: 验证码校验
    
    alt 验证码正确
        B->>D: 创建sys_user记录
        B->>D: 创建crm_user_auth_methods记录
        B->>D: 创建crm_customer_leads记录
        B->>D: 更新注册记录状态为completed
        B->>F: 上传成功
        F->>C: 显示成功页面
        B->>Sales: 发送线索通知
        Sales->>Sales: 分配给销售人员
    else 验证码错误
        B->>F: 返回验证码错误
        F->>C: 提示重新输入
    end
</div>

        <h3>5.3 销售人员处理线索时序图</h3>
        <div class="mermaid">
sequenceDiagram
    participant S as 销售人员
    participant CRM as CRM系统
    participant D as 数据库
    participant Phone as 电话系统
    participant C as 客户
    
    CRM->>S: 线索通知
    S->>CRM: 登录系统
    CRM->>D: 查询待处理线索
    D->>CRM: 返回线索列表
    CRM->>S: 显示线索列表
    S->>CRM: 点击查看线索详情
    CRM->>D: 查询线索详情和文件
    D->>CRM: 返回详细信息
    CRM->>S: 显示线索详情页面
    S->>S: 查看3D文件并评估
    S->>CRM: 填写评估结果
    CRM->>D: 保存评估结果
    S->>Phone: 通过注册手机号联系客户
    Phone->>C: 电话接通
    S->>C: 沟通需求和方案
    C->>S: 反馈意见
    S->>CRM: 记录沟通内容
    CRM->>D: 保存跟进记录
    S->>CRM: 安排后续跟进
    CRM->>D: 更新线索状态
</div>

        <h2>6. 表关系设计</h2>
        <div class="mermaid">
erDiagram
    sys_user {
        bigint user_id PK
        varchar user_name
        varchar nick_name
        varchar phonenumber
        varchar email
        char status
    }
    
    crm_user_registration {
        bigint id PK
        varchar phone_number
        varchar registration_type
        varchar wechat_union_id
        varchar verification_code
        datetime verification_expire_time
        tinyint is_verified
        bigint user_id FK
        varchar status
        varchar user_type
        json source_data
    }
    
    crm_user_auth_methods {
        bigint id PK
        bigint user_id FK
        varchar auth_type
        varchar auth_identifier
        tinyint is_primary
        tinyint is_active
        datetime bind_time
        json metadata
    }
    
    crm_thirdparty_wechat {
        bigint id PK
        bigint user_id FK
        varchar wecom_user_id
    }
    
    crm_customer_leads {
        bigint id PK
        bigint user_id FK
        varchar phone_number
        varchar source_type
        json lead_data
        varchar status
        bigint assigned_to FK
        text evaluation_result
    }
    
    sys_user ||--o{ crm_user_registration : "一个用户可能有多个注册记录"
    sys_user ||--o{ crm_user_auth_methods : "一个用户可以有多种认证方式"
    sys_user ||--o| crm_thirdparty_wechat : "一对一企业微信绑定"
    sys_user ||--o{ crm_customer_leads : "一个用户可能对应多个线索"
    crm_user_registration ||--|| sys_user : "注册记录关联用户"
</div>

        <h2>7. 关键技术点</h2>

        <div class="highlight">
            <h3>7.1 企业微信集成</h3>
            <ul>
                <li><strong>OAuth2.0授权流程：</strong>使用企业微信提供的OAuth接口进行用户身份验证</li>
                <li><strong>用户信息获取：</strong>通过企业微信API获取用户的unionid、用户名等基本信息</li>
                <li><strong>唯一标识：</strong>使用企业微信的unionid作为用户在系统中的唯一标识</li>
            </ul>
        </div>

        <div class="highlight">
            <h3>7.2 短信验证码机制</h3>
            <ul>
                <li><strong>验证码生成：</strong>6位数字随机验证码，有效期5分钟</li>
                <li><strong>频率限制：</strong>同一手机号1分钟内只能发送一条，每天最多10条</li>
                <li><strong>安全策略：</strong>验证码错误超过5次锁定账户10分钟</li>
            </ul>
        </div>

        <div class="highlight">
            <h3>7.3 润物无声注册策略</h3>
            <ul>
                <li><strong>预注册：</strong>用户输入手机号发送验证码时就创建注册记录</li>
                <li><strong>无感知：</strong>用户只需要验证手机号，系统自动完成用户创建</li>
                <li><strong>线索转化：</strong>注册成功自动创建客户线索，分配给销售人员</li>
            </ul>
        </div>

        <h2>8. 安全考虑</h2>

        <div class="issue">
            <h3>8.1 数据安全</h3>
            <ul>
                <li><strong>手机号加密：</strong>存储时对手机号进行加密处理</li>
                <li><strong>验证码加密：</strong>验证码在数据库中加密存储</li>
                <li><strong>敏感信息脱敏：</strong>日志中的手机号等敏感信息进行脱敏处理</li>
            </ul>
        </div>

        <div class="issue">
            <h3>8.2 访问控制</h3>
            <ul>
                <li><strong>角色权限：</strong>区分员工和客户角色，设置不同的系统访问权限</li>
                <li><strong>API权限：</strong>不同类型的API接口设置不同的访问权限</li>
                <li><strong>会话管理：</strong>JWT token设置合理的有效期，支持token刷新</li>
            </ul>
        </div>

        <h2>9. 实施计划</h2>

        <div class="scenario">
            <h3>阶段一：数据库表设计和创建</h3>
            <ul>
                <li>创建新增的三个表：用户注册表、认证方式表、客户线索表</li>
                <li>建立表之间的外键关系和索引</li>
                <li>迁移现有数据到新表结构</li>
            </ul>
        </div>

        <div class="scenario">
            <h3>阶段二：后端API开发</h3>
            <ul>
                <li>企业微信OAuth登录接口</li>
                <li>短信验证码发送和验证接口</li>
                <li>用户注册和认证接口</li>
                <li>客户线索管理接口</li>
            </ul>
        </div>

        <div class="scenario">
            <h3>阶段三：前端界面开发</h3>
            <ul>
                <li>企业微信登录页面</li>
                <li>手机号绑定页面</li>
                <li>客户文件上传页面</li>
                <li>销售线索管理页面</li>
            </ul>
        </div>

        <div class="scenario">
            <h3>阶段四：集成测试和部署</h3>
            <ul>
                <li>单元测试和集成测试</li>
                <li>安全测试和性能测试</li>
                <li>生产环境部署</li>
                <li>用户培训和系统上线</li>
            </ul>
        </div>

        <h2>10. 监控和维护</h2>

        <table>
            <tr>
                <th>监控项目</th>
                <th>监控指标</th>
                <th>报警阈值</th>
                <th>处理措施</th>
            </tr>
            <tr>
                <td>短信发送</td>
                <td>发送成功率</td>
                <td>< 95%</td>
                <td>检查短信服务商状态</td>
            </tr>
            <tr>
                <td>企业微信登录</td>
                <td>登录成功率</td>
                <td>< 98%</td>
                <td>检查企业微信API状态</td>
            </tr>
            <tr>
                <td>注册转化</td>
                <td>注册完成率</td>
                <td>< 80%</td>
                <td>优化注册流程</td>
            </tr>
            <tr>
                <td>线索处理</td>
                <td>线索处理时效</td>
                <td>> 24小时</td>
                <td>提醒销售人员处理</td>
            </tr>
        </table>

        <h2>11. 总结</h2>
        <p>本设计方案提供了一个完整的双轨用户注册登录系统，既满足了内部员工通过企业微信便捷登录的需求，又实现了外部客户的无感知注册体验。通过统一的手机号绑定机制，确保了用户身份的唯一性和可追溯性，为后续的客户关系管理奠定了良好的基础。</p>
        
        <div class="highlight">
            <p><strong>核心优势：</strong></p>
            <ul>
                <li>双轨注册机制满足不同用户群体需求</li>
                <li>润物无声的客户注册体验提升转化率</li>
                <li>统一的手机号绑定确保用户唯一性</li>
                <li>完整的线索管理流程支持业务发展</li>
            </ul>
        </div>
    </div>
</body>
</html>
