<template>
  <div class="sales-metrics-grid">
    <div class="metrics-row">
      <div class="metric-item" v-for="metric in topMetrics" :key="metric.key">
        <div class="metric-icon">
          <el-icon :color="metric.iconColor">
            <component :is="metric.icon" />
          </el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-label">{{ metric.label }}</div>
          <div class="metric-value">{{ metric.value }}</div>
          <div class="metric-change" :class="metric.changeType">
            <span>较上月</span>
            <span class="change-value">{{ metric.change }}</span>
          </div>
        </div>
        <div class="metric-chart">
          <div class="mini-chart" :style="{ height: '40px' }">
            <!-- 这里可以放置迷你图表 -->
            <svg width="100%" height="40">
              <path 
                :d="generateTrendPath(metric.trend)" 
                :stroke="metric.iconColor" 
                stroke-width="2" 
                fill="none"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
    
    <div class="metrics-row">
      <div class="metric-item" v-for="metric in bottomMetrics" :key="metric.key">
        <div class="metric-icon">
          <el-icon :color="metric.iconColor">
            <component :is="metric.icon" />
          </el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-label">{{ metric.label }}</div>
          <div class="metric-value">{{ metric.value }}</div>
          <div class="metric-change" :class="metric.changeType">
            <span>较上月</span>
            <span class="change-value">{{ metric.change }}</span>
          </div>
        </div>
        <div class="metric-chart">
          <div class="mini-chart" :style="{ height: '40px' }">
            <svg width="100%" height="40">
              <path 
                :d="generateTrendPath(metric.trend)" 
                :stroke="metric.iconColor" 
                stroke-width="2" 
                fill="none"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { User, Phone, Opportunity, Money, Files, MessageBox, Notebook, Share } from '@element-plus/icons-vue'

// 模拟数据
const metricsData = [
  {
    key: 'newCustomers',
    label: '新增客户',
    value: '0 人',
    change: '0%',
    changeType: 'neutral',
    icon: User,
    iconColor: '#1677ff',
    trend: [10, 15, 12, 18, 22, 20, 25, 30]
  },
  {
    key: 'newContacts',
    label: '新增联系人',
    value: '0 人',
    change: '0%',
    changeType: 'neutral',
    icon: Phone,
    iconColor: '#52c41a',
    trend: [5, 8, 12, 10, 15, 18, 16, 20]
  },
  {
    key: 'newOpportunities',
    label: '新增商机',
    value: '0 个',
    change: '0%',
    changeType: 'neutral',
    icon: Opportunity,
    iconColor: '#faad14',
    trend: [3, 6, 4, 8, 12, 10, 14, 16]
  },
  {
    key: 'newContracts',
    label: '新增合同',
    value: '0 个',
    change: '0%',
    changeType: 'neutral',
    icon: Files,
    iconColor: '#722ed1',
    trend: [1, 3, 2, 5, 7, 6, 9, 11]
  },
  {
    key: 'contractAmount',
    label: '合同金额',
    value: '0 元',
    change: '0%',
    changeType: 'neutral',
    icon: Money,
    iconColor: '#f5222d',
    trend: [100, 150, 120, 180, 220, 200, 250, 300]
  },
  {
    key: 'opportunityAmount',
    label: '商机金额',
    value: '0 元',
    change: '0%',
    changeType: 'neutral',
    icon: MessageBox,
    iconColor: '#13c2c2',
    trend: [80, 120, 100, 140, 180, 160, 200, 240]
  },
  {
    key: 'paymentAmount',
    label: '回款金额',
    value: '0 元',
    change: '0%',
    changeType: 'neutral',
    icon: Notebook,
    iconColor: '#eb2f96',
    trend: [60, 90, 80, 110, 140, 130, 160, 190]
  },
  {
    key: 'newFollowups',
    label: '新增跟进记录',
    value: '0 条',
    change: '0%',
    changeType: 'neutral',
    icon: Share,
    iconColor: '#fa8c16',
    trend: [20, 30, 25, 35, 45, 40, 50, 60]
  }
]

const topMetrics = computed(() => metricsData.slice(0, 4))
const bottomMetrics = computed(() => metricsData.slice(4, 8))

// 生成趋势线路径
const generateTrendPath = (trend: number[]) => {
  if (!trend || trend.length === 0) return ''
  
  const width = 100
  const height = 40
  const padding = 5
  
  const maxValue = Math.max(...trend)
  const minValue = Math.min(...trend)
  const range = maxValue - minValue || 1
  
  const points = trend.map((value, index) => {
    const x = padding + (index / (trend.length - 1)) * (width - 2 * padding)
    const y = height - padding - ((value - minValue) / range) * (height - 2 * padding)
    return `${x},${y}`
  })
  
  return `M ${points.join(' L ')}`
}
</script>

<style lang="scss" scoped>
.sales-metrics-grid {
  padding: 24px;
  
  .metrics-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    @media (max-width: 1200px) {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
  
  .metric-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #fafbfc;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    
    &:hover {
      background: #f0f7ff;
      border-color: #d6e4ff;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.1);
    }
    
    .metric-icon {
      margin-right: 16px;
      
      .el-icon {
        font-size: 32px;
        padding: 8px;
        background: rgba(22, 119, 255, 0.1);
        border-radius: 8px;
      }
    }
    
    .metric-content {
      flex: 1;
      min-width: 0;
      
      .metric-label {
        font-size: 12px;
        color: #86909c;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .metric-value {
        font-size: 24px;
        font-weight: 600;
        color: #1f2329;
        margin-bottom: 4px;
        line-height: 1.2;
      }
      
      .metric-change {
        font-size: 11px;
        display: flex;
        align-items: center;
        gap: 4px;
        
        &.positive {
          color: #52c41a;
          
          .change-value::before {
            content: '+';
          }
        }
        
        &.negative {
          color: #f5222d;
        }
        
        &.neutral {
          color: #86909c;
        }
        
        .change-value {
          font-weight: 500;
        }
      }
    }
    
    .metric-chart {
      width: 100px;
      margin-left: 12px;
      
      .mini-chart {
        width: 100%;
        opacity: 0.7;
        
        svg {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

// 为不同指标设置不同的主题色
.metric-item {
  &:nth-child(1) {
    .metric-icon .el-icon {
      background: rgba(22, 119, 255, 0.1);
      color: #1677ff;
    }
    
    &:hover {
      background: #f0f7ff;
      border-color: #d6e4ff;
    }
  }
  
  &:nth-child(2) {
    .metric-icon .el-icon {
      background: rgba(82, 196, 26, 0.1);
      color: #52c41a;
    }
    
    &:hover {
      background: #f6ffed;
      border-color: #d9f7be;
    }
  }
  
  &:nth-child(3) {
    .metric-icon .el-icon {
      background: rgba(250, 173, 20, 0.1);
      color: #faad14;
    }
    
    &:hover {
      background: #fffbe6;
      border-color: #ffe7ba;
    }
  }
  
  &:nth-child(4) {
    .metric-icon .el-icon {
      background: rgba(114, 46, 209, 0.1);
      color: #722ed1;
    }
    
    &:hover {
      background: #f9f0ff;
      border-color: #efdbff;
    }
  }
}
</style> 