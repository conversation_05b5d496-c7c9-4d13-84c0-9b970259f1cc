// 商机阶段类型
export type OpportunityStage =
  | 'initial_contact'
  | 'needs_analysis'
  | 'proposal'
  | 'negotiation'
  | 'closed_won'
  | 'closed_lost'

// 商机表单数据类型
export interface OpportunityForm {
  name: string
  customerId: string
  stage: OpportunityStage
  expectedAmount: number
  probability: number
  expectedClosingDate: string
  remarks?: string
}

// 商机详情数据类型
export interface OpportunityDetail extends OpportunityForm {
  id: string
  customerName: string
  stageName: string
  ownerId: string
  ownerName: string
  createTime: string
  updateTime: string
}

// 商机查询参数类型
export interface OpportunityQuery {
  name?: string
  customerId?: string
  stage?: OpportunityStage[]
  minAmount?: number
  maxAmount?: number
  minProbability?: number
  maxProbability?: number
  startDate?: string
  endDate?: string
  ownerId?: string
  pageNum?: number
  pageSize?: number
  sortField?: string
  sortOrder?: 'ascend' | 'descend'
}

// 商机统计数据类型
export interface OpportunityStats {
  totalCount: number
  totalAmount: number
  stageDistribution: {
    stage: OpportunityStage
    stageName: string
    count: number
    amount: number
  }[]
  monthlyTrend: {
    month: string
    count: number
    amount: number
  }[]
  probabilityDistribution: {
    range: string
    count: number
    amount: number
  }[]
} 