// 报价相关类型定义
export interface ModelInfo {
    name: string;
    unit: string;
    dimensions: string;
    volume: string;
    surfaceArea: string;
}

export interface TableRow {
    index: number;
    drawing: string;
    modelInfo: ModelInfo;
    printParams: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    thumbnail?: string;
    originalUrl?: string;
    modelUrl?: string;
    materialId?: string;
    materialName?: string;
    fileData?: File;
    processOptions?: string[];
}

// 可选的后处理选项
export interface PostProcessOptions {
    sprayOptions: string[];
    insertOptions: string[];
}

// 报价单状态
export interface QuoteStatus {
    quoteNumber: string;
    status: 'pending' | 'quoted' | 'accepted' | 'rejected';
    totalAmount: number;
    estimatedDelivery: string;
}