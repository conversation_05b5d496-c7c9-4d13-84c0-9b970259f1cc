// 客户实体接口
export interface CustomerEntity {
    id: number;
    name: string;
    phone: string;
    email: string;
    delFlag: string;
    createTime?: string;
    updateTime?: string;
}

// 默认客户数据
export const DEFAULT_CUSTOMER: CustomerEntity = {
    id: 0,
    name: '',
    phone: '',
    email: '',
    delFlag: '0'
};

// 客户表单接口
export interface CustomerForm {
    id?: number;
    name: string;
    phone: string;
    email?: string;
    industry?: string;
    level?: string;
    status?: string;
    address?: string;
    remarks?: string;
} 