import { ElMessage, ElMessageBox } from 'element-plus';

/**
 * MessageBox 测试工具类
 */
export class MessageBoxTester {
  
  /**
   * 检查 Element Plus 是否正确加载
   */
  static checkElementPlusLoaded(): boolean {
    console.log('=== 检查 Element Plus 加载状态 ===');
    
    const checks = {
      ElMessage: !!ElMessage,
      ElMessageBox: !!ElMessageBox,
      ElMessageAlert: !!(ElMessageBox && ElMessageBox.alert),
      ElMessageConfirm: !!(ElMessageBox && ElMessageBox.confirm),
    };
    
    console.log('Element Plus 组件检查:', checks);
    
    return Object.values(checks).every(Boolean);
  }
  
  /**
   * 检查 DOM 环境
   */
  static checkDOMEnvironment(): Record<string, any> {
    console.log('=== 检查 DOM 环境 ===');
    
    const info = {
      bodyOverflow: getComputedStyle(document.body).overflow,
      htmlOverflow: getComputedStyle(document.documentElement).overflow,
      existingOverlays: document.querySelectorAll('.el-overlay').length,
      existingMessageBoxes: document.querySelectorAll('.el-message-box').length,
      hasElementPlusStyles: !!document.querySelector('link[href*="element-plus"], style[data-vite-dev-id*="element-plus"]'),
    };
    
    console.log('DOM 环境信息:', info);
    return info;
  }
  
  /**
   * 强制修复样式问题
   */
  static forceFixStyles(): void {
    console.log('=== 强制修复样式 ===');
    
    // 创建样式标签
    const style = document.createElement('style');
    style.textContent = `
      /* 强制修复 MessageBox 样式 */
      .el-overlay {
        z-index: 9999 !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
      }
      
      .el-message-box {
        z-index: 10000 !important;
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        background: white !important;
        border-radius: 4px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        padding: 20px !important;
        min-width: 300px !important;
      }
      
      .el-message {
        z-index: 10001 !important;
        position: fixed !important;
        top: 20px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
      }
      
      body {
        overflow: visible !important;
      }
    `;
    
    document.head.appendChild(style);
    console.log('样式修复已应用');
  }
  
  /**
   * 测试原生弹窗
   */
  static testNativeAlert(): boolean {
    try {
      alert('原生弹窗测试 - 如果你看到这个，说明浏览器弹窗功能正常');
      return true;
    } catch (error) {
      console.error('原生弹窗测试失败:', error);
      return false;
    }
  }
  
  /**
   * 测试 Element Plus Message
   */
  static async testMessage(): Promise<boolean> {
    try {
      console.log('=== 测试 Message ===');
      
      if (!ElMessage) {
        throw new Error('ElMessage 未定义');
      }
      
      ElMessage.success('Message 测试成功！');
      console.log('Message 测试完成');
      return true;
    } catch (error) {
      console.error('Message 测试失败:', error);
      return false;
    }
  }
  
  /**
   * 测试 MessageBox Alert (回调方式)
   */
  static testAlertWithCallback(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        console.log('=== 测试 MessageBox Alert (回调方式) ===');
        
        if (!ElMessageBox || !ElMessageBox.alert) {
          throw new Error('ElMessageBox.alert 未定义');
        }
        
        ElMessageBox.alert('这是回调方式的测试', '提示', {
          confirmButtonText: '确定',
          type: 'info',
          callback: (action: string) => {
            console.log('MessageBox 回调:', action);
            resolve(true);
          }
        });
        
        console.log('MessageBox Alert 已调用');
      } catch (error) {
        console.error('MessageBox Alert 测试失败:', error);
        resolve(false);
      }
    });
  }
  
  /**
   * 测试 MessageBox Alert (Promise方式)
   */
  static async testAlertWithPromise(): Promise<boolean> {
    try {
      console.log('=== 测试 MessageBox Alert (Promise方式) ===');
      
      if (!ElMessageBox || !ElMessageBox.alert) {
        throw new Error('ElMessageBox.alert 未定义');
      }
      
      await ElMessageBox.alert('这是Promise方式的测试', '提示', {
        confirmButtonText: '确定',
        type: 'info'
      });
      
      console.log('MessageBox Alert Promise 完成');
      return true;
    } catch (error) {
      console.error('MessageBox Alert Promise 测试失败:', error);
      return false;
    }
  }
  
  /**
   * 测试 MessageBox Confirm
   */
  static async testConfirm(): Promise<boolean> {
    try {
      console.log('=== 测试 MessageBox Confirm ===');
      
      if (!ElMessageBox || !ElMessageBox.confirm) {
        throw new Error('ElMessageBox.confirm 未定义');
      }
      
      const result = await ElMessageBox.confirm('确认要执行这个操作吗？', '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });
      
      console.log('Confirm 结果:', result);
      return true;
    } catch (error) {
      if (error === 'cancel') {
        console.log('用户取消了操作');
        return true; // 取消也算测试成功
      } else {
        console.error('MessageBox Confirm 测试失败:', error);
        return false;
      }
    }
  }
  
  /**
   * 运行完整测试套件
   */
  static async runFullTest(): Promise<void> {
    console.log('=== 开始完整测试套件 ===');
    
    // 1. 检查加载状态
    const isLoaded = this.checkElementPlusLoaded();
    if (!isLoaded) {
      console.error('Element Plus 未正确加载，停止测试');
      return;
    }
    
    // 2. 检查DOM环境
    this.checkDOMEnvironment();
    
    // 3. 强制修复样式
    this.forceFixStyles();
    
    // 4. 测试原生弹窗
    this.testNativeAlert();
    
    // 5. 测试Message
    await this.testMessage();
    
    // 6. 测试MessageBox (回调方式)
    await this.testAlertWithCallback();
    
    console.log('=== 测试套件完成 ===');
  }
}

// 导出便捷函数
export const testMessageBox = () => MessageBoxTester.runFullTest();
export const fixMessageBoxStyles = () => MessageBoxTester.forceFixStyles(); 