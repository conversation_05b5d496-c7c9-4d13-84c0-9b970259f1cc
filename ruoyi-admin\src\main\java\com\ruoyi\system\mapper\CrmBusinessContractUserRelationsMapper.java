package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.CrmBusinessContractUserRelations;

/**
 * 合同用户关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface CrmBusinessContractUserRelationsMapper 
{
    /**
     * 查询合同用户关系
     * 
     * @param id 合同用户关系主键
     * @return 合同用户关系
     */
    public CrmBusinessContractUserRelations selectCrmBusinessContractUserRelationsById(Long id);

    /**
     * 查询合同用户关系列表
     * 
     * @param crmBusinessContractUserRelations 合同用户关系
     * @return 合同用户关系集合
     */
    public List<CrmBusinessContractUserRelations> selectCrmBusinessContractUserRelationsList(CrmBusinessContractUserRelations crmBusinessContractUserRelations);

    /**
     * 新增合同用户关系
     * 
     * @param crmBusinessContractUserRelations 合同用户关系
     * @return 结果
     */
    public int insertCrmBusinessContractUserRelations(CrmBusinessContractUserRelations crmBusinessContractUserRelations);

    /**
     * 修改合同用户关系
     * 
     * @param crmBusinessContractUserRelations 合同用户关系
     * @return 结果
     */
    public int updateCrmBusinessContractUserRelations(CrmBusinessContractUserRelations crmBusinessContractUserRelations);

    /**
     * 删除合同用户关系
     * 
     * @param id 合同用户关系主键
     * @return 结果
     */
    public int deleteCrmBusinessContractUserRelationsById(Long id);

    /**
     * 批量删除合同用户关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCrmBusinessContractUserRelationsByIds(Long[] ids);
}
