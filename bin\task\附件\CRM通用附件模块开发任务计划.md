# CRM通用附件模块开发任务计划

## 📋 项目概述

### 目标
开发一个通用的附件管理模块，支持CRM系统中的多种业务实体（联系人、客户、商机、合同等）的附件管理功能。

### 核心原则
- **通用性**：一套代码支持多种业务实体
- **组件化**：可复用的Tab页面组件
- **统一管理**：统一的API接口和存储策略
- **用户体验**：现代化的UI设计和交互

---

## 🎯 第一阶段：数据库设计与实体层开发

### 任务1.1：通用附件表设计
**预计工时：4小时**

#### 1.1.1 数据库表结构设计
```sql
CREATE TABLE `crm_attachments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '附件ID',
  `entity_type` varchar(50) NOT NULL COMMENT '实体类型(contact/customer/opportunity/contract)',
  `entity_id` bigint(20) NOT NULL COMMENT '实体ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件存储路径',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(100) DEFAULT NULL COMMENT '文件MIME类型',
  `file_extension` varchar(20) DEFAULT NULL COMMENT '文件扩展名',
  `category` varchar(50) DEFAULT NULL COMMENT '附件分类(contract/image/document/other)',
  `description` varchar(500) DEFAULT NULL COMMENT '附件描述',
  `upload_by` varchar(64) DEFAULT NULL COMMENT '上传人',
  `upload_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数',
  `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开(0私有 1公开)',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序顺序',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志(0存在 2删除)',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_entity` (`entity_type`, `entity_id`),
  KEY `idx_del_flag` (`del_flag`),
  KEY `idx_upload_time` (`upload_time`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CRM通用附件表';
```

#### 1.1.2 创建SQL脚本文件
- 位置：`ruoyi-crm/sql/crm_attachments.sql`
- 包含表结构和初始化数据

#### 1.1.3 验收标准
- ✅ 表结构支持多种实体类型
- ✅ 索引优化查询性能
- ✅ 字段设计满足业务需求

### 任务1.2：通用附件实体类开发
**预计工时：3小时**

#### 1.2.1 实体类设计
- 类名：`CrmAttachment`
- 位置：`com.ruoyi.common.domain.entity.CrmAttachment`
- 继承：`BaseEntity`

#### 1.2.2 关键字段设计
```java
public class CrmAttachment extends BaseEntity {
    private Long id;
    private String entityType;  // 实体类型枚举
    private Long entityId;      // 实体ID  
    private String fileName;    // 存储文件名
    private String originalName; // 原始文件名
    private String filePath;    // 文件路径
    private Long fileSize;      // 文件大小
    private String fileType;    // MIME类型
    private String fileExtension; // 扩展名
    private String category;    // 附件分类
    private String description; // 描述
    private String uploadBy;    // 上传人
    private Date uploadTime;    // 上传时间
    private Integer downloadCount; // 下载次数
    private Boolean isPublic;   // 是否公开
    private Integer sortOrder;  // 排序
    // ... 其他字段和方法
}
```

#### 1.2.3 枚举类设计
```java
public enum AttachmentEntityType {
    CONTACT("contact", "联系人"),
    CUSTOMER("customer", "客户"), 
    OPPORTUNITY("opportunity", "商机"),
    CONTRACT("contract", "合同");
}

public enum AttachmentCategory {
    CONTRACT("contract", "合同文件"),
    IMAGE("image", "图片"),
    DOCUMENT("document", "文档"),
    OTHER("other", "其他");
}
```

#### 1.2.4 验收标准
- ✅ 实体类字段完整
- ✅ 注解配置正确
- ✅ 枚举类型定义
- ✅ 单元测试覆盖

---

## 🗃️ 第二阶段：数据访问层开发

### 任务2.1：Mapper接口开发
**预计工时：4小时**

#### 2.1.1 Mapper接口设计
- 接口名：`CrmAttachmentMapper`
- 位置：`com.ruoyi.common.mapper.CrmAttachmentMapper`

#### 2.1.2 核心方法定义
```java
public interface CrmAttachmentMapper {
    // 基础CRUD
    int insertCrmAttachment(CrmAttachment attachment);
    CrmAttachment selectCrmAttachmentById(Long id);
    List<CrmAttachment> selectCrmAttachmentList(CrmAttachment attachment);
    int updateCrmAttachment(CrmAttachment attachment);
    int deleteCrmAttachmentById(Long id);
    int deleteCrmAttachmentByIds(Long[] ids);
    
    // 业务方法
    List<CrmAttachment> selectByEntityTypeAndId(String entityType, Long entityId);
    int countByEntityTypeAndId(String entityType, Long entityId);
    List<CrmAttachment> selectByCategory(String entityType, Long entityId, String category);
    int updateDownloadCount(Long id);
    List<CrmAttachment> selectExpiredAttachments(Date expireDate);
}
```

#### 2.1.3 MyBatis XML配置
- 文件名：`CrmAttachmentMapper.xml`
- 位置：`resources/mapper/common/CrmAttachmentMapper.xml`

#### 2.1.4 验收标准
- ✅ 所有CRUD方法实现
- ✅ 复杂查询优化
- ✅ 分页查询支持
- ✅ 单元测试覆盖

### 任务2.2：数据访问层测试
**预计工时：2小时**

#### 2.2.1 测试类开发
- 类名：`CrmAttachmentMapperTest`
- 位置：`src/test/java/com/ruoyi/common/mapper/CrmAttachmentMapperTest.java`

#### 2.2.2 测试用例
- 插入附件记录测试
- 查询附件列表测试
- 按实体类型查询测试  
- 更新下载次数测试
- 删除附件记录测试

#### 2.2.3 验收标准
- ✅ 测试覆盖率>90%
- ✅ 所有测试用例通过
- ✅ 边界条件测试

---

## 🔧 第三阶段：业务服务层开发

### 任务3.1：附件服务接口设计
**预计工时：3小时**

#### 3.1.1 服务接口定义
- 接口名：`ICrmAttachmentService`
- 位置：`com.ruoyi.common.service.ICrmAttachmentService`

#### 3.1.2 核心方法设计
```java
public interface ICrmAttachmentService {
    // 基础CRUD
    int insertCrmAttachment(CrmAttachment attachment);
    CrmAttachment selectCrmAttachmentById(Long id);
    List<CrmAttachment> selectCrmAttachmentList(CrmAttachment attachment);
    int updateCrmAttachment(CrmAttachment attachment);
    int deleteCrmAttachmentById(Long id);
    int deleteCrmAttachmentByIds(Long[] ids);
    
    // 业务方法
    List<CrmAttachment> getAttachmentsByEntity(String entityType, Long entityId);
    int getAttachmentCountByEntity(String entityType, Long entityId);
    AjaxResult uploadAttachment(MultipartFile file, String entityType, Long entityId, String category, String description);
    AjaxResult downloadAttachment(Long id, HttpServletResponse response);
    AjaxResult previewAttachment(Long id);
    boolean deleteAttachmentFile(Long id);
    
    // 批量操作
    AjaxResult batchUpload(MultipartFile[] files, String entityType, Long entityId);
    AjaxResult batchDelete(Long[] ids);
    
    // 统计分析
    Map<String, Object> getAttachmentStatistics(String entityType, Long entityId);
    List<CrmAttachment> getRecentAttachments(String entityType, Long entityId, int limit);
}
```

#### 3.1.3 验收标准
- ✅ 接口方法完整
- ✅ 返回值类型合理
- ✅ 异常处理考虑

### 任务3.2：附件服务实现
**预计工时：8小时**

#### 3.2.1 服务实现类
- 类名：`CrmAttachmentServiceImpl`
- 位置：`com.ruoyi.common.service.impl.CrmAttachmentServiceImpl`

#### 3.2.2 文件存储策略
```java
@Component
public class FileStorageStrategy {
    // 存储路径规则：/uploads/{entityType}/{entityId}/{yyyy/MM/dd}/
    public String generateFilePath(String entityType, Long entityId, String originalName);
    
    // 文件上传处理
    public String saveFile(MultipartFile file, String filePath);
    
    // 文件删除处理
    public boolean deleteFile(String filePath);
    
    // 文件预览URL生成
    public String generatePreviewUrl(String filePath, String fileType);
}
```

#### 3.2.3 核心业务逻辑
- 文件上传验证（大小、格式）
- 文件名唯一性处理
- 文件存储路径生成
- 下载次数统计
- 文件预览功能
- 批量操作事务处理

#### 3.2.4 验收标准
- ✅ 所有接口方法实现
- ✅ 文件存储功能完整
- ✅ 异常处理完善
- ✅ 事务管理正确

### 任务3.3：文件工具类开发
**预计工时：3小时**

#### 3.3.1 文件工具类
- 类名：`AttachmentFileUtils`
- 位置：`com.ruoyi.common.utils.AttachmentFileUtils`

#### 3.3.2 工具方法
```java
public class AttachmentFileUtils {
    // 文件类型检测
    public static String getFileType(MultipartFile file);
    
    // 文件大小格式化
    public static String formatFileSize(long size);
    
    // 生成唯一文件名
    public static String generateUniqueFileName(String originalName);
    
    // 获取文件扩展名
    public static String getFileExtension(String fileName);
    
    // 验证文件格式
    public static boolean isValidFileType(String fileType, String[] allowedTypes);
    
    // 生成文件缩略图
    public static String generateThumbnail(String filePath);
}
```

#### 3.3.3 验收标准
- ✅ 工具方法完整
- ✅ 单元测试覆盖
- ✅ 异常处理完善

---

## 🎮 第四阶段：控制器层开发

### 任务4.1：通用附件控制器开发
**预计工时：6小时**

#### 4.1.1 控制器类设计
- 类名：`CrmAttachmentController`
- 位置：`com.ruoyi.crm.controller.CrmAttachmentController`
- 基础路径：`/front/crm/attachments`

#### 4.1.2 API接口设计
```java
@RestController
@RequestMapping("/front/crm/attachments")
public class CrmAttachmentController extends BaseController {
    
    // 获取附件列表
    @GetMapping("/{entityType}/{entityId}")
    public AjaxResult getAttachments(@PathVariable String entityType, @PathVariable Long entityId);
    
    // 上传附件
    @PostMapping("/{entityType}/{entityId}")
    public AjaxResult uploadAttachment(@RequestParam("file") MultipartFile file,
                                     @PathVariable String entityType,
                                     @PathVariable Long entityId,
                                     @RequestParam(required = false) String category,
                                     @RequestParam(required = false) String description);
    
    // 批量上传
    @PostMapping("/{entityType}/{entityId}/batch")
    public AjaxResult batchUpload(@RequestParam("files") MultipartFile[] files,
                                @PathVariable String entityType,
                                @PathVariable Long entityId);
    
    // 下载附件
    @GetMapping("/download/{id}")
    public void downloadAttachment(@PathVariable Long id, HttpServletResponse response);
    
    // 预览附件
    @GetMapping("/preview/{id}")
    public AjaxResult previewAttachment(@PathVariable Long id);
    
    // 删除附件
    @DeleteMapping("/{id}")
    public AjaxResult deleteAttachment(@PathVariable Long id);
    
    // 批量删除
    @DeleteMapping("/batch")
    public AjaxResult batchDelete(@RequestBody Long[] ids);
    
    // 更新附件信息
    @PutMapping("/{id}")
    public AjaxResult updateAttachment(@PathVariable Long id, @RequestBody CrmAttachment attachment);
    
    // 获取附件统计
    @GetMapping("/{entityType}/{entityId}/stats")
    public AjaxResult getAttachmentStats(@PathVariable String entityType, @PathVariable Long entityId);
}
```

#### 4.1.3 验收标准
- ✅ 所有API接口实现
- ✅ 参数验证完整
- ✅ 权限控制正确
- ✅ 异常处理完善

### 任务4.2：接口文档和测试
**预计工时：4小时**

#### 4.2.1 API文档
- 使用Swagger生成接口文档
- 包含请求参数说明
- 包含响应格式说明
- 包含错误码定义

#### 4.2.2 接口测试
- 创建Postman测试集合
- 覆盖所有API接口
- 包含正常和异常场景

#### 4.2.3 验收标准
- ✅ 接口文档完整
- ✅ 所有接口测试通过
- ✅ 错误处理验证

---

## 🎨 第五阶段：前端组件开发

### 任务5.1：通用附件Tab组件开发
**预计工时：10小时**

#### 5.1.1 组件设计
- 组件名：`AttachmentTab.vue`
- 位置：`frontend/src/components/Attachment/AttachmentTab.vue`

#### 5.1.2 组件Props设计
```typescript
interface Props {
  entityType: string;        // 实体类型
  entityId: number;          // 实体ID
  readonly?: boolean;        // 是否只读
  maxFileSize?: number;      // 最大文件大小(MB)
  allowedTypes?: string[];   // 允许的文件类型
  maxFileCount?: number;     // 最大文件数量
  showCategory?: boolean;    // 是否显示分类
  showDescription?: boolean; // 是否显示描述
  layout?: 'card' | 'list' | 'compact'; // 布局样式
}
```

#### 5.1.3 组件功能
- 📤 拖拽上传支持
- 📋 附件列表展示
- 🔍 文件预览功能
- 📊 附件统计显示
- 🗂️ 分类筛选功能
- 📱 响应式设计

#### 5.1.4 样式设计
- 采用卡片网格布局（推荐样式）
- 支持多种布局切换
- 文件类型图标显示
- 悬浮效果和动画

#### 5.1.5 验收标准
- ✅ 组件功能完整
- ✅ 样式美观响应式
- ✅ 交互体验良好
- ✅ 错误处理完善

### 任务5.2：附件API适配层
**预计工时：3小时**

#### 5.2.1 API接口封装
- 文件：`frontend/src/api/attachment.ts`

```typescript
export interface AttachmentAPI {
  // 获取附件列表
  getAttachments(entityType: string, entityId: number): Promise<ApiResponse<Attachment[]>>;
  
  // 上传附件
  uploadAttachment(file: File, entityType: string, entityId: number, options?: UploadOptions): Promise<ApiResponse<Attachment>>;
  
  // 批量上传
  batchUpload(files: File[], entityType: string, entityId: number): Promise<ApiResponse<Attachment[]>>;
  
  // 下载附件
  downloadAttachment(id: number): Promise<void>;
  
  // 删除附件
  deleteAttachment(id: number): Promise<ApiResponse<void>>;
  
  // 批量删除
  batchDelete(ids: number[]): Promise<ApiResponse<void>>;
  
  // 获取统计信息
  getAttachmentStats(entityType: string, entityId: number): Promise<ApiResponse<AttachmentStats>>;
}
```

#### 5.2.2 TypeScript类型定义
```typescript
export interface Attachment {
  id: number;
  entityType: string;
  entityId: number;
  fileName: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  fileType: string;
  fileExtension: string;
  category: string;
  description?: string;
  uploadBy: string;
  uploadTime: string;
  downloadCount: number;
  isPublic: boolean;
}

export interface AttachmentStats {
  totalCount: number;
  totalSize: number;
  categoryStats: Array<{
    category: string;
    count: number;
    size: number;
  }>;
}
```

#### 5.2.3 验收标准
- ✅ API接口封装完整
- ✅ 类型定义准确
- ✅ 错误处理完善

### 任务5.3：组件集成和测试
**预计工时：4小时**

#### 5.3.1 集成到现有模块
- 联系人详情页面集成
- 客户详情页面集成
- 商机详情页面集成
- 合同详情页面集成

#### 5.3.2 更新配置文件
```typescript
// ContactManagement/config/index.ts
{
  key: 'attachments',
  label: '附件',
  icon: 'Paperclip',
  component: markRaw(AttachmentTab),
  props: {
    entityType: 'contact',
    entityId: 'id'
  },
  badge: true
}
```

#### 5.3.3 验收标准
- ✅ 所有模块集成成功
- ✅ 配置文件更新正确
- ✅ 功能测试通过

---

## 🧪 第六阶段：测试和优化

### 任务6.1：单元测试开发
**预计工时：6小时**

#### 6.1.1 后端测试
- Service层单元测试
- Controller层单元测试  
- Mapper层单元测试
- 工具类单元测试

#### 6.1.2 前端测试
- 组件单元测试
- API接口测试
- 用户交互测试

#### 6.1.3 验收标准
- ✅ 测试覆盖率>85%
- ✅ 所有测试用例通过
- ✅ 边界条件测试

### 任务6.2：集成测试
**预计工时：4小时**

#### 6.2.1 端到端测试
- 上传功能测试
- 下载功能测试
- 删除功能测试
- 权限控制测试

#### 6.2.2 性能测试
- 大文件上传测试
- 批量操作测试
- 并发访问测试

#### 6.2.3 验收标准
- ✅ 功能测试全部通过
- ✅ 性能指标达标
- ✅ 兼容性测试通过

### 任务6.3：代码优化和重构
**预计工时：3小时**

#### 6.3.1 代码质量优化
- 代码规范检查
- 性能优化
- 安全性加固

#### 6.3.2 文档完善
- 代码注释完善
- 使用文档编写
- 部署文档编写

#### 6.3.3 验收标准
- ✅ 代码质量达标
- ✅ 文档完整
- ✅ 安全漏洞修复

---

## 📊 项目管理

### 总工时预估
- **第一阶段**：7小时
- **第二阶段**：6小时  
- **第三阶段**：14小时
- **第四阶段**：10小时
- **第五阶段**：17小时
- **第六阶段**：13小时
- **总计**：67小时

### 里程碑节点
1. **Week 1**：完成数据库设计和实体层开发
2. **Week 2**：完成数据访问层和服务层开发
3. **Week 3**：完成控制器层和API接口开发
4. **Week 4**：完成前端组件开发和集成
5. **Week 5**：完成测试和优化工作

### 风险评估
- **技术风险**：文件存储策略选择
- **进度风险**：前端组件开发复杂度
- **质量风险**：大文件上传性能优化

### 交付物清单
- ✅ 数据库表结构和SQL脚本
- ✅ 后端实体类、Mapper和Service
- ✅ REST API接口和文档
- ✅ 前端通用附件组件
- ✅ 单元测试和集成测试
- ✅ 技术文档和使用手册

---

## 🚀 后续扩展计划

### 功能扩展
- 附件版本管理
- 附件权限控制
- 附件审批流程
- 附件自动分类
- 云存储支持

### 技术优化
- CDN加速支持
- 图片压缩处理
- 视频预览功能
- 全文检索支持
- 缓存策略优化

---

**文档版本**：v1.0  
**创建时间**：2025-07-01  
**更新时间**：2025-07-01  
**负责人**：开发团队
