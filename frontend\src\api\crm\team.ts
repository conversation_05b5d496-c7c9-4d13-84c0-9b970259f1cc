import request from '@/utils/request';
import { CrmTeam, CrmTeamMember } from '@/types/crm-team';

interface ListResponse<T> {
  rows: T[];
  total: number;
}

// 查询团队列表
export function listTeam(query?: any): Promise<ListResponse<CrmTeam>> {
  return request({
    url: '/crm/team/list',
    method: 'get',
    params: query
  });
}

// 查询团队详细信息
export function getTeam(teamId: number) {
  return request({
    url: `/crm/team/${teamId}`,
    method: 'get'
  });
}

// 新增团队
export function addTeam(data: CrmTeam) {
  return request({
    url: '/crm/team',
    method: 'post',
    data
  });
}

// 修改团队
export function updateTeam(data: CrmTeam) {
  return request({
    url: '/crm/team',
    method: 'put',
    data
  });
}

// 删除团队
export function deleteTeam(teamId: number) {
  return request({
    url: `/crm/team/${teamId}`,
    method: 'delete'
  });
}

// =================== 团队成员管理 API (已迁移到 team-relation.ts) ===================
// 注意：以下API已迁移到 @/api/team-relation，请使用新的API
// 这些函数保留是为了向后兼容，建议使用 team-relation.ts 中的对应函数

// 获取团队成员列表 - 请使用 getTeamMembersByTeamId from '@/api/team-relation'
export function getTeamMembers(teamId: number) {
  return request({
    url: `/crm/team-member/team/${teamId}`,
    method: 'get'
  });
}

// 添加团队成员 - 请使用 addTeamMember from '@/api/team-relation'
export function addTeamMember(data: CrmTeamMember) {
  return request({
    url: '/crm/team-member',
    method: 'post',
    data
  });
}

// 批量添加团队成员 - 请使用 batchAddTeamMembers from '@/api/team-relation'
export function batchAddTeamMembers(teamId: number, userIds: number[], roleType: string) {
  return request({
    url: '/crm/team-member/batch-add',
    method: 'post',
    data: { teamId, userIds, roleType }
  });
}

// 移除团队成员 - 请使用 removeTeamMember from '@/api/team-relation'
export function removeTeamMember(teamId: number, userId: number) {
  return request({
    url: '/crm/team-member/remove',
    method: 'delete',
    params: { teamId, userId }
  });
}

// 获取可选用户列表
export function getAvailableUsers(deptId?: number) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: { deptId, status: '0' }
  });
}

// =================== 团队关联对象 API (已迁移到 team-relation.ts) ===================
// 注意：以下API已迁移到 @/api/team-relation，请使用新的API
// 这些函数保留是为了向后兼容，建议使用 team-relation.ts 中的对应函数

// 查询团队关联的对象列表 - 请使用 getBizsByTeam from '@/api/team-relation'
export function getTeamRelations(teamId: number): Promise<ListResponse<any>> {
  return request({
    url: '/crm/relation/bizs',
    method: 'get',
    params: { teamId }
  });
}

// 批量添加团队关联 - 请使用相关API from '@/api/team-relation'
export function addTeamRelations(relations: any[]) {
  return request({
    url: '/crm/relation',
    method: 'post',
    data: relations
  });
}

// 批量删除团队关联 - 请使用相关API from '@/api/team-relation'
export function removeTeamRelations(relations: any[]) {
  return request({
    url: '/crm/relation',
    method: 'delete',
    data: relations
  });
}
