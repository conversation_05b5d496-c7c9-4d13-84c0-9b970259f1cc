# CRM系统无密码用户注册登录完整设计方案

## 🎯 设计理念：完全无入侵的无密码认证系统

### 核心原则
- **不修改现有表结构**：sys_user表保持完全不变
- **密码保留机制**：后台管理员仍可使用密码登录，系统自动为前端用户生成密码（用户无感知）
- **用户无感知**：前端用户体验完全无密码，但系统内部自动管理密码
- **渐进式部署**：可以与现有系统并行运行，完全可逆

### 💡 核心设计理念：无密码认证系统

本系统采用现代化的无密码认证机制，用户无需记住任何密码，只需通过安全便捷的认证方式即可访问系统：

- 🔐 **企业微信OAuth认证** - 内部员工通过企业微信身份验证
- 📱 **手机短信验证** - 客户通过手机验证码验证身份  
- 🚫 **用户无感知密码** - 系统内部自动生成密码，用户完全无感知
- 🛡️ **双重认证保障** - 管理员密码登录 + 用户无密码登录并存

## 1. 系统概述

CRM系统采用无入侵式无密码认证架构，支持多种用户注册和登录方式，包括内部工作人员（销售、财务等）和外部客户用户。系统设计基于现代用户体验理念：**没有人愿意记住密码，但后台系统仍需密码安全保障**。

### 设计目标

- **内部员工：** 通过企业微信扫码登录/注册，绑定手机号，完全无密码体验
- **外部客户：** 通过手机短信验证码"润物无声"式注册，无需任何密码设置
- **管理员保障：** 后台管理员仍然使用密码登录，确保系统安全
- **统一管理：** 所有前端用户以手机号作为最终唯一标识
- **完全无入侵：** 不修改现有表结构，通过新表扩展功能

## 2. 现有表结构分析

| 表名 | 用途 | 关键字段 | 密码相关字段 |
|------|------|----------|-------------|
| sys_user | 系统用户基础信息 | user_id, user_name, phonenumber, email | password（内部员工保留，客户为空） |
| crm_thirdparty_wechat | 企业微信第三方登录关联 | user_id, wecom_user_id | 无 |
| crm_wecom_config | 企业微信配置 | corp_id, corp_secret, agent_id | 无 |

## 3. 新增表设计方案

### 3.1 用户注册表 (crm_user_registration)

```sql
CREATE TABLE `crm_user_registration` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '注册记录ID',
  `phone_number` varchar(20) NOT NULL COMMENT '手机号码',
  `registration_type` varchar(20) NOT NULL COMMENT '注册类型：wechat-企业微信,sms-短信验证',
  `wechat_union_id` varchar(100) DEFAULT NULL COMMENT '企业微信唯一ID',
  `verification_code` varchar(10) DEFAULT NULL COMMENT '验证码（加密存储）',
  `verification_expire_time` datetime DEFAULT NULL COMMENT '验证码过期时间',
  `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否已验证',
  `user_id` bigint(20) DEFAULT NULL COMMENT '关联的用户ID',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态：pending-待处理,verified-已验证,completed-已完成',
  `user_type` varchar(20) DEFAULT 'customer' COMMENT '用户类型：employee-员工,customer-客户',
  `source_data` json DEFAULT NULL COMMENT '来源数据(如企业微信用户信息)',
  `passwordless_token` varchar(255) DEFAULT NULL COMMENT '无密码登录令牌',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone_type` (`phone_number`, `registration_type`),
  KEY `idx_wechat_union_id` (`wechat_union_id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='用户注册记录表（无密码设计）';
```

### 3.2 用户认证方式表 (crm_user_auth_methods)

```sql
CREATE TABLE `crm_user_auth_methods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '认证方式ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `auth_type` varchar(20) NOT NULL COMMENT '认证类型：wechat-企业微信,phone-手机号',
  `auth_identifier` varchar(100) NOT NULL COMMENT '认证标识(企业微信ID或手机号)',
  `is_primary` tinyint(1) DEFAULT 0 COMMENT '是否主要认证方式',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否有效',
  `bind_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_used_time` datetime DEFAULT NULL COMMENT '最后使用时间',
  `auth_token` varchar(500) DEFAULT NULL COMMENT '认证令牌（无密码登录）',
  `token_expire_time` datetime DEFAULT NULL COMMENT '令牌过期时间',
  `metadata` json DEFAULT NULL COMMENT '附加元数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_auth_type` (`user_id`, `auth_type`),
  UNIQUE KEY `uk_auth_identifier` (`auth_type`, `auth_identifier`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_auth_type` (`auth_type`)
) ENGINE=InnoDB COMMENT='用户认证方式表（支持无密码认证）';
```

### 3.3 客户线索表增强 (crm_customer_leads)

```sql
CREATE TABLE `crm_customer_leads` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '线索ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '关联用户ID(如果已注册)',
  `phone_number` varchar(20) NOT NULL COMMENT '手机号码',
  `source_type` varchar(50) NOT NULL COMMENT '来源类型：3d_upload-3D文件上传,website-官网,other-其他',
  `lead_data` json DEFAULT NULL COMMENT '线索数据(如上传的3D文件信息)',
  `status` varchar(20) DEFAULT 'new' COMMENT '状态：new-新建,contacted-已联系,converted-已转化,closed-已关闭',
  `assigned_to` bigint(20) DEFAULT NULL COMMENT '分配给的销售人员ID',
  `evaluation_result` text DEFAULT NULL COMMENT '评估结果',
  `contact_notes` text DEFAULT NULL COMMENT '联系记录',
  `registration_method` varchar(20) DEFAULT 'sms' COMMENT '注册方式：sms-短信无密码注册',
  `access_token` varchar(255) DEFAULT NULL COMMENT '客户专用访问令牌',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_assigned_to` (`assigned_to`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='客户线索表（无密码注册）';
```

## 4. 完整业务流程设计

### 4.1 企业微信员工无密码注册登录流程

```mermaid
flowchart TD
    A[员工打开CRM系统] --> B[点击企业微信登录]
    B --> C[跳转到企业微信授权页面]
    C --> D[用户确认授权]
    D --> E[获取企业微信用户信息]
    E --> F{是否已注册?}
    
    F -->|是| G[检查手机号绑定状态]
    G --> H{手机号已绑定?}
    H -->|是| I[直接无密码登录成功]
    H -->|否| J[提示绑定手机号]
    
    F -->|否| K[创建注册记录]
    K --> J
    
    J --> L[用户输入手机号]
    L --> M[发送短信验证码]
    M --> N[用户输入验证码]
    N --> O{验证码正确?}
    
    O -->|否| P[提示错误，重新输入]
    P --> N
    
    O -->|是| Q[创建用户账户（无密码）]
    Q --> R[绑定企业微信和手机号]
    R --> S[生成无密码登录令牌]
    S --> T[注册完成，自动登录]
    
    I --> U[进入系统首页]
    T --> U
    
    classDef passwordless fill:#e8f4fd,stroke:#3498db
    class Q,S,T passwordless
```

### 4.2 客户用户"润物无声"无密码注册完整流程

```mermaid
flowchart TD
    A[客户访问前端上传页面] --> B[查看页面介绍和演示]
    B --> C[选择3D文件上传]
    C --> D[填写基本信息]
    D --> E[输入手机号码]
    E --> F[点击获取验证码]
    F --> G[系统发送短信验证码]
    G --> H[系统自动创建注册记录]
    H --> I[用户输入验证码]
    I --> J{验证码正确?}
    
    J -->|否| K[提示错误]
    K --> I
    
    J -->|是| L[系统自动创建用户账户（完全无密码）]
    L --> M[生成无密码访问令牌]
    M --> N[创建客户线索记录]
    N --> O[文件上传成功]
    O --> P[显示上传成功页面]
    P --> Q[提供客户专用访问链接]
    Q --> R[发送确认短信（含访问链接）]
    
    R --> S[后台销售人员收到线索通知]
    S --> T[系统自动分配给销售人员]
    T --> U[销售人员查看线索详情]
    U --> V[销售人员进行评估]
    V --> W[通过注册手机号联系客户]
    W --> X[客户通过专用链接查看进度]
    
    classDef passwordless fill:#e8f4fd,stroke:#3498db
    classDef customer fill:#e8f5e8,stroke:#28a745
    class L,M,Q,R passwordless
    class X customer
```

### 4.3 客户后续访问和跟进流程

```mermaid
flowchart TD
    A[客户收到短信通知] --> B[点击专用访问链接]
    B --> C[系统验证访问令牌]
    C --> D{令牌有效?}
    
    D -->|否| E[提示重新验证手机号]
    E --> F[发送新的验证码]
    F --> G[客户输入验证码]
    G --> H[重新生成访问令牌]
    H --> I[进入客户专区]
    
    D -->|是| I[进入客户专区]
    I --> J[查看项目进度]
    J --> K[查看评估结果]
    K --> L[查看销售人员联系方式]
    L --> M[在线留言或反馈]
    M --> N[系统通知销售人员]
    
    N --> O[销售人员回复客户]
    O --> P[客户收到回复通知]
    P --> Q[客户再次访问专区]
    Q --> R[查看最新回复]
    
    classDef customer fill:#e8f5e8,stroke:#28a745
    classDef sales fill:#fff3cd,stroke:#ffeaa7
    class I,J,K,L,M,Q,R customer
    class N,O sales
```

### 4.4 销售人员处理客户线索完整流程

```mermaid
flowchart TD
    A[系统接收到客户线索] --> B[线索智能分配算法]
    B --> C[根据地区/产品类型分配]
    C --> D[销售人员收到通知]
    D --> E[销售人员企业微信登录系统]
    E --> F[查看待处理线索列表]
    F --> G[点击查看线索详情]
    G --> H[查看客户基本信息]
    H --> I[下载客户上传的3D文件]
    I --> J[进行技术可行性评估]
    J --> K[计算成本和报价]
    K --> L[填写评估报告]
    L --> M{是否有合作机会?}
    
    M -->|否| N[标记线索为已关闭]
    N --> O[填写关闭原因]
    O --> P[系统记录并归档]
    
    M -->|是| Q[通过手机号联系客户]
    Q --> R[电话沟通详细需求]
    R --> S[记录沟通内容]
    S --> T[为客户提供专用访问链接]
    T --> U[在客户专区上传详细方案]
    U --> V[安排后续跟进计划]
    V --> W[设置提醒任务]
    W --> X[线索转化为商机]
    
    P --> Y[线索处理完成]
    X --> Z[进入商机管理流程]
    
    classDef sales fill:#fff3cd,stroke:#ffeaa7
    classDef system fill:#f8f9fa,stroke:#e9ecef
    class E,F,G,H,I,J,K,L,Q,R,S,T,U,V,W sales
    class A,B,C,D,P,X,Z system
```

## 5. 完整时序图设计

### 5.1 企业微信无密码登录详细时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端页面
    participant B as 后端服务
    participant W as 企业微信API
    participant D as 数据库
    participant SMS as 短信服务
    
    Note over U,SMS: 企业微信无密码认证完整流程
    
    U->>F: 点击企业微信登录
    F->>W: 跳转到企业微信授权页面
    W->>U: 显示授权确认页面
    U->>W: 确认授权（无需密码）
    W->>F: 返回授权码
    F->>B: 发送授权码
    B->>W: 使用授权码获取用户信息
    W->>B: 返回用户信息(unionid, name等)
    B->>D: 查询crm_thirdparty_wechat表
    
    alt 用户已存在且手机号已绑定
        D->>B: 返回完整用户信息
        B->>D: 更新最后登录时间
        B->>B: 生成无密码JWT token
        B->>F: 登录成功，返回JWT token
        F->>U: 跳转到系统首页
    else 用户已存在但手机号未绑定
        D->>B: 返回用户信息（手机号为空）
        B->>F: 返回需要绑定手机号提示
        F->>U: 显示手机号绑定页面
        Note over U,SMS: 手机号绑定流程
        U->>F: 输入手机号（无需设置密码）
        F->>B: 提交手机号
        B->>SMS: 发送短信验证码
        B->>D: 更新注册记录
        B->>F: 返回验证码已发送
        F->>U: 显示验证码输入框
        U->>F: 输入验证码
        F->>B: 提交验证码
        B->>B: 验证码校验
        B->>D: 更新用户手机号
        B->>D: 创建crm_user_auth_methods记录
        B->>B: 生成无密码JWT token
        B->>F: 绑定成功，返回JWT token
        F->>U: 跳转到系统首页
    else 用户不存在
        B->>D: 创建注册记录到crm_user_registration
        B->>F: 返回需要完成注册提示
        F->>U: 显示注册页面
        Note over U,SMS: 新用户注册流程
        U->>F: 输入手机号和基本信息
        F->>B: 提交注册信息
        B->>SMS: 发送短信验证码
        B->>D: 更新注册记录状态
        B->>F: 返回验证码已发送
        F->>U: 显示验证码输入框
        U->>F: 输入验证码
        F->>B: 提交验证码
        B->>B: 验证码校验
        B->>D: 创建sys_user记录（password字段为空）
        B->>D: 创建crm_thirdparty_wechat关联
        B->>D: 创建crm_user_auth_methods记录
        B->>D: 更新注册记录状态为completed
        B->>B: 生成无密码JWT token
        B->>F: 注册成功，返回JWT token
        F->>U: 跳转到系统首页
    end
```

### 5.2 客户无密码注册详细时序图

```mermaid
sequenceDiagram
    participant C as 客户
    participant F as 前端页面
    participant B as 后端服务
    participant S as 短信服务
    participant D as 数据库
    participant File as 文件存储
    participant Sales as 销售系统
    participant Email as 邮件服务
    
    Note over C,Email: 客户完全无密码注册流程
    
    C->>F: 访问文件上传页面
    F->>C: 显示上传表单和说明
    C->>F: 阅读服务说明
    C->>F: 选择3D文件
    F->>C: 显示文件预览
    C->>F: 填写项目基本信息
    C->>F: 输入手机号（无需设置密码）
    F->>B: 提交手机号请求验证码
    
    B->>D: 检查手机号是否已注册
    alt 手机号已存在
        D->>B: 返回已存在用户信息
        B->>S: 发送登录验证码
        B->>F: 返回"验证码已发送到已注册手机"
    else 手机号不存在
        B->>S: 发送注册验证码
        B->>D: 创建注册记录(pending状态)
        B->>F: 返回"验证码已发送"
    end
    
    F->>C: 显示验证码输入框
    C->>F: 输入验证码
    F->>B: 提交验证码和文件
    B->>B: 验证码校验
    
    alt 验证码正确
        alt 新用户注册
            B->>D: 创建sys_user记录（password字段为NULL）
            B->>D: 创建crm_user_auth_methods记录（无密码认证）
            B->>D: 更新注册记录状态为completed
        else 已有用户登录
            B->>D: 获取用户信息
        end
        
        B->>File: 上传3D文件到存储系统
        File->>B: 返回文件访问URL
        B->>D: 创建crm_customer_leads记录
        B->>B: 生成客户专用无密码访问令牌
        B->>D: 保存访问令牌到leads表
        B->>F: 上传成功，返回专用访问链接
        F->>C: 显示成功页面和专用访问链接
        
        Note over B,Email: 后台处理流程
        B->>Sales: 发送新线索通知
        B->>S: 发送确认短信给客户（含访问链接）
        B->>Email: 发送详细信息邮件给客户（可选）
        
        Sales->>Sales: 智能分配给合适的销售人员
        Sales->>D: 更新线索分配信息
        Sales->>B: 发送分配通知给销售人员
        
    else 验证码错误
        B->>F: 返回验证码错误
        F->>C: 提示重新输入
    end
```

### 5.3 客户访问专区时序图

```mermaid
sequenceDiagram
    participant C as 客户
    participant F as 前端页面
    participant B as 后端服务
    participant D as 数据库
    participant S as 短信服务
    
    Note over C,S: 客户专区无密码访问流程
    
    C->>F: 点击短信中的专用链接
    F->>B: 发送访问令牌
    B->>D: 验证访问令牌
    
    alt 令牌有效
        D->>B: 返回客户和线索信息
        B->>F: 返回客户专区数据
        F->>C: 显示客户专区首页
        
        C->>F: 查看项目进度
        F->>B: 请求项目详情
        B->>D: 查询线索处理状态
        D->>B: 返回最新进度信息
        B->>F: 返回进度数据
        F->>C: 显示项目进度页面
        
        C->>F: 查看评估结果
        F->>B: 请求评估报告
        B->>D: 查询评估结果
        D->>B: 返回评估数据
        B->>F: 返回评估结果
        F->>C: 显示评估报告页面
        
        C->>F: 在线留言
        F->>B: 提交留言内容
        B->>D: 保存客户留言
        B->>B: 通知分配的销售人员
        B->>F: 返回留言成功
        F->>C: 显示留言提交成功
        
    else 令牌过期或无效
        B->>F: 返回需要重新验证
        F->>C: 显示重新验证页面
        C->>F: 输入手机号
        F->>B: 请求新的验证码
        B->>S: 发送验证码
        B->>F: 返回验证码已发送
        F->>C: 显示验证码输入框
        C->>F: 输入验证码
        F->>B: 提交验证码
        B->>B: 验证码校验成功
        B->>B: 生成新的访问令牌
        B->>D: 更新令牌信息
        B->>F: 返回新的访问令牌
        F->>C: 自动跳转到客户专区
    end
```

### 5.4 销售人员处理线索详细时序图

```mermaid
sequenceDiagram
    participant S as 销售人员
    participant CRM as CRM系统
    participant D as 数据库
    participant File as 文件存储
    participant Phone as 电话系统
    participant C as 客户
    participant SMS as 短信服务
    
    Note over S,SMS: 销售人员处理客户线索完整流程
    
    CRM->>S: 推送新线索通知
    S->>CRM: 企业微信扫码登录
    CRM->>D: 验证销售人员身份
    D->>CRM: 返回销售人员信息
    CRM->>S: 显示系统首页
    
    S->>CRM: 点击线索管理
    CRM->>D: 查询分配给该销售的线索
    D->>CRM: 返回线索列表
    CRM->>S: 显示线索列表页面
    
    S->>CRM: 点击具体线索
    CRM->>D: 查询线索详细信息
    D->>CRM: 返回客户信息、文件信息等
    CRM->>S: 显示线索详情页面
    
    S->>CRM: 下载客户3D文件
    CRM->>File: 请求文件下载
    File->>CRM: 返回文件流
    CRM->>S: 下载文件到本地
    
    S->>S: 线下分析3D文件
    S->>S: 评估技术可行性
    S->>S: 计算成本和报价
    
    S->>CRM: 填写评估报告
    CRM->>D: 保存评估结果
    D->>CRM: 确认保存成功
    CRM->>S: 显示保存成功提示
    
    alt 有合作机会
        S->>Phone: 拨打客户电话
        Phone->>C: 电话接通
        S->>C: 介绍自己和公司
        S->>C: 说明项目评估结果
        C->>S: 询问详细方案和报价
        S->>C: 详细沟通技术方案
        C->>S: 表达合作意向
        
        S->>CRM: 记录沟通内容
        CRM->>D: 保存沟通记录
        
        S->>CRM: 上传详细方案到客户专区
        CRM->>File: 上传方案文件
        File->>CRM: 返回文件URL
        CRM->>D: 更新客户专区内容
        
        S->>CRM: 发送方案通知给客户
        CRM->>SMS: 发送短信通知客户查看
        SMS->>C: 发送短信（含专区链接）
        
        S->>CRM: 设置跟进提醒
        CRM->>D: 创建跟进任务
        
        S->>CRM: 将线索转化为商机
        CRM->>D: 创建商机记录
        CRM->>D: 更新线索状态为已转化
        
    else 无合作机会
        S->>CRM: 标记线索为已关闭
        S->>CRM: 填写关闭原因
        CRM->>D: 更新线索状态为已关闭
        CRM->>D: 保存关闭原因
        
        opt 礼貌告知客户
            S->>Phone: 拨打客户电话
            S->>C: 礼貌说明无法合作的原因
            S->>C: 感谢客户的信任
            S->>CRM: 记录告知情况
        end
    end
```

## 6. 无入侵式表协作关系详解

### 6.1 完全无入侵的设计原则

```
现有系统 (完全不变)          新增无密码系统 (扩展)
┌─────────────────┐         ┌─────────────────────┐
│   原有功能      │         │   无密码认证        │
│   ✅ 管理员登录  │         │   📱 手机验证码     │
│   ✅ 员工权限    │    +    │   🔐 企业微信OAuth  │
│   ✅ 客户数据    │         │   🚫 用户无感知密码 │
│   ✅ 业务流程    │         │   🔗 专属访问链接   │
└─────────────────┘         └─────────────────────┘
```

### 6.2 表协作关系图

```mermaid
erDiagram
    sys_user {
        bigint user_id PK "原有主键,不变"
        varchar user_name "原有字段,不变"
        varchar password "管理员保留,客户自动生成"
        varchar phonenumber "原有字段,不变"
        varchar email "原有字段,不变"
        datetime create_time "原有字段,不变"
    }
    
    crm_user_registration {
        bigint id PK "新增主键"
        varchar phone_number "手机号"
        varchar registration_type "注册类型"
        varchar verification_code "验证码"
        bigint user_id FK "无入侵外键关联"
        varchar status "注册状态"
        varchar user_type "用户类型"
        varchar passwordless_token "无密码令牌"
    }
    
    crm_user_auth_methods {
        bigint id PK "新增主键"
        bigint user_id FK "无入侵外键关联"
        varchar auth_type "认证类型"
        varchar auth_identifier "认证标识"
        tinyint is_primary "是否主要"
        datetime last_used_time "最后使用"
        varchar auth_token "认证令牌"
    }
    
    crm_customer_leads {
        bigint id PK "新增主键"
        bigint user_id FK "无入侵外键关联"
        varchar phone_number "客户手机"
        varchar source_type "来源类型"
        varchar status "线索状态"
        bigint assigned_to FK "分配销售"
        varchar access_token "专用令牌"
    }
    
    crm_lead_files {
        bigint id PK "新增主键"
        bigint lead_id FK "关联线索"
        varchar file_name "文件名"
        varchar file_url "文件URL"
        varchar file_type "文件类型"
        bigint file_size "文件大小"
    }
    
    crm_lead_communications {
        bigint id PK "新增主键"
        bigint lead_id FK "关联线索"
        bigint sales_user_id FK "销售人员"
        varchar communication_type "沟通类型"
        text content "沟通内容"
        datetime communication_time "沟通时间"
    }
    
    sys_user ||--o{ crm_user_registration : "无入侵外键关联"
    sys_user ||--o{ crm_user_auth_methods : "无入侵外键关联"
    sys_user ||--o{ crm_customer_leads : "无入侵外键关联"
    crm_customer_leads ||--o{ crm_lead_files : "线索文件关联"
    crm_customer_leads ||--o{ crm_lead_communications : "沟通记录关联"
    sys_user ||--o{ crm_lead_communications : "销售人员关联"
```

### 6.3 客户无密码注册表协作流程

```mermaid
sequenceDiagram
    participant C as 客户
    participant F as 前端
    participant B as 后端
    participant T1 as crm_user_registration
    participant T2 as sys_user
    participant T3 as crm_user_auth_methods
    participant T4 as crm_customer_leads
    participant SMS as 短信服务
    
    Note over C,SMS: 表协作：客户无密码注册
    
    C->>F: 输入手机号
    F->>B: 请求验证码
    B->>SMS: 发送短信
    B->>T1: 创建注册记录
    Note right of T1: status='pending'<br/>phone_number='138xxxx'<br/>registration_type='sms'
    
    C->>F: 输入验证码
    F->>B: 提交验证码
    B->>T1: 验证码校验
    
    alt 验证成功
        B->>T2: 创建sys_user(无入侵)
        Note right of T2: user_name='user_138xxxx'<br/>password=自动生成(用户不知道)<br/>phonenumber='138xxxx'
        
        T2->>B: 返回user_id
        B->>T1: 更新注册记录
        Note right of T1: user_id=新用户ID<br/>status='completed'<br/>is_verified=true
        
        B->>T3: 创建认证方式
        Note right of T3: user_id=用户ID<br/>auth_type='phone'<br/>auth_identifier='138xxxx'<br/>is_primary=true
        
        B->>T4: 创建客户线索
        Note right of T4: user_id=用户ID<br/>phone_number='138xxxx'<br/>source_type='website'<br/>access_token=专用令牌
        
        B->>F: 返回成功和专区链接
    end
```

### 6.4 企业微信员工登录表协作流程

```mermaid
sequenceDiagram
    participant E as 员工
    participant F as 前端
    participant B as 后端
    participant W as 企业微信API
    participant T1 as crm_thirdparty_wechat
    participant T2 as sys_user
    participant T3 as crm_user_auth_methods
    participant T4 as crm_user_registration
    
    Note over E,T4: 表协作：企业微信无密码登录
    
    E->>F: 扫码登录
    F->>W: 获取授权
    W->>B: 返回用户信息
    B->>T1: 查询企业微信绑定
    
    alt 已有用户
        T1->>B: 返回user_id
        B->>T2: 查询用户详情
        T2->>B: 返回用户信息
        B->>T3: 更新最后使用时间
        Note right of T3: last_used_time=NOW()
        B->>F: 登录成功
    else 新用户
        B->>T4: 创建注册记录
        Note right of T4: registration_type='wechat'<br/>wechat_union_id='wx123'<br/>status='pending'
        
        B->>T2: 创建sys_user(无入侵)
        Note right of T2: password=自动生成<br/>user_type='employee'
        
        B->>T1: 创建微信绑定
        B->>T3: 创建认证方式
        Note right of T3: auth_type='wechat'<br/>auth_identifier='wx123'
        
        B->>T4: 完成注册
        Note right of T4: status='completed'<br/>user_id=新用户ID
    end
```

### 6.5 客户专区访问表协作流程

```mermaid
sequenceDiagram
    participant C as 客户
    participant F as 前端
    participant B as 后端
    participant T1 as crm_customer_leads
    participant T2 as crm_lead_files
    participant T3 as crm_lead_communications
    participant T4 as sys_user
    
    Note over C,T4: 表协作：客户专区访问
    
    C->>F: 点击专属链接(含token)
    F->>B: 发送访问令牌
    B->>T1: 验证访问令牌
    
    alt 令牌有效
        T1->>B: 返回线索信息
        Note right of T1: user_id, status, assigned_to<br/>evaluation_result, contact_notes
        
        B->>T2: 查询相关文件
        T2->>B: 返回文件列表
        Note right of T2: file_name, file_url<br/>file_type, file_size
        
        B->>T3: 查询沟通记录
        T3->>B: 返回沟通历史
        Note right of T3: communication_type, content<br/>communication_time, sales_user_id
        
        B->>T4: 查询销售人员信息
        T4->>B: 返回销售联系方式
        Note right of T4: nick_name, phonenumber, email
        
        B->>F: 组装专区数据
        F->>C: 显示客户专区
    else 令牌无效
        B->>F: 要求重新验证
    end
```

## 7. 无密码关键技术点

### 7.1 企业微信无密码集成

- **OAuth2.0授权流程：** 使用企业微信提供的OAuth接口，完全不涉及密码
- **用户信息获取：** 通过企业微信API获取用户的unionid、用户名等基本信息
- **唯一标识：** 使用企业微信的unionid作为用户在系统中的唯一标识
- **令牌管理：** 生成JWT token进行会话管理，无需密码验证
- **自动绑定：** 首次登录时自动提示绑定手机号，完善用户信息

### 7.2 短信验证码无密码机制

- **验证码生成：** 6位数字随机验证码，有效期5分钟
- **频率限制：** 同一手机号1分钟内只能发送一条，每天最多10条
- **安全策略：** 验证码错误超过5次锁定账户10分钟
- **无密码存储：** 验证成功后直接生成访问令牌，不设置密码
- **令牌持久化：** 为客户生成长期有效的专用访问令牌

### 7.3 润物无声无密码注册策略

- **预注册：** 用户输入手机号发送验证码时就创建注册记录
- **完全无感知：** 用户只需要验证手机号，系统自动完成用户创建，无需任何密码
- **令牌访问：** 注册成功后生成专用访问令牌，用户可通过链接直接访问
- **线索转化：** 注册成功自动创建客户线索，分配给销售人员
- **专区管理：** 为每个客户创建专属的项目进度查看专区

### 7.4 客户专区无密码访问

- **专用链接：** 每个客户获得唯一的专用访问链接
- **令牌验证：** 基于JWT令牌的安全访问机制
- **自动续期：** 令牌临近过期时自动通过短信验证续期
- **权限控制：** 客户只能访问自己的项目信息
- **实时更新：** 销售人员更新信息后客户可实时查看

## 8. 无密码安全考虑

### 8.1 无密码数据安全

- **手机号加密：** 存储时对手机号进行加密处理
- **验证码加密：** 验证码在数据库中加密存储
- **令牌安全：** JWT token设置合理有效期，支持刷新机制
- **敏感信息脱敏：** 日志中的手机号等敏感信息进行脱敏处理
- **访问日志：** 记录所有无密码访问行为，便于安全审计

### 8.2 无密码访问控制

- **角色权限：** 区分员工和客户角色，设置不同的系统访问权限
- **API权限：** 不同类型的API接口设置不同的访问权限
- **会话管理：** 基于令牌的会话管理，无需密码验证
- **设备绑定：** 可选的设备绑定机制，增强安全性
- **地理位置：** 可选的地理位置验证，防止异地恶意访问

### 8.3 防止滥用机制

- **验证码限制：** 严格的发送频率和次数限制
- **IP黑名单：** 恶意请求的IP地址自动加入黑名单
- **行为分析：** 异常访问行为自动检测和预警
- **令牌撤销：** 支持手动撤销和批量撤销访问令牌

## 9. 实施计划

### 阶段一：数据库表设计和创建
- **创建新增表结构**：用户注册表、认证方式表、客户线索表、线索文件表、沟通记录表
- **建立表关系**：外键关系和索引优化
- **数据清理**：清理现有 `sys_user` 表中客户用户的密码字段
- **数据迁移**：将现有数据安全迁移到新表结构

```sql
-- 示例：添加新增表的外键约束
ALTER TABLE `crm_user_auth_methods` 
ADD CONSTRAINT `fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`user_id`);

ALTER TABLE `crm_customer_leads` 
ADD CONSTRAINT `fk_leads_user_id` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`user_id`),
ADD CONSTRAINT `fk_leads_assigned_to` FOREIGN KEY (`assigned_to`) REFERENCES `sys_user` (`user_id`);
```

### 阶段二：无密码后端API开发
- **企业微信OAuth接口**：实现无密码登录API
- **短信验证服务**：发送和验证接口
- **用户注册认证**：无密码注册流程API
- **客户线索管理**：CRUD接口开发
- **令牌管理服务**：JWT无密码令牌服务
- **客户专区接口**：专用访问API

```java
// 示例：无密码认证接口
@RestController
@RequestMapping("/crm/auth")
public class PasswordlessAuthController {
    
    @PostMapping("/wechat/login")
    public AjaxResult wechatLogin(@RequestBody WechatLoginRequest request) {
        // 企业微信OAuth无密码登录
        return authService.wechatPasswordlessLogin(request);
    }
    
    @PostMapping("/sms/send")
    public AjaxResult sendSmsCode(@RequestBody SmsRequest request) {
        // 发送短信验证码（无需密码）
        return authService.sendPasswordlessCode(request);
    }
    
    @PostMapping("/sms/verify")
    public AjaxResult verifySmsCode(@RequestBody VerifyRequest request) {
        // 验证码登录/注册（完全无密码）
        return authService.passwordlessVerify(request);
    }
}
```

### 阶段三：前端界面开发
- **企业微信登录页**：扫码无密码登录界面
- **手机号绑定页**：首次绑定（无密码设置）
- **客户上传页面**：无密码注册集成
- **销售管理界面**：线索处理页面
- **客户专区页面**：无密码访问体验
- **移动端适配**：响应式设计

```typescript
// 示例：无密码认证API接口
interface PasswordlessAuthAPI {
  // 企业微信无密码登录
  wechatLogin(code: string): Promise<AuthResponse>;
  
  // 发送无密码验证码
  sendSmsCode(phone: string): Promise<BaseResponse>;
  
  // 无密码验证登录/注册
  verifySmsCode(phone: string, code: string): Promise<AuthResponse>;
  
  // 客户专用令牌访问
  accessCustomerZone(token: string): Promise<CustomerData>;
}
```

### 阶段四：集成测试和部署
- **无密码流程测试**：端到端认证测试
- **安全性测试**：令牌安全和防护测试
- **用户体验测试**：无密码体验优化
- **兼容性测试**：跨浏览器和设备测试
- **生产环境部署**：Docker容器化部署
- **用户培训**：无密码系统使用培训

```bash
# 示例：部署脚本
#!/bin/bash
# 无密码CRM系统部署脚本

# 1. 备份数据库
mysqldump -u root -p crm_db > crm_backup_$(date +%Y%m%d).sql

# 2. 执行数据库迁移
mysql -u root -p crm_db < migration/passwordless_tables.sql

# 3. 部署后端服务
docker-compose up -d crm-backend

# 4. 部署前端应用
docker-compose up -d crm-frontend

# 5. 验证无密码服务状态
curl -X GET http://localhost:8080/crm/auth/health
```

## 10. 监控和维护

### 10.1 监控指标和报警阈值

| 监控项目 | 监控指标 | 报警阈值 | 处理措施 |
|----------|----------|----------|----------|
| 短信发送 | 发送成功率 | < 95% | 检查短信服务商状态 |
| 企业微信登录 | 无密码登录成功率 | < 98% | 检查企业微信API状态 |
| 无密码注册转化 | 注册完成率 | < 80% | 优化无密码注册流程 |
| 令牌安全 | 令牌滥用率 | > 1% | 加强令牌安全策略 |
| 线索处理 | 线索处理时效 | > 24小时 | 提醒销售人员处理 |
| 客户专区访问 | 访问成功率 | < 95% | 检查专区服务状态 |
| 文件上传 | 上传成功率 | < 90% | 检查文件存储服务 |

### 10.2 监控实现代码

```java
// 无密码认证监控服务
@Service
public class PasswordlessMonitorService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 监控短信发送成功率
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void monitorSmsSuccessRate() {
        String key = "sms:stats:" + DateUtils.today();
        Long totalSent = (Long) redisTemplate.opsForHash().get(key, "total");
        Long successSent = (Long) redisTemplate.opsForHash().get(key, "success");
        
        if (totalSent != null && totalSent > 0) {
            double successRate = (double) successSent / totalSent * 100;
            if (successRate < 95) {
                // 发送报警
                alertService.sendAlert("短信发送成功率异常", 
                    String.format("当前成功率: %.2f%%", successRate));
            }
        }
    }
    
    /**
     * 监控无密码令牌使用情况
     */
    @Scheduled(fixedRate = 600000) // 每10分钟执行一次
    public void monitorTokenUsage() {
        // 查询可疑的令牌使用模式
        List<TokenUsageLog> suspiciousUsage = tokenService.findSuspiciousUsage();
        
        if (!suspiciousUsage.isEmpty()) {
            alertService.sendAlert("令牌滥用检测", 
                "检测到可疑的令牌使用行为，数量: " + suspiciousUsage.size());
        }
    }
    
    /**
     * 监控客户专区访问成功率
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void monitorCustomerZoneAccess() {
        String key = "customer:zone:stats:" + DateUtils.today();
        Long totalAccess = (Long) redisTemplate.opsForHash().get(key, "total");
        Long successAccess = (Long) redisTemplate.opsForHash().get(key, "success");
        
        if (totalAccess != null && totalAccess > 0) {
            double successRate = (double) successAccess / totalAccess * 100;
            if (successRate < 95) {
                alertService.sendAlert("客户专区访问异常", 
                    String.format("当前访问成功率: %.2f%%", successRate));
            }
        }
    }
}
```

### 10.3 日志收集和分析

```yaml
# 示例：日志配置（logback-spring.xml）
<configuration>
    <appender name="PASSWORDLESS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/passwordless-auth.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/passwordless-auth.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <logger name="com.ruoyi.crm.auth.passwordless" level="INFO" additivity="false">
        <appender-ref ref="PASSWORDLESS_FILE"/>
    </logger>
</configuration>
```

### 10.4 性能监控脚本

```bash
#!/bin/bash
# 无密码认证系统性能监控脚本

# 检查短信服务状态
check_sms_service() {
    response=$(curl -s -w "%{http_code}" -o /dev/null http://localhost:8080/crm/auth/sms/health)
    if [ "$response" != "200" ]; then
        echo "$(date): 短信服务异常，状态码: $response" >> /var/log/crm-monitor.log
        # 发送报警
        curl -X POST "http://alert-server/api/alert" \
            -H "Content-Type: application/json" \
            -d '{"type":"sms_service_down","message":"短信服务不可用"}'
    fi
}

# 检查企业微信API状态
check_wechat_api() {
    response=$(curl -s -w "%{http_code}" -o /dev/null http://localhost:8080/crm/auth/wechat/health)
    if [ "$response" != "200" ]; then
        echo "$(date): 企业微信API异常，状态码: $response" >> /var/log/crm-monitor.log
        curl -X POST "http://alert-server/api/alert" \
            -H "Content-Type: application/json" \
            -d '{"type":"wechat_api_down","message":"企业微信API不可用"}'
    fi
}

# 检查数据库连接
check_database() {
    mysql -h localhost -u monitor -p${DB_PASSWORD} -e "SELECT 1;" crm_db > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "$(date): 数据库连接异常" >> /var/log/crm-monitor.log
        curl -X POST "http://alert-server/api/alert" \
            -H "Content-Type: application/json" \
            -d '{"type":"database_down","message":"数据库连接失败"}'
    fi
}

# 每分钟执行监控检查
while true; do
    check_sms_service
    check_wechat_api
    check_database
    sleep 60
done
```

### 10.5 安全审计

```sql
-- 无密码认证安全审计查询
-- 1. 查询异常登录行为
SELECT 
    phone_number,
    COUNT(*) as login_attempts,
    MAX(create_time) as last_attempt
FROM crm_user_registration 
WHERE registration_type = 'sms' 
    AND create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY phone_number 
HAVING login_attempts > 10
ORDER BY login_attempts DESC;

-- 2. 查询可疑的令牌使用
SELECT 
    access_token,
    phone_number,
    COUNT(*) as access_count,
    COUNT(DISTINCT DATE(create_time)) as access_days
FROM crm_customer_leads 
WHERE access_token IS NOT NULL
    AND create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY access_token, phone_number
HAVING access_count > 100 OR access_days > 5;

-- 3. 查询验证码发送频率异常
SELECT 
    phone_number,
    COUNT(*) as code_requests,
    MIN(create_time) as first_request,
    MAX(create_time) as last_request
FROM crm_user_registration 
WHERE create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
GROUP BY phone_number 
HAVING code_requests > 20
ORDER BY code_requests DESC;
```

## 11. 总结

本设计方案提供了一个完整的无密码双轨用户注册登录系统，完全摒弃了传统的密码认证机制，既满足了内部员工通过企业微信便捷登录的需求，又实现了外部客户的完全无感知注册体验。

### 核心优势：

- 🚫 **完全无密码** - 系统不涉及任何密码设置和管理
- 🔄 **双轨认证机制** - 满足不同用户群体需求
- 📱 **现代化用户体验** - 基于手机和企业微信的便捷认证
- 🔐 **高安全性** - OAuth和短信验证的双重安全保障
- 💡 **润物无声注册** - 客户无感知的账户创建过程
- 📊 **完整线索管理** - 支持业务发展的线索转化流程
- 🏠 **客户专区** - 为每个客户提供专属的项目跟踪空间
- 📞 **完整沟通链路** - 从线索到成交的全流程管理

### 设计理念：

在现代互联网时代，没有人愿意记住密码。我们的系统完全基于这一理念设计，为用户提供最便捷、最安全的无密码认证体验，同时确保业务流程的完整性和高效性。
