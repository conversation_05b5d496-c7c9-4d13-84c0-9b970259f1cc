// 统一导出所有类型
export * from './api/responseTypes';
export * from './components/filter';
export * from './components/form';
export * from './components/table';
export * from './modules/management';
export * from './printing/modelTypes';
export * from './printing/quoteTypes';

// API 响应接口
export interface ApiResponse<T> {
    code: number;
    msg: string;
    rows?: T[];
    total?: number;
    data?: T;
}

export interface ApiResponseOnlyDataArray<T> {
    code: number;
    msg: string;
    data: T[];
}

// 表格行数据接口
export interface TableRowData {
    [key: string]: any;
}

// 线索数据接口
export interface LeadData extends TableRowData {
    id: number;
    name: string;
    source: string;
    status: string;
    phone: string;
    email: string;
    industry: string;
    remarks?: string;
    createTime?: string;
    lastFollowUpTime?: string;
    nextFollowUpTime?: string;
    owner?: string;
    attachmentsCount?: number;
}

// 筛选类型
export type FilterType = 'all' | 'mine' | 'subordinate' | 'following';

export interface TableColumn {
    type?: string;
    prop?: string;
    label?: string;
    width?: string | number;
    minWidth?: string | number;
    fixed?: string | boolean;
    sortable?: boolean | string;
    link?: boolean;
    key?: string;
}
