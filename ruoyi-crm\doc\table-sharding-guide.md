# CRM系统分表功能使用指南

## 概述

本CRM系统采用MyBatis-Plus动态表名插件实现分表功能，支持按时间维度自动分表，提高大数据量场景下的查询性能。

## 功能特性

- ✅ **自动分表**：根据配置自动创建分表
- ✅ **透明操作**：业务代码无需修改，自动路由到正确的分表
- ✅ **跨表查询**：支持跨多个分表进行查询
- ✅ **自动清理**：定时清理过期的分表
- ✅ **预创建表**：提前创建未来的分表
- ✅ **统计监控**：提供分表统计信息

## 分表策略

### 当前配置的分表策略

| 表名 | 分表策略 | 分表字段 | 说明 |
|------|----------|----------|------|
| `crm_business_leads` | 按月分表 | `create_time` | 线索表，数据量大，按月分表 |
| `crm_business_customers` | 按年分表 | `create_time` | 客户表，数据相对稳定，按年分表 |
| `crm_lead_operation_log` | 按月分表 | `operation_time` | 线索操作日志，按月分表 |
| `crm_customer_operation_log` | 按月分表 | `operation_time` | 客户操作日志，按月分表 |

### 分表命名规则

- **按月分表**：`表名_YYYYMM`，如 `crm_business_leads_202412`
- **按年分表**：`表名_YYYY`，如 `crm_business_customers_2024`

## 配置说明

### 1. 启用/禁用分表功能

在 `application-sharding.yml` 中配置：

```yaml
crm:
  sharding:
    enabled: true  # 启用分表功能
```

### 2. 数据保留策略

```yaml
crm:
  sharding:
    data-retention-months: 36  # 保留36个月的数据
    cleanup-cron: "0 0 2 1 * ?" # 每月1号凌晨2点清理过期表
```

### 3. 预创建策略

```yaml
crm:
  sharding:
    pre-create-months: 3  # 预创建未来3个月的分表
```

## API接口

### 1. 获取分表配置

```http
GET /crm/table-sharding/config
```

### 2. 预创建分表

```http
POST /crm/table-sharding/pre-create?months=3
```

### 3. 清理过期分表

```http
POST /crm/table-sharding/cleanup
```

### 4. 获取表统计信息

```http
GET /crm/table-sharding/statistics/{tableName}
```

### 5. 跨表查询线索

```http
GET /crm/table-sharding/leads/query?startDate=2024-01-01&endDate=2024-12-31&leadSource=网络&status=1
```

### 6. 跨表查询客户

```http
GET /crm/table-sharding/customers/query?startDate=2024-01-01&endDate=2024-12-31&customerName=测试公司
```

### 7. 获取当前使用的表名

```http
GET /crm/table-sharding/current-tables
```

## 使用示例

### 1. 业务代码中的使用

业务代码无需修改，MyBatis-Plus会自动将表名路由到正确的分表：

```java
@Service
public class CrmLeadsServiceImpl {
    
    @Autowired
    private CrmLeadsMapper leadsMapper;
    
    // 正常的业务操作，会自动路由到当前月份的分表
    public void insertLead(CrmLeads lead) {
        leadsMapper.insert(lead);  // 自动插入到 crm_business_leads_202412
    }
    
    public List<CrmLeads> selectLeads(CrmLeads query) {
        return leadsMapper.selectList(query);  // 自动查询 crm_business_leads_202412
    }
}
```

### 2. 跨表查询示例

```java
@Service
public class ReportService {
    
    @Autowired
    private ITableShardingService tableShardingService;
    
    // 查询过去6个月的线索数据
    public List<Map<String, Object>> getLeadsReport() {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusMonths(6);
        
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("status", "1");
        
        return tableShardingService.queryLeadsAcrossTables(startDate, endDate, conditions);
    }
}
```

### 3. 手动指定表名（高级用法）

```java
// 使用ThreadLocal指定特定的表名
MybatisPlusConfig.TableNameContext.setTableName("crm_business_leads", "crm_business_leads_202411");
try {
    // 这里的操作会使用指定的表名
    List<CrmLeads> leads = leadsMapper.selectList(query);
} finally {
    // 清理ThreadLocal
    MybatisPlusConfig.TableNameContext.clear();
}
```

## 数据迁移

### 1. 从原表迁移到分表

```http
POST /crm/table-sharding/migrate
Content-Type: application/x-www-form-urlencoded

sourceTable=crm_business_leads&targetTable=crm_business_leads_202412&dateCondition=2024-12-01
```

### 2. 批量数据迁移脚本

```sql
-- 迁移2024年12月的数据到分表
INSERT INTO crm_business_leads_202412 
SELECT * FROM crm_business_leads 
WHERE DATE(create_time) >= '2024-12-01' AND DATE(create_time) < '2025-01-01';

-- 删除已迁移的数据（谨慎操作）
DELETE FROM crm_business_leads 
WHERE DATE(create_time) >= '2024-12-01' AND DATE(create_time) < '2025-01-01';
```

## 监控和维护

### 1. 定时任务

系统会自动执行以下定时任务：

- **清理过期分表**：每月1号凌晨2点执行
- **预创建未来分表**：可手动触发或在系统启动时执行

### 2. 监控指标

- 各分表的记录数量
- 各分表的存储大小
- 分表创建和清理日志

### 3. 性能优化建议

1. **索引策略**：为分表创建合适的索引
2. **查询优化**：尽量指定时间范围，避免全表扫描
3. **分区键选择**：选择合适的分表字段
4. **数据归档**：定期归档历史数据

## 注意事项

1. **事务处理**：跨表操作不支持事务，需要应用层处理
2. **外键约束**：分表不支持外键约束
3. **数据一致性**：需要应用层保证数据一致性
4. **备份策略**：需要调整备份策略适应分表结构
5. **升级维护**：表结构变更需要同步到所有分表

## 故障排除

### 1. 表不存在错误

```
Table 'crm_business_leads_202412' doesn't exist
```

**解决方案**：
- 检查分表是否正确创建
- 手动调用预创建接口
- 检查TableShardingManager配置

### 2. 动态表名不生效

**解决方案**：
- 检查MyBatis-Plus配置是否正确
- 确认动态表名插件已注册
- 查看日志确认表名替换逻辑

### 3. 跨表查询性能问题

**解决方案**：
- 缩小查询时间范围
- 添加合适的索引
- 考虑数据预聚合

## 扩展功能

### 1. 自定义分表策略

可以扩展 `TableShardingManager` 类，实现自定义的分表策略。

### 2. 分表路由规则

可以根据业务需要，实现更复杂的分表路由规则。

### 3. 数据同步

可以集成数据同步工具，实现分表间的数据同步。

---

如有问题，请查看系统日志或联系技术支持。 