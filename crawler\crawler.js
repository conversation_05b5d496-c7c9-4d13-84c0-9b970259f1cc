import axios from 'axios';
import * as cheerio from 'cheerio';
import { setTimeout } from 'timers/promises';
import config from './config.js';

// 添加日志函数
function logWithTimestamp(message, error = null) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
  if (error) {
    console.error(`[${timestamp}] 错误详情:`, error);
  }
}

// 请求重试函数
async function requestWithRetry(url, options = {}, retries = config.crawler.retryTimes) {
  try {
    logWithTimestamp(`开始请求URL: ${url}`);
    const response = await axios.get(url, {
      ...options,
      headers: config.crawler.headers,
      timeout: config.crawler.timeout
    });
    logWithTimestamp(`请求成功: ${url}`);
    return response;
  } catch (error) {
    logWithTimestamp(`请求失败: ${url}`, error);
    if (retries > 0) {
      const delay = Math.random() * 
        (config.crawler.delay.max - config.crawler.delay.min) + 
        config.crawler.delay.min;
      logWithTimestamp(`等待 ${delay}ms 后进行第 ${config.crawler.retryTimes - retries + 1} 次重试`);
      await setTimeout(delay);
      return requestWithRetry(url, options, retries - 1);
    }
    throw error;
  }
}

/**
 * 爬取产品列表信息
 * @param {string} url - 要爬取的页面URL
 * @returns {Promise<Array>} 返回产品信息数组
 */
export async function crawlProductList(url) {
  try {
    logWithTimestamp(`开始爬取产品列表: ${url}`);
    // 添加随机延时
    const delay = Math.random() * 
      (config.crawler.delay.max - config.crawler.delay.min) + 
      config.crawler.delay.min;
    logWithTimestamp(`等待 ${delay}ms 后开始请求`);
    await setTimeout(delay);

    // 获取页面HTML
    const { data } = await requestWithRetry(url);
    logWithTimestamp('页面HTML获取成功，开始解析');
    const $ = cheerio.load(data);
    
    const products = [];
    
    // 解析产品列表
    $('.p_list > div').each((i, el) => {
      const product = {
        name: $(el).find('img').attr('title') || $(el).find('img').attr('alt'),
        price: $(el).find('.product-price').text().trim(),
        image: $(el).find('img').attr('src'),
        link: $(el).find('a').attr('href'),
        alt: $(el).find('img').attr('alt'),
        title: $(el).find('img').attr('title')
      };
      products.push(product);
    });
    
    logWithTimestamp(`产品列表解析完成，共找到 ${products.length} 个产品`);
    return products;
  } catch (error) {
    logWithTimestamp('产品列表爬取失败', error);
    throw error;
  }
}

/**
 * 爬取产品详情信息
 * @param {string} url - 产品详情页URL
 * @returns {Promise<Object>} 返回产品详情信息
 */
/**
 * 获取完整URL
 * @param {string} baseUrl - 基础URL
 * @param {string} path - 相对路径
 * @returns {string} 完整URL
 */
function getFullUrl(baseUrl, path) {
  try {
    const urlObj = new URL(baseUrl);
    const fullUrl = new URL(path, urlObj.origin).toString();
    logWithTimestamp(`URL转换: ${path} -> ${fullUrl}`);
    return fullUrl;
  } catch (error) {
    logWithTimestamp('URL转换失败', error);
    throw error;
  }
}

export async function crawlProductDetail(url, baseUrl) {
  try {
    const fullUrl = url.startsWith('http') ? url : getFullUrl(baseUrl, url);
    logWithTimestamp(`开始爬取产品详情: ${fullUrl}`);

    // 添加随机延时
    const delay = Math.random() * 
      (config.crawler.delay.max - config.crawler.delay.min) + 
      config.crawler.delay.min;
    logWithTimestamp(`等待 ${delay}ms 后开始请求`);
    await setTimeout(delay);
    
    const { data } = await requestWithRetry(fullUrl);
    logWithTimestamp('产品详情页面获取成功，开始解析');
    const $ = cheerio.load(data);

    // 解析材料特性
    logWithTimestamp('正在解析材料特性');
    let materialProperties = $('.e_text-55.s_title').first().text().trim() || '';
    let materialProcess = $('.e_container-57').first().text().trim() || '';
    const techSpecs = {};

    // 解析技术参数
    logWithTimestamp('正在解析技术参数');
    $('.p_loopItem').each((i, el) => {
      const key = $(el).find('.e_text-123').text().replace(/\s+/g, '');
      const value = $(el).find('.e_text-126').text().replace(/\s+/g, '');
      if (key) {
        techSpecs[key] = value || '';
      }
    });

    // 解析材料评价
    logWithTimestamp('正在解析材料评价');
    const materialEvaluation = {
      advantages: $('.e_text-127').text().replace(/优点/, '').trim() || '',
      disadvantages: $('.e_text-129').text().replace(/缺点/, '').trim() || ''
    };

    // 解析应用领域
    logWithTimestamp('正在解析应用领域');
    const applicationAreas = [];
    $('.e_richText-110.s_link p').each((i, el) => {
      const area = $(el).text().trim();
      if (area) {
        applicationAreas.push(area);
      }
    });

    // 解析产品图片
    logWithTimestamp('正在解析产品图片');
    const productImages = [];
    $('.swiper-slide .p_img').each((i, el) => {
      const imgUrl = $(el).find('img').attr('src');
      if (imgUrl) {
        productImages.push(imgUrl);
      }
    });

    logWithTimestamp('产品详情解析完成');
    return {
      materialProperties,
      materialProcess,
      techSpecs,
      materialEvaluation,
      applicationAreas,
      productImages
    };
  } catch (error) {
    logWithTimestamp('产品详情爬取失败', error);
    throw error;
  }
}

// 示例用法
// const url = 'http://2302245059.p.make.dcloud.portal1.portal.thefastmake.com/pro_list_1/15.html';
// crawlProductList(url).then(products => console.log(products));
