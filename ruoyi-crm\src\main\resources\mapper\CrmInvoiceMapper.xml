<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmInvoiceMapper">
    
    <resultMap type="CrmInvoice" id="CrmInvoiceResult">
        <result property="id"    column="id"    />
        <result property="invoiceNo"    column="invoice_no"    />
        <result property="invoiceTitle"    column="invoice_title"    />
        <result property="invoiceType"    column="invoice_type"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="contactId"    column="contact_id"    />
        <result property="contactName"    column="contact_name"    />
        <result property="quotationId"    column="quotation_id"    />
        <result property="contractId"    column="contract_id"    />
        <result property="contractNo"    column="contract_no"    />
        <result property="responsiblePersonId"    column="responsible_person_id"    />
        <result property="taxpayerId"    column="taxpayer_id"    />
        <result property="taxRate"    column="tax_rate"    />
        <result property="amountExcludingTax"    column="amount_excluding_tax"    />
        <result property="taxAmount"    column="tax_amount"    />
        <result property="amountIncludingTax"    column="amount_including_tax"    />
        <result property="bankName"    column="bank_name"    />
        <result property="bankAccount"    column="bank_account"    />
        <result property="companyAddress"    column="company_address"    />
        <result property="companyPhone"    column="company_phone"    />
        <result property="status"    column="status"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="processInstanceId"    column="process_instance_id"    />
        <result property="processStatus"    column="process_status"    />
        <result property="currentTaskId"    column="current_task_id"    />
        <result property="currentTaskName"    column="current_task_name"    />
        <result property="currentAssignee"    column="current_assignee"    />
        <result property="processStartTime"    column="process_start_time"    />
        <result property="processEndTime"    column="process_end_time"    />
        <result property="issueDate"    column="issue_date"    />
        <result property="dueDate"    column="due_date"    />
        <result property="cancelDate"    column="cancel_date"    />
        <result property="cancelReason"    column="cancel_reason"    />
        <result property="currency"    column="currency"    />
        <result property="exchangeRate"    column="exchange_rate"    />
        <result property="remarks"    column="remarks"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="CrmInvoiceCrmInvoiceItemResult" type="CrmInvoice" extends="CrmInvoiceResult">
        <collection property="invoiceItems" javaType="java.util.List" resultMap="CrmInvoiceItemResult" />
        <collection property="invoiceAttachments" javaType="java.util.List" resultMap="CrmInvoiceAttachmentResult" />
    </resultMap>

    <resultMap type="CrmInvoiceItem" id="CrmInvoiceItemResult">
        <result property="id"    column="item_id"    />
        <result property="invoiceId"    column="item_invoice_id"    />
        <result property="itemName"    column="item_name"    />
        <result property="itemCode"    column="item_code"    />
        <result property="specification"    column="item_specification"    />
        <result property="unit"    column="item_unit"    />
        <result property="quantity"    column="item_quantity"    />
        <result property="unitPrice"    column="item_unit_price"    />
        <result property="amount"    column="item_amount"    />
        <result property="taxRate"    column="item_tax_rate"    />
        <result property="taxAmount"    column="item_tax_amount"    />
        <result property="sortOrder"    column="item_sort_order"    />
        <result property="remarks"    column="item_remarks"    />
    </resultMap>

    <resultMap type="CrmInvoiceAttachment" id="CrmInvoiceAttachmentResult">
        <result property="id"    column="att_id"    />
        <result property="invoiceId"    column="att_invoice_id"    />
        <result property="fileName"    column="att_file_name"    />
        <result property="fileOriginalName"    column="att_file_original_name"    />
        <result property="filePath"    column="att_file_path"    />
        <result property="fileUrl"    column="att_file_url"    />
        <result property="fileSize"    column="att_file_size"    />
        <result property="fileType"    column="att_file_type"    />
        <result property="fileExtension"    column="att_file_extension"    />
        <result property="attachmentType"    column="att_attachment_type"    />
        <result property="attachmentCategory"    column="att_attachment_category"    />
        <result property="description"    column="att_description"    />
        <result property="sortOrder"    column="att_sort_order"    />
        <result property="isMain"    column="att_is_main"    />
    </resultMap>

    <sql id="selectCrmInvoiceVo">
        select id, invoice_no, invoice_title, invoice_type, customer_id, customer_name, contact_id, contact_name, 
               quotation_id, contract_id, contract_no, responsible_person_id, taxpayer_id, tax_rate, 
               amount_excluding_tax, tax_amount, amount_including_tax, bank_name, bank_account, 
               company_address, company_phone, status, approval_status, process_instance_id, process_status, 
               current_task_id, current_task_name, current_assignee, process_start_time, process_end_time, 
               issue_date, due_date, cancel_date, cancel_reason, currency, exchange_rate, remarks, 
               del_flag, create_by, create_time, update_by, update_time 
        from crm_invoices
    </sql>

    <select id="selectCrmInvoiceList" parameterType="CrmInvoice" resultMap="CrmInvoiceResult">
        <include refid="selectCrmInvoiceVo"/>
        <where>
            del_flag = '0'
            <if test="invoiceNo != null  and invoiceNo != ''"> and invoice_no like concat('%', #{invoiceNo}, '%')</if>
            <if test="invoiceTitle != null  and invoiceTitle != ''"> and invoice_title like concat('%', #{invoiceTitle}, '%')</if>
            <if test="invoiceType != null  and invoiceType != ''"> and invoice_type = #{invoiceType}</if>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="contactId != null "> and contact_id = #{contactId}</if>
            <if test="quotationId != null "> and quotation_id = #{quotationId}</if>
            <if test="contractId != null "> and contract_id = #{contractId}</if>
            <if test="responsiblePersonId != null  and responsiblePersonId != ''"> and responsible_person_id = #{responsiblePersonId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="approvalStatus != null  and approvalStatus != ''"> and approval_status = #{approvalStatus}</if>
            <if test="params.beginIssueDate != null and params.beginIssueDate != ''">
                and date_format(issue_date,'%y%m%d') &gt;= date_format(#{params.beginIssueDate},'%y%m%d')
            </if>
            <if test="params.endIssueDate != null and params.endIssueDate != ''">
                and date_format(issue_date,'%y%m%d') &lt;= date_format(#{params.endIssueDate},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectCrmInvoiceById" parameterType="Long" resultMap="CrmInvoiceCrmInvoiceItemResult">
        select a.id, a.invoice_no, a.invoice_title, a.invoice_type, a.customer_id, a.customer_name, 
               a.contact_id, a.contact_name, a.quotation_id, a.contract_id, a.contract_no, 
               a.responsible_person_id, a.taxpayer_id, a.tax_rate, a.amount_excluding_tax, 
               a.tax_amount, a.amount_including_tax, a.bank_name, a.bank_account, a.company_address, 
               a.company_phone, a.status, a.approval_status, a.process_instance_id, a.process_status, 
               a.current_task_id, a.current_task_name, a.current_assignee, a.process_start_time, 
               a.process_end_time, a.issue_date, a.due_date, a.cancel_date, a.cancel_reason, 
               a.currency, a.exchange_rate, a.remarks, a.del_flag, a.create_by, a.create_time, 
               a.update_by, a.update_time,
               b.id as item_id, b.invoice_id as item_invoice_id, b.item_name as item_name, 
               b.item_code as item_code, b.specification as item_specification, b.unit as item_unit, 
               b.quantity as item_quantity, b.unit_price as item_unit_price, b.amount as item_amount, 
               b.tax_rate as item_tax_rate, b.tax_amount as item_tax_amount, b.sort_order as item_sort_order, 
               b.remarks as item_remarks,
               c.id as att_id, c.invoice_id as att_invoice_id, c.file_name as att_file_name, 
               c.file_original_name as att_file_original_name, c.file_path as att_file_path, 
               c.file_url as att_file_url, c.file_size as att_file_size, c.file_type as att_file_type, 
               c.file_extension as att_file_extension, c.attachment_type as att_attachment_type, 
               c.attachment_category as att_attachment_category, c.description as att_description, 
               c.sort_order as att_sort_order, c.is_main as att_is_main
        from crm_invoices a
        left join crm_invoice_items b on b.invoice_id = a.id
        left join crm_invoice_attachments c on c.invoice_id = a.id and c.del_flag = '0'
        where a.id = #{id} and a.del_flag = '0'
        order by b.sort_order, c.sort_order
    </select>

    <select id="selectCrmInvoiceByInvoiceNo" parameterType="String" resultMap="CrmInvoiceResult">
        <include refid="selectCrmInvoiceVo"/>
        where invoice_no = #{invoiceNo} and del_flag = '0'
    </select>

    <select id="selectCrmInvoiceByProcessInstanceId" parameterType="String" resultMap="CrmInvoiceResult">
        <include refid="selectCrmInvoiceVo"/>
        where process_instance_id = #{processInstanceId} and del_flag = '0'
    </select>

    <select id="selectCrmInvoiceByQuotationId" parameterType="Long" resultMap="CrmInvoiceResult">
        <include refid="selectCrmInvoiceVo"/>
        where quotation_id = #{quotationId} and del_flag = '0'
    </select>
        
    <insert id="insertCrmInvoice" parameterType="CrmInvoice" useGeneratedKeys="true" keyProperty="id">
        insert into crm_invoices
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceNo != null and invoiceNo != ''">invoice_no,</if>
            <if test="invoiceTitle != null and invoiceTitle != ''">invoice_title,</if>
            <if test="invoiceType != null and invoiceType != ''">invoice_type,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="contactId != null">contact_id,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="quotationId != null">quotation_id,</if>
            <if test="contractId != null">contract_id,</if>
            <if test="contractNo != null">contract_no,</if>
            <if test="responsiblePersonId != null and responsiblePersonId != ''">responsible_person_id,</if>
            <if test="taxpayerId != null">taxpayer_id,</if>
            <if test="taxRate != null">tax_rate,</if>
            <if test="amountExcludingTax != null">amount_excluding_tax,</if>
            <if test="taxAmount != null">tax_amount,</if>
            <if test="amountIncludingTax != null">amount_including_tax,</if>
            <if test="bankName != null">bank_name,</if>
            <if test="bankAccount != null">bank_account,</if>
            <if test="companyAddress != null">company_address,</if>
            <if test="companyPhone != null">company_phone,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status,</if>
            <if test="processInstanceId != null">process_instance_id,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="currentTaskId != null">current_task_id,</if>
            <if test="currentTaskName != null">current_task_name,</if>
            <if test="currentAssignee != null">current_assignee,</if>
            <if test="processStartTime != null">process_start_time,</if>
            <if test="processEndTime != null">process_end_time,</if>
            <if test="issueDate != null">issue_date,</if>
            <if test="dueDate != null">due_date,</if>
            <if test="cancelDate != null">cancel_date,</if>
            <if test="cancelReason != null">cancel_reason,</if>
            <if test="currency != null and currency != ''">currency,</if>
            <if test="exchangeRate != null">exchange_rate,</if>
            <if test="remarks != null">remarks,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceNo != null and invoiceNo != ''">#{invoiceNo},</if>
            <if test="invoiceTitle != null and invoiceTitle != ''">#{invoiceTitle},</if>
            <if test="invoiceType != null and invoiceType != ''">#{invoiceType},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="contactId != null">#{contactId},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="quotationId != null">#{quotationId},</if>
            <if test="contractId != null">#{contractId},</if>
            <if test="contractNo != null">#{contractNo},</if>
            <if test="responsiblePersonId != null and responsiblePersonId != ''">#{responsiblePersonId},</if>
            <if test="taxpayerId != null">#{taxpayerId},</if>
            <if test="taxRate != null">#{taxRate},</if>
            <if test="amountExcludingTax != null">#{amountExcludingTax},</if>
            <if test="taxAmount != null">#{taxAmount},</if>
            <if test="amountIncludingTax != null">#{amountIncludingTax},</if>
            <if test="bankName != null">#{bankName},</if>
            <if test="bankAccount != null">#{bankAccount},</if>
            <if test="companyAddress != null">#{companyAddress},</if>
            <if test="companyPhone != null">#{companyPhone},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="approvalStatus != null and approvalStatus != ''">#{approvalStatus},</if>
            <if test="processInstanceId != null">#{processInstanceId},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="currentTaskId != null">#{currentTaskId},</if>
            <if test="currentTaskName != null">#{currentTaskName},</if>
            <if test="currentAssignee != null">#{currentAssignee},</if>
            <if test="processStartTime != null">#{processStartTime},</if>
            <if test="processEndTime != null">#{processEndTime},</if>
            <if test="issueDate != null">#{issueDate},</if>
            <if test="dueDate != null">#{dueDate},</if>
            <if test="cancelDate != null">#{cancelDate},</if>
            <if test="cancelReason != null">#{cancelReason},</if>
            <if test="currency != null and currency != ''">#{currency},</if>
            <if test="exchangeRate != null">#{exchangeRate},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCrmInvoice" parameterType="CrmInvoice">
        update crm_invoices
        <trim prefix="SET" suffixOverrides=",">
            <if test="invoiceNo != null and invoiceNo != ''">invoice_no = #{invoiceNo},</if>
            <if test="invoiceTitle != null and invoiceTitle != ''">invoice_title = #{invoiceTitle},</if>
            <if test="invoiceType != null and invoiceType != ''">invoice_type = #{invoiceType},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="contactId != null">contact_id = #{contactId},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="quotationId != null">quotation_id = #{quotationId},</if>
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="contractNo != null">contract_no = #{contractNo},</if>
            <if test="responsiblePersonId != null and responsiblePersonId != ''">responsible_person_id = #{responsiblePersonId},</if>
            <if test="taxpayerId != null">taxpayer_id = #{taxpayerId},</if>
            <if test="taxRate != null">tax_rate = #{taxRate},</if>
            <if test="amountExcludingTax != null">amount_excluding_tax = #{amountExcludingTax},</if>
            <if test="taxAmount != null">tax_amount = #{taxAmount},</if>
            <if test="amountIncludingTax != null">amount_including_tax = #{amountIncludingTax},</if>
            <if test="bankName != null">bank_name = #{bankName},</if>
            <if test="bankAccount != null">bank_account = #{bankAccount},</if>
            <if test="companyAddress != null">company_address = #{companyAddress},</if>
            <if test="companyPhone != null">company_phone = #{companyPhone},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status = #{approvalStatus},</if>
            <if test="processInstanceId != null">process_instance_id = #{processInstanceId},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="currentTaskId != null">current_task_id = #{currentTaskId},</if>
            <if test="currentTaskName != null">current_task_name = #{currentTaskName},</if>
            <if test="currentAssignee != null">current_assignee = #{currentAssignee},</if>
            <if test="processStartTime != null">process_start_time = #{processStartTime},</if>
            <if test="processEndTime != null">process_end_time = #{processEndTime},</if>
            <if test="issueDate != null">issue_date = #{issueDate},</if>
            <if test="dueDate != null">due_date = #{dueDate},</if>
            <if test="cancelDate != null">cancel_date = #{cancelDate},</if>
            <if test="cancelReason != null">cancel_reason = #{cancelReason},</if>
            <if test="currency != null and currency != ''">currency = #{currency},</if>
            <if test="exchangeRate != null">exchange_rate = #{exchangeRate},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmInvoiceById" parameterType="Long">
        update crm_invoices set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteCrmInvoiceByIds" parameterType="String">
        update crm_invoices set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <!-- 发票明细相关操作 -->
    <delete id="deleteCrmInvoiceItemByInvoiceIds" parameterType="String">
        delete from crm_invoice_items where invoice_id in 
        <foreach item="invoiceId" collection="array" open="(" separator="," close=")">
            #{invoiceId}
        </foreach>
    </delete>

    <delete id="deleteCrmInvoiceItemByInvoiceId" parameterType="Long">
        delete from crm_invoice_items where invoice_id = #{invoiceId}
    </delete>

    <insert id="batchCrmInvoiceItem">
        insert into crm_invoice_items( id, invoice_id, item_name, item_code, specification, unit, quantity, unit_price, amount, tax_rate, tax_amount, sort_order, remarks) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.invoiceId}, #{item.itemName}, #{item.itemCode}, #{item.specification}, #{item.unit}, #{item.quantity}, #{item.unitPrice}, #{item.amount}, #{item.taxRate}, #{item.taxAmount}, #{item.sortOrder}, #{item.remarks})
        </foreach>
    </insert>

    <select id="selectCrmInvoiceItemList" parameterType="Long" resultMap="CrmInvoiceItemResult">
        select id, invoice_id, item_name, item_code, specification, unit, quantity, unit_price, amount, tax_rate, tax_amount, sort_order, remarks
        from crm_invoice_items
        where invoice_id = #{invoiceId}
        order by sort_order
    </select>

    <!-- 发票附件相关操作 -->
    <delete id="deleteCrmInvoiceAttachmentByInvoiceIds" parameterType="String">
        delete from crm_invoice_attachments where invoice_id in 
        <foreach item="invoiceId" collection="array" open="(" separator="," close=")">
            #{invoiceId}
        </foreach>
    </delete>

    <delete id="deleteCrmInvoiceAttachmentByInvoiceId" parameterType="Long">
        delete from crm_invoice_attachments where invoice_id = #{invoiceId}
    </delete>

    <insert id="batchCrmInvoiceAttachment">
        insert into crm_invoice_attachments( id, invoice_id, file_name, file_original_name, file_path, file_url, file_size, file_type, file_extension, attachment_type, attachment_category, description, sort_order, is_main, del_flag, create_by, create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.invoiceId}, #{item.fileName}, #{item.fileOriginalName}, #{item.filePath}, #{item.fileUrl}, #{item.fileSize}, #{item.fileType}, #{item.fileExtension}, #{item.attachmentType}, #{item.attachmentCategory}, #{item.description}, #{item.sortOrder}, #{item.isMain}, #{item.delFlag}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <select id="selectCrmInvoiceAttachmentList" parameterType="Long" resultMap="CrmInvoiceAttachmentResult">
        select id, invoice_id, file_name, file_original_name, file_path, file_url, file_size, file_type, file_extension, attachment_type, attachment_category, description, sort_order, is_main
        from crm_invoice_attachments
        where invoice_id = #{invoiceId} and del_flag = '0'
        order by sort_order
    </select>
</mapper>