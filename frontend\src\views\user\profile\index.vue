<script lang="ts" setup>
import { getInfo } from '@/api/login';
import { useUserStore } from '@/store/modules/user';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

const userStore = useUserStore();

interface UserDept {
  deptId?: number;
  parentId?: number;
  ancestors?: string;
  deptName: string;
  orderNum?: number;
  leader?: string;
  status?: string;
  children?: UserDept[];
}

interface UserRole {
  roleId: number;
  roleName: string;
  roleKey: string;
  roleSort: number;
  dataScope: string;
  menuCheckStrictly: boolean;
  deptCheckStrictly: boolean;
  status: string;
  admin: boolean;
}

// 用户信息
const userInfo = ref({
  userId: 0,
  userName: '',
  nickName: '',
  email: '',
  phonenumber: '',
  sex: '',
  avatar: '',
  status: '',
  loginIp: '',
  loginDate: '',
  dept: {
    deptName: ''
  } as UserDept,
  roles: [] as UserRole[]
});

// 部门名称（单独提取出来，避免可选链赋值问题）
const deptName = ref('');

// 修改密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 密码表单校验规则
const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入旧密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: (error?: Error) => void) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
};

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const res = await getInfo();
    if (res.code === 200) {
      userInfo.value = res.user;
      // 单独设置部门名称
      deptName.value = res.user.dept?.deptName || '';
    } else {
      ElMessage.error(res.msg || '获取用户信息失败');
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    ElMessage.error('获取用户信息失败');
  }
};

// 修改密码
const passwordFormRef = ref();
const submitPasswordForm = async () => {
  if (!passwordFormRef.value) return;
  
  await passwordFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      ElMessage.success('密码修改功能正在开发中...');
      // 这里应该调用修改密码的API
      // 示例: await updatePassword(passwordForm);
      // 成功后清空表单
      passwordForm.oldPassword = '';
      passwordForm.newPassword = '';
      passwordForm.confirmPassword = '';
    }
  });
};

// 上传头像
const avatarUrl = ref(userStore.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png');
const handleAvatarSuccess = (res: any) => {
  if (res.code === 200) {
    avatarUrl.value = res.data;
    userStore.avatar = res.data;
    ElMessage.success('头像上传成功');
  } else {
    ElMessage.error(res.msg || '头像上传失败');
  }
};

const beforeAvatarUpload = (file: any) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPG) {
    ElMessage.error('上传头像图片只能是 JPG 或 PNG 格式!');
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!');
  }
  return isJPG && isLt2M;
};

// 激活的标签页
const activeName = ref('userInfo');

onMounted(() => {
  loadUserInfo();
});
</script>

<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>个人中心</span>
        </div>
      </template>
      
      <el-tabs v-model="activeName">
        <!-- 基本资料 -->
        <el-tab-pane label="基本资料" name="userInfo">
          <el-form label-width="100px" class="profile-form">
            <el-row>
              <el-col :span="24" :md="12">
                <el-form-item label="用户昵称">
                  <el-input v-model="userInfo.nickName" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="24" :md="12">
                <el-form-item label="用户名称">
                  <el-input v-model="userInfo.userName" disabled />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="24" :md="12">
                <el-form-item label="手机号码">
                  <el-input v-model="userInfo.phonenumber" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="24" :md="12">
                <el-form-item label="用户邮箱">
                  <el-input v-model="userInfo.email" disabled />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="24" :md="12">
                <el-form-item label="所属部门">
                  <el-input v-model="deptName" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="24" :md="12">
                <el-form-item label="最后登录">
                  <el-input v-model="userInfo.loginDate" disabled />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item>
              <el-button type="primary" disabled>保存</el-button>
              <el-button @click="loadUserInfo">刷新</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 修改密码 -->
        <el-tab-pane label="修改密码" name="resetPwd">
          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="100px"
            class="profile-form"
          >
            <el-form-item label="旧密码" prop="oldPassword">
              <el-input
                v-model="passwordForm.oldPassword"
                placeholder="请输入旧密码"
                type="password"
                show-password
              />
            </el-form-item>
            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                placeholder="请输入新密码"
                type="password"
                show-password
              />
            </el-form-item>
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                placeholder="请确认新密码"
                type="password"
                show-password
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitPasswordForm">保存</el-button>
              <el-button @click="passwordForm = { oldPassword: '', newPassword: '', confirmPassword: '' }">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 修改头像 -->
        <el-tab-pane label="修改头像" name="avatar">
          <div class="avatar-container">
            <div class="avatar-preview">
              <img :src="avatarUrl" class="avatar-image" />
            </div>
            <div class="avatar-upload">
              <p>支持 JPG、PNG 格式，文件大小不超过 2MB</p>
              <el-upload
                class="avatar-uploader"
                action="/dev-api/system/user/profile/avatar"
                :headers="{ Authorization: 'Bearer ' + userStore.token }"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <el-button type="primary">选择图片</el-button>
              </el-upload>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<style scoped>
.app-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.box-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profile-form {
  max-width: 700px;
  margin: 0 auto;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 400px;
  margin: 0 auto;
}

.avatar-preview {
  margin-bottom: 20px;
}

.avatar-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 1px solid #eee;
}

.avatar-upload {
  text-align: center;
}

.avatar-upload p {
  margin-bottom: 10px;
  color: #909399;
  font-size: 14px;
}

@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .profile-form {
    padding: 0 10px;
  }
}
</style> 