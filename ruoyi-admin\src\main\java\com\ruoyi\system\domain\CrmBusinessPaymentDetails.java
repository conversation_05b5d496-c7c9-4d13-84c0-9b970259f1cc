package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 回款明细对象 crm_business_payment_details
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public class CrmBusinessPaymentDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键，自增字段 */
    private Long id;

    /** 回款ID */
    @Excel(name = "回款ID")
    private Long paymentId;

    /** 回款计划ID */
    @Excel(name = "回款计划ID")
    private Long paymentPlanId;

    /** 回款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "回款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date paymentDate;

    /** 回款金额 */
    @Excel(name = "回款金额")
    private BigDecimal paymentAmount;

    /** 回款方式 */
    @Excel(name = "回款方式")
    private String paymentMethod;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 记录创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记录创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 记录更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记录更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPaymentId(Long paymentId) 
    {
        this.paymentId = paymentId;
    }

    public Long getPaymentId() 
    {
        return paymentId;
    }
    public void setPaymentPlanId(Long paymentPlanId) 
    {
        this.paymentPlanId = paymentPlanId;
    }

    public Long getPaymentPlanId() 
    {
        return paymentPlanId;
    }
    public void setPaymentDate(Date paymentDate) 
    {
        this.paymentDate = paymentDate;
    }

    public Date getPaymentDate() 
    {
        return paymentDate;
    }
    public void setPaymentAmount(BigDecimal paymentAmount) 
    {
        this.paymentAmount = paymentAmount;
    }

    public BigDecimal getPaymentAmount() 
    {
        return paymentAmount;
    }
    public void setPaymentMethod(String paymentMethod) 
    {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentMethod() 
    {
        return paymentMethod;
    }
    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("paymentId", getPaymentId())
            .append("paymentPlanId", getPaymentPlanId())
            .append("paymentDate", getPaymentDate())
            .append("paymentAmount", getPaymentAmount())
            .append("paymentMethod", getPaymentMethod())
            .append("remarks", getRemarks())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}
