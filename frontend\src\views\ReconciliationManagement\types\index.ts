export interface Reconciliation {
  reconciliationId: number;
  reconciliationNo: string;
  customerId: number;
  customerName: string;
  reconciliationDate: string | Date;
  reconciliationPeriod: string;
  totalAmount: number;
  status: string;
  processInstanceId?: string;
  processStatus?: string;
  createBy: string;
  createTime: string;
  remark?: string;
  details?: ReconciliationDetail[];
}

export interface ReconciliationDetail {
  detailId: number;
  reconciliationId: number;
  orderId: number;
  orderNo: string;
  amount: number;
  detailType: string;
  remark?: string;
}

export interface ReconciliationQuery {
  pageNum: number;
  pageSize: number;
  reconciliationNo?: string;
  customerName?: string;
  customerId?: number;
  status?: string;
}

export interface ReconciliationExportQuery {
  reconciliationNo?: string;
  customerName?: string;
  customerId?: number;
  status?: string;
}

export interface Order {
  orderId: number;
  orderNo: string;
  amount: number;
  orderDate: string;
}
