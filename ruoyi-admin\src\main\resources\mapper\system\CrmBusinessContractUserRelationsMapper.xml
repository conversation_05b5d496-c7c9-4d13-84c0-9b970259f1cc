<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CrmBusinessContractUserRelationsMapper">
    
    <resultMap type="CrmBusinessContractUserRelations" id="CrmBusinessContractUserRelationsResult">
        <result property="id"    column="id"    />
        <result property="contractId"    column="contract_id"    />
        <result property="userId"    column="user_id"    />
        <result property="relationType"    column="relation_type"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectCrmBusinessContractUserRelationsVo">
        select id, contract_id, user_id, relation_type, created_at, updated_at from crm_business_contract_user_relations
    </sql>

    <select id="selectCrmBusinessContractUserRelationsList" parameterType="CrmBusinessContractUserRelations" resultMap="CrmBusinessContractUserRelationsResult">
        <include refid="selectCrmBusinessContractUserRelationsVo"/>
        <where>  
            <if test="contractId != null "> and contract_id = #{contractId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="relationType != null  and relationType != ''"> and relation_type = #{relationType}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectCrmBusinessContractUserRelationsById" parameterType="Long" resultMap="CrmBusinessContractUserRelationsResult">
        <include refid="selectCrmBusinessContractUserRelationsVo"/>
        where id = #{id}
    </select>

    <insert id="insertCrmBusinessContractUserRelations" parameterType="CrmBusinessContractUserRelations" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_contract_user_relations
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractId != null">contract_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="relationType != null">relation_type,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contractId != null">#{contractId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="relationType != null">#{relationType},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateCrmBusinessContractUserRelations" parameterType="CrmBusinessContractUserRelations">
        update crm_business_contract_user_relations
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="relationType != null">relation_type = #{relationType},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmBusinessContractUserRelationsById" parameterType="Long">
        delete from crm_business_contract_user_relations where id = #{id}
    </delete>

    <delete id="deleteCrmBusinessContractUserRelationsByIds" parameterType="String">
        delete from crm_business_contract_user_relations where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>