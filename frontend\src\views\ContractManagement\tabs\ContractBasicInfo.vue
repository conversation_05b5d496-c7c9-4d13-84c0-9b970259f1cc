<template>
    <div class="contract-basic-info">
        <el-descriptions :column="2" border>
            <el-descriptions-item label="合同编号">{{ contract.contract_number }}</el-descriptions-item>
            <el-descriptions-item label="合同名称">{{ contract.contract_name }}</el-descriptions-item>
            <el-descriptions-item label="客户名称">{{ contract.customer_name }}</el-descriptions-item>
            <el-descriptions-item label="报价单号">{{ contract.quotation_number }}</el-descriptions-item>
            <el-descriptions-item label="关联商机">{{ contract.opportunity_name }}</el-descriptions-item>
            <el-descriptions-item label="合同金额">{{ contract.contract_amount }}</el-descriptions-item>
            <el-descriptions-item label="签订日期">{{ contract.order_date }}</el-descriptions-item>
            <el-descriptions-item label="开始日期">{{ contract.start_date }}</el-descriptions-item>
            <el-descriptions-item label="结束日期">{{ contract.end_date }}</el-descriptions-item>
            <el-descriptions-item label="客户签约人">{{ contract.customer_signatory }}</el-descriptions-item>
            <el-descriptions-item label="公司签约人">{{ contract.company_signatory }}</el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">{{ contract.remarks }}</el-descriptions-item>
        </el-descriptions>
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { ContractData } from '../types';

export default defineComponent({
    name: 'ContractBasicInfo',
    props: {
        contract: {
            type: Object as PropType<ContractData>,
            required: true
        }
    }
});
</script>

<style scoped>
.contract-basic-info {
    padding: 20px;
}
</style> 