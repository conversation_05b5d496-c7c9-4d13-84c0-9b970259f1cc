<template>
  <div class="unified-team-management">
    <!-- 头部工具栏 -->
    <div class="team-header">
      <div class="header-left">
        <h4>{{ title }}</h4>
        <span class="member-count" v-if="teamMembers.length > 0">
          ({{ teamMembers.length }}人)
        </span>
      </div>
      <div class="header-right">
        <!-- 视图切换器 -->
        <div class="view-switcher">
          <el-button 
            :type="currentView === 'table' ? 'primary' : 'default'" 
            @click="currentView = 'table'"
            class="switcher-btn"
          >
            <el-icon><Grid /></el-icon>
            列表
          </el-button>
          <el-button 
            :type="currentView === 'card' ? 'primary' : 'default'" 
            @click="currentView = 'card'"
            class="switcher-btn"
          >
            <el-icon><Postcard /></el-icon>
            网格
          </el-button>
        </div>
        
        <!-- 操作按钮 -->
        <el-button 
          v-if="canManageTeam" 
          type="primary" 
          @click="showAddDialog = true"
          class="add-member-btn"
        >
          <el-icon><Plus /></el-icon>
          添加成员
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="team-filters" v-if="showFilters">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索成员姓名或用户名"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select v-model="filterRole" placeholder="筛选角色" clearable @change="handleFilter">
            <el-option label="全部角色" value="" />
            <el-option label="负责人" value="owner" />
            <el-option label="管理员" value="admin" />
            <el-option label="成员" value="member" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="filterStatus" placeholder="筛选状态" clearable @change="handleFilter">
            <el-option label="全部状态" value="" />
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button @click="resetFilters">重置</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 表格视图 -->
    <div v-if="currentView === 'table'" class="table-view">
      <el-table 
        :data="filteredMembers" 
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" v-if="canManageTeam" />
        <el-table-column prop="nickName" label="姓名" width="120">
          <template #default="{ row }">
            <div class="member-info">
              <el-avatar :size="32" :src="row.avatar">
                {{ row.nickName?.charAt(0) }}
              </el-avatar>
              <span class="name">{{ row.nickName || row.userName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="userName" label="用户名" width="120" />
        <el-table-column prop="roleType" label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="getRoleTagType(row.roleType)" size="small">
              {{ getRoleLabel(row.roleType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="joinTime" label="加入时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.joinTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === '0' ? 'success' : 'danger'" size="small">
              {{ row.status === '0' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column v-if="canManageTeam" label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button link size="small" @click="editMember(row)">编辑</el-button>
            <el-button link size="small" style="color: #f56c6c;" @click="removeMember(row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 卡片视图 -->
    <div v-if="currentView === 'card'" class="card-view">
      <el-row :gutter="16">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="member in filteredMembers" :key="member.id">
          <div class="member-card" @click="viewMemberDetail(member)">
            <div class="card-header">
              <el-avatar :size="48" :src="member.avatar">
                {{ member.nickName?.charAt(0) }}
              </el-avatar>
              <div class="member-actions" v-if="canManageTeam">
                <el-dropdown @command="handleCardAction">
                  <el-button link size="small">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{action: 'edit', member}">编辑</el-dropdown-item>
                      <el-dropdown-item :command="{action: 'remove', member}">移除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
            <div class="card-body">
              <h5 class="member-name">{{ member.nickName || member.userName }}</h5>
              <p class="member-username">@{{ member.userName }}</p>
              <div class="member-role">
                <el-tag :type="getRoleTagType(member.roleType)" size="small">
                  {{ getRoleLabel(member.roleType) }}
                </el-tag>
              </div>
              <div class="member-status">
                <el-tag :type="member.status === '0' ? 'success' : 'danger'" size="small">
                  {{ member.status === '0' ? '正常' : '停用' }}
                </el-tag>
              </div>
            </div>
            <div class="card-footer">
              <span class="join-time">{{ formatDate(member.joinTime) }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 头像墙视图 -->
    <div v-if="currentView === 'avatar'" class="avatar-view">
      <div class="avatar-grid">
        <div 
          v-for="member in filteredMembers" 
          :key="member.id"
          class="avatar-item"
          @click="viewMemberDetail(member)"
        >
          <el-tooltip :content="`${member.nickName || member.userName} (${getRoleLabel(member.roleType)})`" placement="top">
            <div class="avatar-wrapper">
              <el-avatar :size="64" :src="member.avatar">
                {{ member.nickName?.charAt(0) }}
              </el-avatar>
              <div class="role-badge">
                <el-tag :type="getRoleTagType(member.roleType)" size="small">
                  {{ getRoleLabel(member.roleType) }}
                </el-tag>
              </div>
              <div class="status-indicator" :class="member.status === '0' ? 'online' : 'offline'"></div>
            </div>
          </el-tooltip>
          <div class="avatar-name">{{ member.nickName || member.userName }}</div>
        </div>
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedMembers.length > 0 && canManageTeam" class="batch-actions">
      <span>已选择 {{ selectedMembers.length }} 个成员</span>
      <el-button size="small" @click="batchRemove">批量移除</el-button>
      <el-button size="small" @click="clearSelection">取消选择</el-button>
    </div>

    <!-- 添加成员对话框 -->
    <el-dialog v-model="showAddDialog" title="添加团队成员" width="600px">
      <el-form ref="addFormRef" :model="addForm" :rules="addRules" label-width="80px">
        <el-form-item label="选择用户" prop="userIds">
          <el-select 
            v-model="addForm.userIds" 
            multiple 
            filterable 
            placeholder="请选择用户" 
            style="width: 100%;"
            @focus="loadAvailableUsers"
          >
            <el-option 
              v-for="user in availableUsers" 
              :key="user.userId" 
              :label="`${user.nickName}(${user.userName})`" 
              :value="user.userId" 
              :disabled="user.disabled" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="角色类型" prop="roleType">
          <el-radio-group v-model="addForm.roleType">
            <el-radio value="member">普通成员</el-radio>
            <el-radio value="admin">管理员</el-radio>
            <el-radio value="owner">负责人</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="handleAddMembers">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑成员对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑团队成员" width="500px">
      <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="80px">
        <el-form-item label="用户" prop="userName">
          <el-input v-model="editForm.userName" disabled />
        </el-form-item>
        <el-form-item label="角色类型" prop="roleType">
          <el-radio-group v-model="editForm.roleType">
            <el-radio value="member">普通成员</el-radio>
            <el-radio value="admin">管理员</el-radio>
            <el-radio value="owner">负责人</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="editForm.status">
            <el-radio value="0">正常</el-radio>
            <el-radio value="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="handleEditMember">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 成员详情抽屉 -->
    <el-drawer v-model="showDetailDrawer" title="成员详情" size="400px">
      <div v-if="selectedMember" class="member-detail">
        <div class="detail-header">
          <el-avatar :size="80" :src="selectedMember.avatar">
            {{ selectedMember.nickName?.charAt(0) }}
          </el-avatar>
          <h3>{{ selectedMember.nickName || selectedMember.userName }}</h3>
          <p>@{{ selectedMember.userName }}</p>
        </div>
        <div class="detail-info">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="角色">
              <el-tag :type="getRoleTagType(selectedMember.roleType)">
                {{ getRoleLabel(selectedMember.roleType) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="selectedMember.status === '0' ? 'success' : 'danger'">
                {{ selectedMember.status === '0' ? '正常' : '停用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="加入时间">
              {{ formatDate(selectedMember.joinTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="团队" v-if="selectedMember.teamName">
              {{ selectedMember.teamName }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store/modules/user'
import {
  Grid,
  MoreFilled,
  Plus,
  Postcard,
  Search
} from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, reactive, ref, watch } from 'vue'

const props = withDefaults(defineProps<Props>(), {
  teamId: 0,
  title: '团队成员管理',
  readonly: false,
  showFilters: true,
  height: 'auto'
})

// 定义接口
interface TeamMember {
  id?: number
  teamId: number
  userId: number
  userName: string
  nickName: string
  roleType: string
  joinTime: string
  status: string
  teamName?: string
  avatar?: string
}

interface UserOption {
  userId: number
  userName: string
  nickName: string
  deptName?: string
  disabled?: boolean
}

interface Props {
  bizId?: number
  bizType?: string
  teamId?: number
  title?: string
  readonly?: boolean
  showFilters?: boolean
  height?: string
}


const emit = defineEmits<{
  memberChange: [members: TeamMember[]]
  memberSelect: [member: TeamMember]
}>()

// 响应式数据
const userStore = useUserStore()
const loading = ref(false)
const currentView = ref('table')
const teamMembers = ref<TeamMember[]>([])
const availableUsers = ref<UserOption[]>([])
const selectedMembers = ref<TeamMember[]>([])
const selectedMember = ref<TeamMember | null>(null)

// 搜索和筛选
const searchKeyword = ref('')
const filterRole = ref('')
const filterStatus = ref('')

// 对话框状态
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const showDetailDrawer = ref(false)

// 表单引用
const addFormRef = ref<FormInstance>()
const editFormRef = ref<FormInstance>()

// 表单数据
const addForm = reactive({
  userIds: [] as number[],
  roleType: 'member'
})

const editForm = reactive({
  id: 0,
  teamId: 0,
  userId: 0,
  userName: '',
  nickName: '',
  roleType: 'member',
  status: '0'
})

// 表单验证规则
const addRules: FormRules = {
  userIds: [{ required: true, message: '请选择用户', trigger: 'change' }],
  roleType: [{ required: true, message: '请选择角色类型', trigger: 'change' }]
}

const editRules: FormRules = {
  roleType: [{ required: true, message: '请选择角色类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 计算属性
const isSuperAdmin = computed(() => {
  return userStore.roles.includes('super_admin')
})

const canManageTeam = computed(() => {
  // 临时的修改：始终返回true以显示管理按钮，后续需要后端进行权限配置
  return true
  // return !props.readonly && (isSuperAdmin.value || userStore.roles.includes('team_admin'))
})

const filteredMembers = computed(() => {
  let result = teamMembers.value

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(member =>
      member.nickName?.toLowerCase().includes(keyword) ||
      member.userName?.toLowerCase().includes(keyword)
    )
  }

  // 角色过滤
  if (filterRole.value) {
    result = result.filter(member => member.roleType === filterRole.value)
  }

  // 状态过滤
  if (filterStatus.value) {
    result = result.filter(member => member.status === filterStatus.value)
  }

  return result
})

// 方法
const loadTeamMembers = async () => {
  loading.value = true
  try {
    let response
    if (props.teamId) {
      // 根据团队ID加载成员
      response = await getTeamMembersByTeamId(props.teamId)
    } else if (props.bizId && props.bizType) {
      // 根据业务对象加载关联的团队成员
      response = await getTeamMembersByBiz(props.bizId, props.bizType)
    } else {
      teamMembers.value = []
      return
    }

    teamMembers.value = response.data?.rows || response.data || []
    emit('memberChange', teamMembers.value)
  } catch (error) {
    console.error('加载团队成员失败:', error)
    ElMessage.error('加载团队成员失败')
  } finally {
    loading.value = false
  }
}

const loadAvailableUsers = async () => {
  try {
    const response: any = await getAvailableUsers()
    availableUsers.value = (response.rows || []).map((user: any) => ({
      userId: user.userId,
      userName: user.userName,
      nickName: user.nickName,
      deptName: user.dept?.deptName,
      disabled: teamMembers.value.some(member => member.userId === user.userId)
    }))
  } catch (error) {
    console.error('加载用户列表失败:', error)
  }
}

const handleAddMembers = async () => {
  if (!addFormRef.value) return;

  await addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        console.log('添加成员: valid', JSON.stringify({ teamId: props.teamId, bizId: props.bizId, bizType: props.bizType }, null, 2) )

        if (props.teamId) {
          await batchAddTeamMembers(props.teamId, addForm.userIds, addForm.roleType);
        } else if (props.bizId && props.bizType) {
          const teamResponse = await createTeamForBiz(props.bizId, props.bizType);
          console.log('添加成员: createTeamForBiz', JSON.stringify(teamResponse, null, 2) )
          const newTeamId = teamResponse.data?.teamId;
          if (newTeamId) {
            await batchAddTeamMembers(newTeamId, addForm.userIds, addForm.roleType);
          } else {
            ElMessage.error('创建团队失败，无法添加成员');
            return;
          }
        } else {
          ElMessage.error('缺少团队信息，无法添加成员');
          return;
        }

        ElMessage.success('添加成员成功');
        showAddDialog.value = false;
        resetAddForm();
        await loadTeamMembers();
      } catch (error) {
        console.error('添加成员失败:', error);
        ElMessage.error('添加成员失败');
      }
    }
  });
};

const editMember = (member: TeamMember) => {
  editForm.id = member.id || 0
  editForm.teamId = member.teamId
  editForm.userId = member.userId
  editForm.userName = member.userName
  editForm.nickName = member.nickName
  editForm.roleType = member.roleType
  editForm.status = member.status
  showEditDialog.value = true
}

const handleEditMember = async () => {
  if (!editFormRef.value) return
  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await updateTeamMember(editForm)
        ElMessage.success('更新成员成功')
        showEditDialog.value = false
        await loadTeamMembers()
      } catch (error) {
        console.error('更新成员失败:', error)
        ElMessage.error('更新成员失败')
      }
    }
  })
}

const removeMember = async (member: TeamMember) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除团队成员 "${member.nickName}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    await removeTeamMember(member.teamId, member.userId)
    ElMessage.success('移除成员成功')
    await loadTeamMembers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除成员失败:', error)
      ElMessage.error('移除成员失败')
    }
  }
}

const batchRemove = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要移除选中的 ${selectedMembers.value.length} 个成员吗？`,
      '确认批量移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const userIds = selectedMembers.value.map(member => member.userId)
    await batchRemoveTeamMembers(userIds)
    ElMessage.success('批量移除成功')
    clearSelection()
    await loadTeamMembers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量移除失败:', error)
      ElMessage.error('批量移除失败')
    }
  }
}

const viewMemberDetail = (member: TeamMember) => {
  selectedMember.value = member
  showDetailDrawer.value = true
  emit('memberSelect', member)
}

const handleCardAction = ({ action, member }: { action: string, member: TeamMember }) => {
  if (action === 'edit') {
    editMember(member)
  } else if (action === 'remove') {
    removeMember(member)
  }
}

const handleSelectionChange = (selection: TeamMember[]) => {
  selectedMembers.value = selection
}

const clearSelection = () => {
  selectedMembers.value = []
}

const handleSearch = () => {
  // 搜索逻辑在计算属性中处理
}

const handleFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const resetFilters = () => {
  searchKeyword.value = ''
  filterRole.value = ''
  filterStatus.value = ''
}

const resetAddForm = () => {
  addForm.userIds = []
  addForm.roleType = 'member'
  addFormRef.value?.resetFields()
}

// 辅助方法
const getRoleTagType = (roleType: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    owner: 'danger',
    admin: 'warning',
    member: 'info'
  }
  return typeMap[roleType] || 'info'
}

const getRoleLabel = (roleType: string) => {
  const labelMap: Record<string, string> = {
    owner: '负责人',
    admin: '管理员',
    member: '成员'
  }
  return labelMap[roleType] || '未知'
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 导入API方法
import {
  batchAddTeamMembers as apiBatchAddTeamMembers,
  batchRemoveTeamMembers as apiBatchRemoveTeamMembers,
  createTeamForBiz as apiCreateTeamForBiz,
  getAvailableUsers as apiGetAvailableUsers,
  getTeamMembersByBiz as apiGetTeamMembersByBiz,
  getTeamMembersByTeamId as apiGetTeamMembersByTeamId,
  removeTeamMember as apiRemoveTeamMember,
  updateTeamMember as apiUpdateTeamMember
} from '@/api/team-relation'

// API 方法
const getTeamMembersByTeamId = async (teamId: number) => {
  return await apiGetTeamMembersByTeamId(teamId)
}

const getTeamMembersByBiz = async (bizId: number, bizType: string) => {
  return await apiGetTeamMembersByBiz(bizId, bizType)
}

const getAvailableUsers = async () => {
  return await apiGetAvailableUsers()
}

const batchAddTeamMembers = async (teamId: number, userIds: number[], roleType: string) => {
  return await apiBatchAddTeamMembers(teamId, userIds, roleType)
}

const updateTeamMember = async (member: any) => {
  return await apiUpdateTeamMember(member)
}

const removeTeamMember = async (teamId: number, userId: number) => {
  return await apiRemoveTeamMember(teamId, userId)
}

const batchRemoveTeamMembers = async (userIds: number[]) => {
  return await apiBatchRemoveTeamMembers(userIds)
}

const createTeamForBiz = async (bizId: number, bizType: string) => {
  return await apiCreateTeamForBiz(bizId, bizType)
}

// 生命周期
onMounted(() => {
  loadTeamMembers()
})

// 监听属性变化
watch(() => [props.teamId, props.bizId, props.bizType], () => {
  loadTeamMembers()
}, { deep: true })

// 暴露方法给父组件
defineExpose({
  loadTeamMembers,
  clearSelection,
  resetFilters
})
</script>

<style scoped>
.unified-team-management {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

/* 头部样式 */
.team-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-left h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.member-count {
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.view-switcher {
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  height: 36px;
  border: 1px solid #e4e7ed;
}

.view-switcher .el-button,
.view-switcher .switcher-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  border-radius: 0;
  border: none;
  padding: 0 20px;
  margin: 0;
  transition: all 0.3s ease;
  height: 36px;
  font-size: 14px;
  min-width: 80px;
}

.view-switcher .el-button--default {
  background-color: #f5f7fa;
  color: #606266;
}

.view-switcher .el-button--primary {
  background-color: #409eff;
  color: white;
}

.add-member-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0 16px;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
  transition: all 0.3s ease;
  height: 36px;
  font-size: 14px;
  min-width: 100px;
}

.add-member-btn:hover {
  background-color: #66b1ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
}

/* 筛选器样式 */
.team-filters {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

/* 表格视图样式 */
.table-view {
  padding: 20px;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.member-info .name {
  font-weight: 500;
  color: #303133;
}

/* 卡片视图样式 */
.card-view {
  padding: 20px;
}

.member-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.member-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.member-actions {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.member-card:hover .member-actions {
  opacity: 1;
}

.card-body {
  text-align: center;
}

.member-name {
  margin: 8px 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.member-username {
  margin: 0 0 8px 0;
  color: #909399;
  font-size: 14px;
}

.member-role,
.member-status {
  margin: 8px 0;
}

.card-footer {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

.join-time {
  color: #909399;
  font-size: 12px;
}

/* 头像墙视图样式 */
.avatar-view {
  padding: 20px;
}

.avatar-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 20px;
  justify-items: center;
}

.avatar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.avatar-item:hover {
  transform: scale(1.05);
}

.avatar-wrapper {
  position: relative;
  margin-bottom: 8px;
}

.role-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 1;
}

.status-indicator {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #fff;
}

.status-indicator.online {
  background-color: #67c23a;
}

.status-indicator.offline {
  background-color: #f56c6c;
}

.avatar-name {
  font-size: 14px;
  color: #303133;
  text-align: center;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 批量操作栏样式 */
.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 成员详情样式 */
.member-detail {
  padding: 20px;
}

.detail-header {
  text-align: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.detail-header h3 {
  margin: 12px 0 4px 0;
  color: #303133;
}

.detail-header p {
  margin: 0;
  color: #909399;
}

.detail-info {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .team-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-right {
    justify-content: space-between;
  }

  .view-switcher .el-radio-button__inner {
    padding: 6px 8px;
    font-size: 12px;
  }

  .team-filters .el-row {
    flex-direction: column;
  }

  .team-filters .el-col {
    margin-bottom: 8px;
  }

  .avatar-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 16px;
  }

  .batch-actions {
    left: 10px;
    right: 10px;
    transform: none;
    flex-direction: column;
    gap: 8px;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}
</style>
