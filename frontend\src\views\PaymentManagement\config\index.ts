import { TableOperationsConfig } from '@/components/TableOperations/types';
import type { ButtonType } from 'element-plus';

// 表格列配置
export const tableColumns = [
    { type: 'selection', width: 55 },
    { prop: 'payment_number', label: '回款编号', width: 180, sortable: true },
    { prop: 'contract_number', label: '合同编号', width: 180, sortable: true },
    { prop: 'customer_name', label: '客户名称', width: 180, sortable: true },
    { prop: 'payment_amount', label: '回款金额', width: 180, sortable: true },
    { prop: 'payment_date', label: '回款日期', width: 180, sortable: true },
    { prop: 'payment_method', label: '回款方式', width: 180, sortable: true },
    { prop: 'payment_status', label: '回款状态', width: 180, sortable: true },
    { prop: 'payment_type', label: '回款类型', width: 180, sortable: true },
    { prop: 'bank_account', label: '银行账号', width: 180, sortable: true },
    { prop: 'bank_name', label: '开户银行', width: 180, sortable: true },
    { prop: 'invoice_status', label: '开票状态', width: 180, sortable: true },
    { prop: 'invoice_number', label: '发票编号', width: 180, sortable: true },
    { prop: 'invoice_amount', label: '发票金额', width: 180, sortable: true },
    { prop: 'remarks', label: '备注', width: 180, sortable: true }
];

// 表格操作配置
export const tableOperations: TableOperationsConfig = {
    width: 220,
    fixed: 'right' as const,
    buttons: [
        {
            label: '分配',
            type: 'primary' as ButtonType,
            link: true,
            handler: 'handleAssign',
            icon: 'Share',
            show: true
        },
        {
            label: '审核',
            type: 'success' as ButtonType,
            link: true,
            handler: 'handleApprove',
            icon: 'Check',
            show: true
        },
        {
            label: '删除',
            type: 'danger' as ButtonType,
            link: true,
            handler: 'handleDelete',
            icon: 'Delete',
            show: true
        }
    ]
};

// 新建回款表单配置
export const newPaymentFormConfig = {
    layout: {
        labelPosition: 'top',
        size: 'default',
        columns: 2
    },
    fields: [
        { label: '回款编号', field: 'payment_number', component: 'el-input', colSpan: 12 },
        { label: '合同编号', field: 'contract_number', component: 'el-input', colSpan: 12 },
        { label: '客户名称', field: 'customer_name', component: 'el-input', colSpan: 12 },
        { 
            label: '回款金额', 
            field: 'payment_amount', 
            component: 'el-input-number', 
            colSpan: 12,
            props: {
                min: 0,
                precision: 2,
                step: 1000,
                'controls-position': 'right'
            }
        },
        { 
            label: '回款日期', 
            field: 'payment_date', 
            component: 'el-date-picker', 
            colSpan: 12,
            props: {
                type: 'date',
                placeholder: '选择日期'
            }
        },
        { 
            label: '回款方式', 
            field: 'payment_method', 
            component: 'el-select', 
            colSpan: 12,
            props: {
                options: [
                    { label: '银行转账', value: '银行转账' },
                    { label: '现金', value: '现金' },
                    { label: '支票', value: '支票' },
                    { label: '其他', value: '其他' }
                ]
            }
        },
        { 
            label: '回款状态', 
            field: 'payment_status', 
            component: 'el-select', 
            colSpan: 12,
            props: {
                options: [
                    { label: '待收款', value: '待收款' },
                    { label: '已收款', value: '已收款' },
                    { label: '部分收款', value: '部分收款' },
                    { label: '已退款', value: '已退款' }
                ]
            }
        },
        { 
            label: '回款类型', 
            field: 'payment_type', 
            component: 'el-select', 
            colSpan: 12,
            props: {
                options: [
                    { label: '预付款', value: '预付款' },
                    { label: '进度款', value: '进度款' },
                    { label: '尾款', value: '尾款' },
                    { label: '质保金', value: '质保金' }
                ]
            }
        },
        { label: '银行账号', field: 'bank_account', component: 'el-input', colSpan: 12 },
        { label: '开户银行', field: 'bank_name', component: 'el-input', colSpan: 12 },
        { 
            label: '开票状态', 
            field: 'invoice_status', 
            component: 'el-select', 
            colSpan: 12,
            props: {
                options: [
                    { label: '未开票', value: '未开票' },
                    { label: '已开票', value: '已开票' },
                    { label: '部分开票', value: '部分开票' }
                ]
            }
        },
        { label: '发票编号', field: 'invoice_number', component: 'el-input', colSpan: 12 },
        { 
            label: '发票金额', 
            field: 'invoice_amount', 
            component: 'el-input-number', 
            colSpan: 12,
            props: {
                min: 0,
                precision: 2,
                step: 1000,
                'controls-position': 'right'
            }
        },
        { 
            label: '备注', 
            field: 'remarks', 
            component: 'el-input', 
            colSpan: 24,
            props: {
                type: 'textarea',
                rows: 4
            }
        }
    ]
};

// 抽屉配置
export const drawerConfig = {
    headerFields: [
        { label: '回款编号', field: 'payment_number' },
        { label: '合同编号', field: 'contract_number' },
        { label: '回款金额', field: 'payment_amount' },
        { label: '创建时间', field: 'createTime' }
    ],
    actions: [
        {
            label: '编辑',
            type: 'primary',
            icon: 'Edit',
            handler: (data: any) => {
                console.log('编辑', data);
            }
        },
        {
            label: '审核',
            type: 'success',
            icon: 'Check',
            handler: (data: any) => {
                console.log('审核', data);
            }
        },
        {
            label: '打印',
            icon: 'Printer',
            handler: (data: any) => {
                console.log('打印', data);
            }
        },
        {
            label: '删除',
            type: 'danger',
            icon: 'Delete',
            handler: (data: any) => {
                console.log('删除', data);
            }
        }
    ],
    menuItems: [
        {
            key: 'details',
            label: '详细资料',
            icon: 'Document',
            component: 'PaymentDetailsTab'
        },
        {
            key: 'activity',
            label: '活动记录',
            icon: 'Timer',
            component: 'PaymentActivityTab'
        },
        {
            key: 'attachments',
            label: '附件',
            icon: 'Folder',
            component: 'PaymentAttachmentsTab',
            badge: true
        },
        {
            key: 'operations',
            label: '操作记录',
            icon: 'List',
            component: 'PaymentOperationsTab'
        }
    ]
};

// 导航配置
export const navConfig = {
    title: '回款管理',
    menuItems: [
        {
            key: 'payments',
            label: '回款',
            icon: 'Money'
        },
        {
            key: 'followup',
            label: '回款记录',
            icon: 'ChatRound'
        }
    ]
}; 