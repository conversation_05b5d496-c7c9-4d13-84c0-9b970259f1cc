<template>
  <div class="test-team-assignment">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>团队分配功能测试页面</span>
          <el-button type="primary" @click="runAllTests">运行所有测试</el-button>
        </div>
      </template>

      <!-- 测试结果显示 -->
      <div class="test-results">
        <el-alert
          v-for="(result, index) in testResults"
          :key="index"
          :title="result.title"
          :type="result.type"
          :description="result.description"
          show-icon
          :closable="false"
          style="margin-bottom: 10px;"
        />
      </div>

      <!-- 手动测试区域 -->
      <el-divider>手动测试</el-divider>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="业务对象ID">
          <el-input v-model.number="testForm.bizId" type="number" placeholder="输入联系人ID" />
        </el-form-item>
        <el-form-item label="业务类型">
          <el-select v-model="testForm.bizType">
            <el-option label="联系人" value="CONTACT" />
            <el-option label="商机" value="OPPORTUNITY" />
            <el-option label="客户" value="CUSTOMER" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="testGetTeamByBiz" :loading="loading">
            查询团队分配
          </el-button>
          <el-button type="success" @click="testAssignTeam" :loading="loading">
            分配团队
          </el-button>
          <el-button type="danger" @click="testUnassignTeam" :loading="loading">
            取消分配
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 查询结果显示 -->
      <el-divider>查询结果</el-divider>
      <div v-if="queryResult" class="query-result">
        <el-descriptions title="团队分配信息" :column="2" border>
          <el-descriptions-item label="团队ID">{{ queryResult.teamId }}</el-descriptions-item>
          <el-descriptions-item label="团队名称">{{ queryResult.teamName || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ queryResult.leaderName || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="分配时间">{{ formatDate(queryResult.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="业务类型">{{ queryResult.relationType }}</el-descriptions-item>
          <el-descriptions-item label="业务ID">{{ queryResult.relationId }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div v-else class="no-result">
        <el-empty description="暂无团队分配信息" />
      </div>

      <!-- API 响应原始数据 -->
      <el-divider>API 响应数据</el-divider>
      <el-input
        v-model="rawResponse"
        type="textarea"
        :rows="8"
        placeholder="API 响应的原始数据将显示在这里"
        readonly
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getTeamByBiz, 
  assignTeamToBiz, 
  unassignTeamFromBiz 
} from '@/api/team-relation'

// 测试结果接口
interface TestResult {
  title: string
  type: 'success' | 'error' | 'warning' | 'info'
  description: string
}

// 响应式数据
const testResults = ref<TestResult[]>([])
const loading = ref(false)
const queryResult = ref<any>(null)
const rawResponse = ref('')

const testForm = reactive({
  bizId: 1,
  bizType: 'CONTACT'
})

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '未知'
  return new Date(dateStr).toLocaleString()
}

// 添加测试结果
const addTestResult = (title: string, type: TestResult['type'], description: string) => {
  testResults.value.push({ title, type, description })
}

// 清空测试结果
const clearTestResults = () => {
  testResults.value = []
}

// 测试查询团队分配
const testGetTeamByBiz = async () => {
  loading.value = true
  try {
    const response = await getTeamByBiz(testForm.bizId, testForm.bizType)
    
    // 显示原始响应
    rawResponse.value = JSON.stringify(response, null, 2)
    
    // 检查响应结构
    if (response.data) {
      queryResult.value = response.data
      
      // 验证数据结构
      const hasTeamId = response.data.teamId !== undefined
      const hasTeamName = response.data.teamName !== undefined
      const hasLeaderName = response.data.leaderName !== undefined
      
      if (hasTeamId && hasTeamName && hasLeaderName) {
        addTestResult(
          '✅ 查询团队分配成功',
          'success',
          `找到团队分配：${response.data.teamName} (负责人: ${response.data.leaderName})`
        )
      } else {
        addTestResult(
          '⚠️ 数据结构不完整',
          'warning',
          `缺少字段 - teamId: ${hasTeamId}, teamName: ${hasTeamName}, leaderName: ${hasLeaderName}`
        )
      }
    } else {
      queryResult.value = null
      addTestResult(
        'ℹ️ 无团队分配',
        'info',
        `业务对象 ${testForm.bizType}:${testForm.bizId} 暂未分配团队`
      )
    }
  } catch (error: any) {
    console.error('查询失败:', error)
    rawResponse.value = JSON.stringify(error.response || error, null, 2)
    addTestResult(
      '❌ 查询失败',
      'error',
      `错误信息: ${error.message || '未知错误'}`
    )
    queryResult.value = null
  } finally {
    loading.value = false
  }
}

// 测试分配团队
const testAssignTeam = async () => {
  loading.value = true
  try {
    // 使用固定的团队ID进行测试
    const testTeamId = 1
    await assignTeamToBiz(testTeamId, testForm.bizId, testForm.bizType)
    
    addTestResult(
      '✅ 分配团队成功',
      'success',
      `成功将 ${testForm.bizType}:${testForm.bizId} 分配给团队 ${testTeamId}`
    )
    
    // 重新查询验证
    await testGetTeamByBiz()
  } catch (error: any) {
    console.error('分配失败:', error)
    addTestResult(
      '❌ 分配团队失败',
      'error',
      `错误信息: ${error.message || '未知错误'}`
    )
  } finally {
    loading.value = false
  }
}

// 测试取消分配
const testUnassignTeam = async () => {
  loading.value = true
  try {
    await unassignTeamFromBiz(testForm.bizId, testForm.bizType)
    
    addTestResult(
      '✅ 取消分配成功',
      'success',
      `成功取消 ${testForm.bizType}:${testForm.bizId} 的团队分配`
    )
    
    // 重新查询验证
    await testGetTeamByBiz()
  } catch (error: any) {
    console.error('取消分配失败:', error)
    addTestResult(
      '❌ 取消分配失败',
      'error',
      `错误信息: ${error.message || '未知错误'}`
    )
  } finally {
    loading.value = false
  }
}

// 运行所有测试
const runAllTests = async () => {
  clearTestResults()
  ElMessage.info('开始运行自动化测试...')
  
  // 测试1: 查询不存在的分配
  testForm.bizId = 999
  testForm.bizType = 'CONTACT'
  await testGetTeamByBiz()
  
  // 测试2: 分配团队
  testForm.bizId = 1
  await testAssignTeam()
  
  // 测试3: 查询已分配的团队
  await testGetTeamByBiz()
  
  // 测试4: 取消分配
  await testUnassignTeam()
  
  ElMessage.success('自动化测试完成！')
}
</script>

<style scoped>
.test-team-assignment {
  padding: 20px;
}

.test-card {
  max-width: 1000px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-results {
  margin-bottom: 20px;
}

.query-result {
  margin-bottom: 20px;
}

.no-result {
  text-align: center;
  margin-bottom: 20px;
}
</style>
