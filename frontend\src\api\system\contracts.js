import request from '@/utils/request'

// 查询合同列表
export function listContracts(query) {
  return request({
    url: '/system/contracts/list',
    method: 'get',
    params: query
  })
}

// 查询合同详细
export function getContracts(id) {
  return request({
    url: '/system/contracts/' + id,
    method: 'get'
  })
}

// 新增合同
export function addContracts(data) {
  return request({
    url: '/system/contracts',
    method: 'post',
    data: data
  })
}

// 修改合同
export function updateContracts(data) {
  return request({
    url: '/system/contracts',
    method: 'put',
    data: data
  })
}

// 删除合同
export function delContracts(id) {
  return request({
    url: '/system/contracts/' + id,
    method: 'delete'
  })
}
