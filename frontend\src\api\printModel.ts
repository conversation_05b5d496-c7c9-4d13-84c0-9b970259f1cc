import { ApiResponse, SimpleApiResponse } from '@/types/apiTypes';
import request from '@/utils/request';

// 定义请求参数的类型
interface GetPrintModelListParams {
    pageNum?: number; // 页码
    pageSize?: number; // 每页数量
    type?: string; // 材料类型
    [key: string]: any; // 其他可能的查询参数
}

// 定义返回数据的类型
interface PrintModel {
    id: string; // 产品ID
    name: string; // 产品名称
    type: string; // 产品类型
    price: number; // 价格
    techSpecs: string; // 技术规格（JSON 字符串）
    imageUrl: string; // 图片URL（JSON 字符串）
    advantages?: string; // 优点
    disadvantages?: string; // 缺点
    applicationAreas?: string; // 应用领域
}

interface ProductType {
    id: string; // 类型ID
    name: string; // 类型名称
}

/**
 * 获取打印模型列表
 * @param query 查询参数
 * @returns 打印模型列表
 */
export function getPrintModelList(query: GetPrintModelListParams): Promise<ApiResponse<PrintModel>> {
    return request({
        url: '/crm/product/list',
        method: 'get',
        params: query,
    });
}

/**
 * 获取产品类型列表
 * @returns 产品类型列表
 */
export function getProductTypes(): Promise<SimpleApiResponse<string[]>> {
    return request({
        url: '/crm/product/types',
        method: 'get',
    });
}