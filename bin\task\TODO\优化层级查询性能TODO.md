# 层级查询性能优化待办事项 (TODO)

## 1. 当前状况

目前，系统中获取一个用户的所有下属（包括多层级）的功能，是通过在Java代码中进行循环迭代查询实现的。

- **实现方式**: `CrmUserHierarchyServiceImpl.getAllSubordinateIds()` 方法通过循环调用`CrmUserHierarchyMapper.selectSubordinateIdsBySuperiorIds()`，逐层获取下属ID。
- **采用原因**: 此方案是为了解决旧版本MySQL（8.0以下）不支持`WITH RECURSIVE`递归查询的问题，保证了系统的数据库兼容性。

## 2. 性能瓶颈分析

虽然当前的Java迭代方案解决了兼容性问题，但存在潜在的性能瓶颈：

- **多次数据库交互**: 每深入查询一个层级，就需要向数据库发起一次SQL查询。如果组织层级较深，会导致大量的网络往返和数据库请求，在高并发场景下可能成为性能瓶-颈。
- **N+1查询问题**: 本质上，这是一种典型的“N+1查询”问题，只不过是垂直方向的。

## 3. 未来优化方案

为了在未来提升系统性能和可扩展性，可以考虑以下几种优化方案：

### 方案一：升级数据库版本 (长期推荐)

- **描述**: 将MySQL数据库升级到 **8.0或更高版本**。这是最直接、最根本的解决方案。
- **优点**:
    - 可以直接使用`WITH RECURSIVE`公用表表达式（CTE），将复杂的层级查询逻辑交由数据库一次性高效完成。
    - SQL语句更简洁，维护成本低。
    - 性能最优，减少了应用服务器和数据库之间的多次通信。
- **缺点**:
    - 需要进行数据库升级，可能涉及数据迁移和兼容性测试，项目成本较高。

### 方案二：物化路径 (Materialized Path)

- **描述**: 在`crm_user_hierarchy`表中增加一个`path`字段（字符串类型），用于存储从根节点到当前节点的完整路径，例如`1/5/12/`。
- **优点**:
    - **查询高效**: 获取一个节点的所有下属，只需一次`LIKE '...%'`查询，例如 `SELECT * FROM crm_user_hierarchy WHERE path LIKE '/1/5/%'`。
    - 兼容所有数据库版本。
- **缺点**:
    - **维护成本高**: 当移动一个节点（即更换其上级）时，需要更新该节点及其所有子孙节点的`path`字段，写操作相对复杂和耗时。
    - 路径字段的长度需要预估，可能存在限制。

### 方案三：闭包表 (Closure Table)

- **描述**: 引入一个独立的关联表（如`crm_user_hierarchy_paths`），存储所有节点之间的关系，包括间接关系。例如，如果C是B的下属，B是A的下属，表中会同时存储(A,C)、(A,B)、(B,C)的记录。
- **优点**:
    - **读取性能极高**: 获取所有上级或下级都非常快，只需一次简单的查询。
- **缺点**:
    - **空间占用大**: 存储了大量的冗余关系数据。
    - **写操作复杂**: 每次新增或移动节点，都需要对路径表进行大量更新。

## 4. 行动计划

- **短期 (当前)**:
    - [x] **维持现状**: 当前的Java迭代方案作为兼容性保底方案，功能上是可行的。
    - [ ] **性能监控**: 在生产环境中，对`getAllSubordinateIds`方法的性能进行监控，评估其在实际负载下的响应时间。

- **长期 (未来版本)**:
    - [ ] **评估数据库升级**: 优先评估将生产环境数据库升级到MySQL 8.0+的可行性。这是最佳实践。
    - [ ] **备选方案调研**: 如果数据库升级在短期内不可行，则详细调研并测试“物化路径”方案，作为主要的备选优化策略。

---
*此文档创建于 2025-07-09，用于记录层级查询的未来优化方向。*
