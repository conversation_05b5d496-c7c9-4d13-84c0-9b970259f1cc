package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CrmBusinessMapper;
import com.ruoyi.common.domain.entity.CrmBusiness;
import com.ruoyi.system.service.ICrmBusinessService;

/**
 * 业务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class CrmBusinessServiceImpl implements ICrmBusinessService 
{
    @Autowired
    private CrmBusinessMapper crmBusinessMapper;

    /**
     * 查询业务
     * 
     * @param id 业务主键
     * @return 业务
     */
    @Override
    public CrmBusiness selectCrmBusinessById(Long id)
    {
        return crmBusinessMapper.selectCrmBusinessById(id);
    }

    /**
     * 查询业务列表
     * 
     * @param crmBusiness 业务
     * @return 业务
     */
    @Override
    public List<CrmBusiness> selectCrmBusinessList(CrmBusiness crmBusiness)
    {
        return crmBusinessMapper.selectCrmBusinessList(crmBusiness);
    }

    /**
     * 新增业务
     * 
     * @param crmBusiness 业务
     * @return 结果
     */
    @Override
    public int insertCrmBusiness(CrmBusiness crmBusiness)
    {
        return crmBusinessMapper.insertCrmBusiness(crmBusiness);
    }

    /**
     * 修改业务
     * 
     * @param crmBusiness 业务
     * @return 结果
     */
    @Override
    public int updateCrmBusiness(CrmBusiness crmBusiness)
    {
        return crmBusinessMapper.updateCrmBusiness(crmBusiness);
    }

    /**
     * 批量删除业务
     * 
     * @param ids 需要删除的业务主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessByIds(Long[] ids)
    {
        return crmBusinessMapper.deleteCrmBusinessByIds(ids);
    }

    /**
     * 删除业务信息
     * 
     * @param id 业务主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessById(Long id)
    {
        return crmBusinessMapper.deleteCrmBusinessById(id);
    }
}
