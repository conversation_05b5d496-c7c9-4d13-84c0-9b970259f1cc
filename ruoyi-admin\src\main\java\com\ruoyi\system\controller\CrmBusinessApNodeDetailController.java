package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.domain.entity.CrmBusinessApNodeDetail;
import com.ruoyi.system.service.ICrmBusinessApNodeDetailService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 审批节点详情Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Api(tags = "审批节点详情管理")
@RestController
@RequestMapping("/system/detail")
public class CrmBusinessApNodeDetailController extends BaseController {
    @Autowired
    private ICrmBusinessApNodeDetailService crmBusinessApNodeDetailService;

    /**
     * 查询审批节点详情列表
     */
    @ApiOperation("查询审批节点详情列表")
    @PreAuthorize("@ss.hasPermi('system:detail:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusinessApNodeDetail crmBusinessApNodeDetail) {
        startPage();
        List<CrmBusinessApNodeDetail> list = crmBusinessApNodeDetailService
                .selectCrmBusinessApNodeDetailList(crmBusinessApNodeDetail);
        return getDataTable(list);
    }

    /**
     * 导出审批节点详情列表
     */
    @ApiOperation("导出审批节点详情列表")
    @PreAuthorize("@ss.hasPermi('system:detail:export')")
    @Log(title = "审批节点详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmBusinessApNodeDetail crmBusinessApNodeDetail) {
        List<CrmBusinessApNodeDetail> list = crmBusinessApNodeDetailService
                .selectCrmBusinessApNodeDetailList(crmBusinessApNodeDetail);
        ExcelUtil<CrmBusinessApNodeDetail> util = new ExcelUtil<CrmBusinessApNodeDetail>(CrmBusinessApNodeDetail.class);
        util.exportExcel(response, list, "审批节点详情数据");
    }

    /**
     * 获取审批节点详情详细信息
     */
    @ApiOperation("获取审批节点详情详细信息")
    @PreAuthorize("@ss.hasPermi('system:detail:query')")
    @GetMapping(value = "/{detailId}")
    public AjaxResult getInfo(@ApiParam("详情ID") @PathVariable("detailId") Long detailId) {
        return success(crmBusinessApNodeDetailService.selectCrmBusinessApNodeDetailByDetailId(detailId));
    }

    /**
     * 新增审批节点详情
     */
    @ApiOperation("新增审批节点详情")
    @PreAuthorize("@ss.hasPermi('system:detail:add')")
    @Log(title = "审批节点详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("审批节点详情") @RequestBody CrmBusinessApNodeDetail crmBusinessApNodeDetail) {
        return toAjax(crmBusinessApNodeDetailService.insertCrmBusinessApNodeDetail(crmBusinessApNodeDetail));
    }

    /**
     * 修改审批节点详情
     */
    @ApiOperation("修改审批节点详情")
    @PreAuthorize("@ss.hasPermi('system:detail:edit')")
    @Log(title = "审批节点详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("审批节点详情") @RequestBody CrmBusinessApNodeDetail crmBusinessApNodeDetail) {
        return toAjax(crmBusinessApNodeDetailService.updateCrmBusinessApNodeDetail(crmBusinessApNodeDetail));
    }

    /**
     * 删除审批节点详情
     */
    @ApiOperation("删除审批节点详情")
    @PreAuthorize("@ss.hasPermi('system:detail:remove')")
    @Log(title = "审批节点详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{detailIds}")
    public AjaxResult remove(@ApiParam("详情ID数组") @PathVariable Long[] detailIds) {
        return toAjax(crmBusinessApNodeDetailService.deleteCrmBusinessApNodeDetailByDetailIds(detailIds));
    }
}
