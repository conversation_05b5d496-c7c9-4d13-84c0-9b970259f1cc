<template>
  <div class="quote-table-section">
    <el-table :data="tableData" class="quote-table" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55">
        <template #header>
          <span>全选</span>
        </template>
      </el-table-column>
      <el-table-column prop="index" label="序号" width="80"></el-table-column>
      <el-table-column label="图纸" width="120">
        <template #default="scope">
          <div class="model-preview" @click="handleModelPreview(scope.row)">
            <el-image 
              :src="scope.row.thumbnail"
              class="model-thumbnail" 
              fit="cover"
              :preview-src-list="[scope.row.originalUrl]"
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
            <div class="preview-overlay">
              <el-icon><View /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="模型信息" min-width="250">
        <template #default="scope">
          <div class="model-info">
            <h4>{{ scope.row.modelInfo.name }}</h4>
            <div class="model-details">
              <p>
                <span class="info-label">单位：</span>
                <el-radio-group v-model="scope.row.modelInfo.unit" size="small">
                  <el-radio value="mm">mm</el-radio>
                  <el-radio value="inch">inch</el-radio>
                </el-radio-group>
              </p>
              <p><span class="info-label">尺寸：</span>{{ scope.row.modelInfo.dimensions }}</p>
              <p><span class="info-label">体积：</span>{{ scope.row.modelInfo.volume }}</p>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="打印参数" min-width="200">
        <template #default="scope">
          <div class="print-params">
            <el-button type="primary" link @click="openDialog(scope.row)">
              修改打印参数（材料，表面处理等）
            </el-button>
            <p class="params-text">{{ scope.row.printParams }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="数量（件）" width="150">
        <template #default="scope">
          <el-input-number v-model="scope.row.quantity" :min="1" :max="999" size="small"
            @change="handleQuantityChange(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column prop="unitPrice" label="单价（元）" width="120"></el-table-column>
      <el-table-column prop="totalPrice" label="总价（元）" width="120"></el-table-column>
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="scope">
          <el-button type="danger" link @click="handleRemoveRow(scope.$index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { Picture, View } from '@element-plus/icons-vue'
import { onMounted } from 'vue'

onMounted(() => {
  console.log('[QuoteTable] Component mounted')
})

const props = defineProps({
  tableData: {
    type: Array,
    required: true
  }
})

const emit = defineEmits([
  'selection-change',
  'model-preview',
  'quantity-change',
  'remove-row',
  'open-dialog'
])

const handleSelectionChange = (val) => {
  console.log('[QuoteTable] Selection changed', {
    timestamp: new Date().toISOString(),
    selectedRows: val
  })
  emit('selection-change', val)
}

const handleModelPreview = (row) => {
  console.log('[QuoteTable] Model preview requested', {
    timestamp: new Date().toISOString(),
    model: row.modelInfo
  })
  emit('model-preview', row)
}

const handleQuantityChange = (row) => {
  console.log('[QuoteTable] Quantity changed', {
    timestamp: new Date().toISOString(),
    model: row.modelInfo.name,
    quantity: row.quantity
  })
  emit('quantity-change', row)
}

const handleRemoveRow = (index) => {
  console.log('[QuoteTable] Row removal requested', {
    timestamp: new Date().toISOString(),
    index: index
  })
  emit('remove-row', index)
}

const openDialog = (row) => {
  console.log('[QuoteTable] Print params dialog opened', {
    timestamp: new Date().toISOString(),
    row: row
  })
  emit('open-dialog', row)
}
</script>

<style scoped>
.model-preview {
  position: relative;
  width: 80px;
  height: 80px;
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
}

.model-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
}

.model-preview:hover .preview-overlay {
  opacity: 1;
}

.preview-overlay .el-icon {
  color: white;
  font-size: 24px;
}

.image-slot {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
}

.image-slot .el-icon {
  font-size: 24px;
  color: #909399;
}
</style>
