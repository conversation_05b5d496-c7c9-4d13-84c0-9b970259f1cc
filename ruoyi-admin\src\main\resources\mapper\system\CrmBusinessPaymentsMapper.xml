<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CrmBusinessPaymentsMapper">
    
    <resultMap type="CrmBusinessPayments" id="CrmBusinessPaymentsResult">
        <result property="id"    column="id"    />
        <result property="managerId"    column="manager_id"    />
        <result property="paymentNumber"    column="payment_number"    />
        <result property="customerId"    column="customer_id"    />
        <result property="contractId"    column="contract_id"    />
        <result property="paymentDetails"    column="payment_details"    />
        <result property="paymentDate"    column="payment_date"    />
        <result property="paymentAmount"    column="payment_amount"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="remarks"    column="remarks"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectCrmBusinessPaymentsVo">
        select id, manager_id, payment_number, customer_id, contract_id, payment_details, payment_date, payment_amount, payment_method, remarks, created_at, updated_at from crm_business_payments
    </sql>

    <select id="selectCrmBusinessPaymentsList" parameterType="CrmBusinessPayments" resultMap="CrmBusinessPaymentsResult">
        <include refid="selectCrmBusinessPaymentsVo"/>
        <where>  
            <if test="managerId != null "> and manager_id = #{managerId}</if>
            <if test="paymentNumber != null  and paymentNumber != ''"> and payment_number = #{paymentNumber}</if>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="contractId != null "> and contract_id = #{contractId}</if>
            <if test="paymentDetails != null  and paymentDetails != ''"> and payment_details = #{paymentDetails}</if>
            <if test="paymentDate != null "> and payment_date = #{paymentDate}</if>
            <if test="paymentAmount != null "> and payment_amount = #{paymentAmount}</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and payment_method = #{paymentMethod}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectCrmBusinessPaymentsById" parameterType="Long" resultMap="CrmBusinessPaymentsResult">
        <include refid="selectCrmBusinessPaymentsVo"/>
        where id = #{id}
    </select>

    <insert id="insertCrmBusinessPayments" parameterType="CrmBusinessPayments" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_payments
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="managerId != null">manager_id,</if>
            <if test="paymentNumber != null and paymentNumber != ''">payment_number,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="contractId != null">contract_id,</if>
            <if test="paymentDetails != null">payment_details,</if>
            <if test="paymentDate != null">payment_date,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="managerId != null">#{managerId},</if>
            <if test="paymentNumber != null and paymentNumber != ''">#{paymentNumber},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="contractId != null">#{contractId},</if>
            <if test="paymentDetails != null">#{paymentDetails},</if>
            <if test="paymentDate != null">#{paymentDate},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateCrmBusinessPayments" parameterType="CrmBusinessPayments">
        update crm_business_payments
        <trim prefix="SET" suffixOverrides=",">
            <if test="managerId != null">manager_id = #{managerId},</if>
            <if test="paymentNumber != null and paymentNumber != ''">payment_number = #{paymentNumber},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="paymentDetails != null">payment_details = #{paymentDetails},</if>
            <if test="paymentDate != null">payment_date = #{paymentDate},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmBusinessPaymentsById" parameterType="Long">
        delete from crm_business_payments where id = #{id}
    </delete>

    <delete id="deleteCrmBusinessPaymentsByIds" parameterType="String">
        delete from crm_business_payments where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>