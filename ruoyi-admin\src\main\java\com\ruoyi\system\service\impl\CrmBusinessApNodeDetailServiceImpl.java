package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CrmBusinessApNodeDetailMapper;
import com.ruoyi.common.domain.entity.CrmBusinessApNodeDetail;
import com.ruoyi.system.service.ICrmBusinessApNodeDetailService;

/**
 * 审批节点详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class CrmBusinessApNodeDetailServiceImpl implements ICrmBusinessApNodeDetailService 
{
    @Autowired
    private CrmBusinessApNodeDetailMapper crmBusinessApNodeDetailMapper;

    /**
     * 查询审批节点详情
     * 
     * @param detailId 审批节点详情主键
     * @return 审批节点详情
     */
    @Override
    public CrmBusinessApNodeDetail selectCrmBusinessApNodeDetailByDetailId(Long detailId)
    {
        return crmBusinessApNodeDetailMapper.selectCrmBusinessApNodeDetailByDetailId(detailId);
    }

    /**
     * 查询审批节点详情列表
     * 
     * @param crmBusinessApNodeDetail 审批节点详情
     * @return 审批节点详情
     */
    @Override
    public List<CrmBusinessApNodeDetail> selectCrmBusinessApNodeDetailList(CrmBusinessApNodeDetail crmBusinessApNodeDetail)
    {
        return crmBusinessApNodeDetailMapper.selectCrmBusinessApNodeDetailList(crmBusinessApNodeDetail);
    }

    /**
     * 新增审批节点详情
     * 
     * @param crmBusinessApNodeDetail 审批节点详情
     * @return 结果
     */
    @Override
    public int insertCrmBusinessApNodeDetail(CrmBusinessApNodeDetail crmBusinessApNodeDetail)
    {
        crmBusinessApNodeDetail.setCreateTime(DateUtils.getNowDate());
        return crmBusinessApNodeDetailMapper.insertCrmBusinessApNodeDetail(crmBusinessApNodeDetail);
    }

    /**
     * 修改审批节点详情
     * 
     * @param crmBusinessApNodeDetail 审批节点详情
     * @return 结果
     */
    @Override
    public int updateCrmBusinessApNodeDetail(CrmBusinessApNodeDetail crmBusinessApNodeDetail)
    {
        crmBusinessApNodeDetail.setUpdateTime(DateUtils.getNowDate());
        return crmBusinessApNodeDetailMapper.updateCrmBusinessApNodeDetail(crmBusinessApNodeDetail);
    }

    /**
     * 批量删除审批节点详情
     * 
     * @param detailIds 需要删除的审批节点详情主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessApNodeDetailByDetailIds(Long[] detailIds)
    {
        return crmBusinessApNodeDetailMapper.deleteCrmBusinessApNodeDetailByDetailIds(detailIds);
    }

    /**
     * 删除审批节点详情信息
     * 
     * @param detailId 审批节点详情主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessApNodeDetailByDetailId(Long detailId)
    {
        return crmBusinessApNodeDetailMapper.deleteCrmBusinessApNodeDetailByDetailId(detailId);
    }
}
