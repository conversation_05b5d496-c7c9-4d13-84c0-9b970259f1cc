<!-- 联系人表单组件 -->
<template>
  <!-- 使用 Element Plus 的表单组件 -->
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
    class="contact-form"
  >
    <!-- 基本信息卡片 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
        </div>
      </template>
      
      <!-- 第一行：姓名和客户名称 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="form.name" placeholder="请输入姓名"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户名称" prop="customerName">
            <!-- 远程搜索的客户选择器 -->
            <el-select 
              v-model="form.customerName"
              filterable
              remote
              placeholder="请选择客户"
              :remote-method="handleCustomerSearch"
              :loading="loading"
            >
              <el-option
                v-for="item in customerOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 第二行：职务和性别 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="职务" prop="position">
            <el-select
              v-model="form.position"
              placeholder="请选择职务"
              clearable
              filterable
              allow-create
            >
              <el-option
                v-for="item in positionOptions"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-radio-group v-model="form.gender">
              <el-radio label="1">男</el-radio>
              <el-radio label="2">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第三行：手机号码和电话号码 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="手机号码" prop="mobile">
            <el-input v-model="form.mobile" placeholder="请输入手机号码"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话号码" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入电话号码"/>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第四行：电子邮件和关键决策人 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="电子邮件" prop="email">
            <el-input v-model="form.email" placeholder="请输入电子邮件"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关键决策人" prop="isKeyDecisionMaker">
            <el-switch
              v-model="form.isKeyDecisionMaker"
              active-value="1"
              inactive-value="0"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第五行：直属上级和部门 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="直属上级" prop="directSuperior">
            <el-input v-model="form.directSuperior" placeholder="请输入直属上级"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门" prop="department">
            <el-input v-model="form.department" placeholder="请输入部门"/>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第六行：生日和联系人级别 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="生日" prop="birthday">
            <el-date-picker
              v-model="form.birthday"
              type="date"
              placeholder="请选择生日"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人级别" prop="contactLevel">
            <el-select
              v-model="form.contactLevel"
              placeholder="请选择联系人级别"
              clearable
            >
              <el-option label="重要" value="important"/>
              <el-option label="普通" value="normal"/>
              <el-option label="低级" value="low"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第七行：决策角色和状态 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="决策角色" prop="decisionRole">
            <el-select
              v-model="form.decisionRole"
              placeholder="请选择决策角色"
              clearable
            >
              <el-option label="决策者" value="decision_maker"/>
              <el-option label="影响者" value="influencer"/>
              <el-option label="执行者" value="executor"/>
              <el-option label="使用者" value="user"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio label="0">有效</el-radio>
              <el-radio label="1">无效</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <!-- 地址信息卡片 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span>地址信息</span>
        </div>
      </template>

      <!-- 地址选择行 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="地址" prop="address">
            <el-cascader
              v-model="form.address"
              :options="addressOptions"
              placeholder="请选择地址"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="详细地址" prop="detailedAddress">
            <el-input v-model="form.detailedAddress" placeholder="请输入详细地址"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <!-- 跟进信息卡片 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>跟进信息</span>
        </div>
      </template>

      <!-- 负责人和下次联系时间 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="负责人" prop="responsiblePersonId">
            <el-select
              v-model="form.responsiblePersonId"
              placeholder="请选择负责人"
              filterable
              clearable
            >
              <el-option
                v-for="item in responsiblePersonOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="下次联系" prop="nextContactTime">
            <el-date-picker
              v-model="form.nextContactTime"
              type="datetime"
              placeholder="请选择下次联系时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 团队分配 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="分配团队" prop="teamId">
            <div style="display: flex; align-items: center; gap: 10px;">
              <el-select
                v-model="form.teamId"
                placeholder="请选择团队（可选）"
                clearable
                filterable
                style="flex: 1"
                @focus="loadTeams"
              >
                <el-option
                  v-for="team in teamOptions"
                  :key="team.id"
                  :label="team.teamName"
                  :value="team.id"
                >
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>{{ team.teamName }}</span>
                    <span style="color: #909399; font-size: 12px;">{{ team.leaderName || '无负责人' }}</span>
                  </div>
                </el-option>
              </el-select>
              <el-button v-if="form.teamId" type="danger" plain @click="form.teamId = null">清除团队</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 备注信息 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注信息" prop="remarks">
            <el-input
              v-model="form.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <!-- 表单底部按钮 -->
    <div class="form-footer">
      <el-button type="primary" @click="submitForm(formRef)">保存</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </el-form>
</template>

<script setup lang="ts">
// 导入必要的类型和组件
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { addContacts, getResponsiblePersonList, searchCustomers, updateContacts } from '../api/contact'
import type { CrmContacts, CustomerOption, ResponsiblePersonOption } from '../types'
import { listTeam } from '@/api/crm/team'

// 初始化路由实例
const router = useRouter()
// 表单引用
const formRef = ref<FormInstance>()
// 加载状态
const loading = ref(false)
// 客户选项列表
const customerOptions = ref<CustomerOption[]>([])
// 地址选项列表
const addressOptions = ref([])
// 负责人选项列表
const responsiblePersonOptions = ref<ResponsiblePersonOption[]>([])
// 团队选项列表
const teamOptions = ref<any[]>([])
// 职位选项列表
const positionOptions = ref<string[]>([
  '总经理',
  '副总经理',
  '销售总监',
  '销售经理',
  '采购总监',
  '采购经理',
  '技术总监',
  '技术经理',
  '财务总监',
  '财务经理',
  '人力资源总监',
  '人力资源经理',
  '部门经理',
  '部门主管',
  '项目经理',
  '业务员',
  '其他'
])

// 表单数据对象
const form = reactive<CrmContacts & { teamId?: number }>({
  name: '',
  customerName: '',
  position: '',
  gender: '',
  mobile: '',
  phone: '',
  email: '',
  isKeyDecisionMaker: '0',
  directSuperior: '',
  address: '',
  detailedAddress: '',
  responsiblePersonId: '',
  nextContactTime: '',
  remarks: '',
  department: '',
  birthday: '',
  contactLevel: '',
  decisionRole: '',
  status: '0',
  teamId: undefined
})

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  customerName: [
    { required: false, message: '请选择客户', trigger: 'change' }  // 改为非必填
  ],
  mobile: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  responsiblePersonId: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ]
})

/**
 * 处理客户搜索
 * @param query 搜索关键词
 */
const handleCustomerSearch = async (query: string) => {
  if (query) {
    loading.value = true
    try {
      const res = await searchCustomers(query)
      customerOptions.value = res.data.map((item: any) => ({
        value: item.customerName,
        label: item.customerName
      }))
    } catch (error) {
      console.error('搜索客户失败:', error)
    } finally {
      loading.value = false
    }
  }
}

/**
 * 获取负责人列表
 */
const getResponsiblePersons = async () => {
  try {
    const res = await getResponsiblePersonList()
    responsiblePersonOptions.value = res.rows.map((item: any) => ({
      value: String(item.userId),
      label: item.nickName || item.userName
    }))
  } catch (error) {
    console.error('获取负责人列表失败:', error)
  }
}

/**
 * 加载团队列表
 */
const loadTeams = async () => {
  try {
    const response = await listTeam({ status: '0' })
    teamOptions.value = (response.rows || []).map((team: any) => ({
      id: team.id || team.teamId,
      teamName: team.teamName,
      leaderName: team.leaderName
    }))
  } catch (error) {
    console.error('加载团队列表失败:', error)
    teamOptions.value = []
  }
}

/**
 * 提交表单
 * @param formEl 表单实例
 */
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  await formEl.validate(async (valid) => {
    if (valid) {
      try {
        // 准备提交数据
        const submitData = { ...form }
        // 移除teamId，因为它不属于CrmContacts
        const teamId = submitData.teamId
        delete submitData.teamId
        
        if (form.id) {
          await updateContacts(submitData)
        } else {
          const response = await addContacts(submitData)
          // 如果选择了团队，创建后分配团队
          if (teamId && response.data?.id) {
            try {
              const { assignTeamToBiz } = await import('@/api/team-relation')
              await assignTeamToBiz(teamId, response.data.id, 'CONTACT')
            } catch (error) {
              console.error('分配团队失败:', error)
              ElMessage.warning('联系人创建成功，但团队分配失败')
            }
          }
        }
        ElMessage.success('保存成功')
        router.push('/contact-management/list')
      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败')
      }
    }
  })
}

/**
 * 取消操作，返回列表页
 */
const cancel = () => {
  router.push('/contact-management/list')
}

// 组件挂载时获取负责人列表和团队列表
onMounted(() => {
  getResponsiblePersons()
  loadTeams()
})
</script>

<style scoped>
/* 表单整体样式 */
.contact-form {
  padding: 20px;
}

/* 底部间距 */
.mb-4 {
  margin-bottom: 16px;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

/* 表单底部按钮区域样式 */
.form-footer {
  margin-top: 20px;
  text-align: center;
}

/* 卡片样式 */
.el-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

/* 表单项样式 */
.el-form-item {
  margin-bottom: 18px;
}
</style> 