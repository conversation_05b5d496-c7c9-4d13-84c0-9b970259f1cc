# 代码和文件位置

- **XML代码位置**: 所有的CRM系统业务XML代码都在 `ruoyi-crm` 项目下的 `resources/mapper` 目录下。XML文件也分 `common`, `crm` 等路径，在操作前需要判断具体文件。
- **Java代码位置**: 所有的CRM系统业务代码都在 `ruoyi-crm` 项目下的 `src/main/java/com/ruoyi/crm` 目录下。
- **业务公共代码位置**: 所有的CRM系统业务公共代码都在 `ruoyi-crm` 项目下的 `src/main/java/com/ruoyi/common` 目录下。
- **测试代码位置**: 测试代码写在 `ruoyi-crm` 项目下的 `src/test/java/com/ruoyi` 目录下。只写后端集成测试，不写单元测试和前端测试。
- **文档格式**: 如果需要创建文档，请使用 HTML 格式。