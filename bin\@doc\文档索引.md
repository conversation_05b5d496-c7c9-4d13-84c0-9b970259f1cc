# CRM系统文档索引

## 一、功能介绍

本文档索引提供了`@doc`目录下所有文档的概览和用途说明，帮助开发者快速定位所需的技术文档和业务文档。

---

## 二、文档分类

### 📚 设计文档

| 文档名称 | 文件路径 | 用途说明 |
|---------|----------|----------|
| [OperationLog优化设计](./设计/OperationLog优化设计.md) | `设计/OperationLog优化设计.md` | 操作日志功能的优化设计方案 |
| [企业微信线索对接与转化流程](./设计/企业微信线索对接与转化流程.md) | `设计/企业微信线索对接与转化流程.md` | 企业微信线索对接和转化流程的详细设计 |
| [对账单模块Activiti协作机制说明](./设计/对账单模块Activiti协作机制说明.md) | `设计/对账单模块Activiti协作机制说明.md` | 对账单模块与Activiti工作流的协作机制说明 |
| [线索](./设计/线索.md) | `设计/线索.md` | 线索模块相关设计文档 |
| [联系人与客户关系分析](./设计/联系人与客户关系分析.md) | `设计/联系人与客户关系分析.md` | CRM系统中联系人与客户的数据库表结构、实体关系和架构设计分析 |
| [联系人与客户关系分析(HTML版)](./设计/联系人与客户关系分析.html) | `设计/联系人与客户关系分析.html` | 联系人与客户关系分析的HTML可视化版本，包含交互式图表 |

### 📜 规定文档

| 文档名称 | 文件路径 | 用途说明 |
|---------|----------|----------|
| [架构分层规则](./规定/架构分层规则.md) | `规定/架构分层规则.md` | 项目的架构分层规范 |
| [测试认证解决方案](./规定/测试认证解决方案.md) | `规定/测试认证解决方案.md` | 关于测试和认证的解决方案 |

### 🚀 计划实现文档

| 文档名称 | 文件路径 | 用途说明 |
|---------|----------|----------|
| [3D打印服务模块功能描述文档](./计划实现/3D打印服务模块功能描述文档.html) | `计划实现/3D打印服务模块功能描述文档.html` | 3D打印服务模块的功能描述 |

### 📖 参考资料文档

| 文档名称 | 文件路径 | 用途说明 |
|---------|----------|----------|
| [Activiti介绍](./参考/activiti_introduction.md) | `参考/activiti_introduction.md` | Activiti工作流引擎的基础概念和功能介绍 |
| [BPMN标准说明](./参考/bpmn_standard_explanation.md) | `参考/bpmn_standard_explanation.md` | BPMN 2.0标准的详细说明和建模规范 |
| [生产部署指南](./参考/production-deployment-guide.md) | `参考/production-deployment-guide.md` | 系统生产环境部署的完整指南和最佳实践 |
| [企业微信与线索对接指南](./参考/企业微信与线索对接指南.md) | `参考/企业微信与线索对接指南.md` | 企业微信与线索对接的指南文档 |

### 🛠️ 脚本

| 文档名称 | 文件路径 | 用途说明 |
|---------|----------|----------|
| [mermaid.min.js](./js/mermaid.min.js) | `js/mermaid.min.js` | Mermaid.js库文件，用于渲染图表 |

---

## 三、文档结构图

```mermaid
graph TD
    Root["@doc 文档根目录"] --> Design[设计]
    Root --> Rules[规定]
    Root --> Plan[计划实现]
    Root --> Reference[参考]
    Root --> Scripts[js]

    Design --> D1["OperationLog优化设计.md"]
    Design --> D2["企业微信线索对接与转化流程.md"]
    Design --> D3["对账单模块Activiti协作机制说明.md"]
    Design --> D4["线索.md"]
    Design --> D5["联系人与客户关系分析.md"]
    Design --> D6["联系人与客户关系分析.html"]

    Rules --> R1["架构分层规则.md"]
    Rules --> R2["测试认证解决方案.md"]

    Plan --> P1["3D打印服务模块功能描述文档.html"]

    Reference --> Ref1["activiti_introduction.md"]
    Reference --> Ref2["bpmn_standard_explanation.md"]
    Reference --> Ref3["production-deployment-guide.md"]
    Reference --> Ref4["企业微信与线索对接指南.md"]

    Scripts --> S1["mermaid.min.js"]
```

---

> **本文档索引是@doc目录的导航地图，帮助快速定位所需文档。如有新增文档，请及时更新本索引。**