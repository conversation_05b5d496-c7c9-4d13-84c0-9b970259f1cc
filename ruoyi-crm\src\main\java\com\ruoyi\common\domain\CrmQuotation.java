package com.ruoyi.common.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 报价单对象 crm_quotations
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmQuotation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 报价单编号 */
    private String quotationNo;

    /** 报价单名称 */
    private String quotationName;

    /** 客户ID */
    private Long customerId;

    /** 客户名称（冗余字段） */
    private String customerName;

    /** 联系人ID */
    private Long contactId;

    /** 联系人姓名（冗余字段） */
    private String contactName;

    /** 负责人ID */
    private String responsiblePersonId;

    /** 报价总金额 */
    private BigDecimal totalAmount;

    /** 币种 */
    private String currency;

    /** 状态：draft-草稿,submitted-已提交,approved-已审批,rejected-已驳回,cancelled-已取消 */
    private String status;

    /** 审批状态：pending-待审批,approved-审批通过,rejected-审批驳回 */
    private String approvalStatus;

    /** 流程实例ID */
    private String processInstanceId;

    /** 流程状态：running-运行中,completed-完成,terminated-终止 */
    private String processStatus;

    /** 当前任务ID */
    private String currentTaskId;

    /** 当前任务名称 */
    private String currentTaskName;

    /** 当前处理人 */
    private String currentAssignee;

    /** 流程开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processStartTime;

    /** 流程结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processEndTime;

    /** 有效期至 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validUntil;

    /** 报价日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date quotationDate;

    /** 交货条款 */
    private String deliveryTerms;

    /** 付款条款 */
    private String paymentTerms;

    /** 备注 */
    private String remarks;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 报价单明细列表 */
    private List<CrmQuotationItem> quotationItems;
}