<!-- 表格操作栏组件 -->
<template>
    <el-table-column
        :label="columnProps.label"
        :width="columnProps.width"
        :fixed="columnProps.fixed"
    >
        <template #default="scope">
            <template v-for="(btn, index) in actualButtons" :key="`btn-${btn.label}-${index}`">
                <el-button
                    v-if="!btn.show || (typeof btn.show === 'function' ? btn.show(scope.row) : btn.show)"
                    :type="btn.type"
                    :link="btn.link"
                    :disabled="typeof btn.disabled === 'function' ? btn.disabled(scope.row) : btn.disabled"
                    @click="handleClick(btn, scope.row)"
                >
                    <el-icon v-if="btn.icon">
                        <component :is="btn.icon" />
                    </el-icon>
                    {{ btn.label }}
                </el-button>
            </template>
        </template>
    </el-table-column>
</template>

<script setup lang="ts">
import type { ButtonType } from 'element-plus';
import { computed } from 'vue';

export interface TableButton {
    label: string;
    type?: ButtonType;
    link?: boolean;
    icon?: string;
    show?: boolean | ((row: any) => boolean);
    disabled?: boolean | ((row: any) => boolean);
    handler: (row: any) => void;
}

interface Props {
    buttons?: TableButton[];
    config?: {
        width?: number;
        fixed?: boolean | 'right' | 'left';
        label?: string;
        buttons: TableButton[];
    };
}

const props = withDefaults(defineProps<Props>(), {
    buttons: () => []
});

const emit = defineEmits<{
    operation: [{ handler: string | ((row: any) => void), row: any }]
}>();

// 计算实际使用的按钮列表
const actualButtons = computed(() => {
    if (props.config) {
        return props.config.buttons || [];
    }
    return props.buttons || [];
});

// 计算表格列属性
const columnProps = computed(() => {
    if (props.config) {
        return {
            label: props.config.label || '操作',
            width: props.config.width || 220,
            fixed: props.config.fixed || 'right'
        };
    }
    return {
        label: '操作',
        width: 220,
        fixed: 'right'
    };
});

// 处理按钮点击事件
const handleClick = (btn: TableButton, row: any) => {
    if (typeof btn.handler === 'function') {
        btn.handler(row);
    } else {
        emit('operation', { handler: btn.handler, row });
    }
};
</script>

<style scoped>
.el-button {
    padding: 2px 0;
    height: auto;
    font-weight: normal;
    
    &:hover {
        text-decoration: underline;
    }
}

.el-icon {
    margin-right: 4px;
}
</style> 