package com.ruoyi.crm.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.domain.CrmInvoice;
import com.ruoyi.crm.service.ICrmInvoiceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 发票Controller
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@RestController
@RequestMapping("/crm/invoice")
public class CrmInvoiceController extends BaseController {
    @Autowired
    private ICrmInvoiceService crmInvoiceService;

    /**
     * 查询发票列表
     */
    @PreAuthorize("@ss.hasPermi('crm:invoice:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmInvoice crmInvoice) {
        startPage();
        List<CrmInvoice> list = crmInvoiceService.selectCrmInvoiceList(crmInvoice);
        return getDataTable(list);
    }

    /**
     * 导出发票列表
     */
    @PreAuthorize("@ss.hasPermi('crm:invoice:export')")
    @Log(title = "发票", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmInvoice crmInvoice) {
        List<CrmInvoice> list = crmInvoiceService.selectCrmInvoiceList(crmInvoice);
        ExcelUtil<CrmInvoice> util = new ExcelUtil<CrmInvoice>(CrmInvoice.class);
        util.exportExcel(response, list, "发票数据");
    }

    /**
     * 获取发票详细信息
     */
    @PreAuthorize("@ss.hasPermi('crm:invoice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(crmInvoiceService.selectCrmInvoiceById(id));
    }

    /**
     * 根据发票编号获取发票信息
     */
    @PreAuthorize("@ss.hasPermi('crm:invoice:query')")
    @GetMapping(value = "/invoiceNo/{invoiceNo}")
    public AjaxResult getInfoByInvoiceNo(@PathVariable("invoiceNo") String invoiceNo) {
        return success(crmInvoiceService.selectCrmInvoiceByInvoiceNo(invoiceNo));
    }

    /**
     * 根据报价单ID查询发票列表
     */
    @PreAuthorize("@ss.hasPermi('crm:invoice:query')")
    @GetMapping(value = "/quotation/{quotationId}")
    public AjaxResult getInvoicesByQuotationId(@PathVariable("quotationId") Long quotationId) {
        return success(crmInvoiceService.selectCrmInvoiceByQuotationId(quotationId));
    }

    /**
     * 新增发票
     */
    @PreAuthorize("@ss.hasPermi('crm:invoice:add')")
    @Log(title = "发票", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmInvoice crmInvoice) {
        return toAjax(crmInvoiceService.insertCrmInvoice(crmInvoice));
    }

    /**
     * 修改发票
     */
    @PreAuthorize("@ss.hasPermi('crm:invoice:edit')")
    @Log(title = "发票", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmInvoice crmInvoice) {
        return toAjax(crmInvoiceService.updateCrmInvoice(crmInvoice));
    }

    /**
     * 删除发票
     */
    @PreAuthorize("@ss.hasPermi('crm:invoice:remove')")
    @Log(title = "发票", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(crmInvoiceService.deleteCrmInvoiceByIds(ids));
    }

    /**
     * 从报价单创建发票
     */
    @PreAuthorize("@ss.hasPermi('crm:invoice:add')")
    @Log(title = "发票", businessType = BusinessType.INSERT)
    @PostMapping("/createFromQuotation/{quotationId}")
    public AjaxResult createFromQuotation(@PathVariable("quotationId") Long quotationId) {
        try {
            CrmInvoice invoice = crmInvoiceService.createInvoiceFromQuotation(quotationId);
            return success("从报价单创建发票成功", invoice);
        } catch (Exception e) {
            logger.error("从报价单创建发票失败", e);
            return error("创建发票失败：" + e.getMessage());
        }
    }

    /**
     * 提交发票审批
     */
    @PreAuthorize("@ss.hasPermi('crm:invoice:approval')")
    @Log(title = "发票审批", businessType = BusinessType.UPDATE)
    @PostMapping("/submit/{id}")
    public AjaxResult submitApproval(@PathVariable("id") Long id) {
        try {
            int result = crmInvoiceService.submitInvoiceApproval(id, "invoice-approval");
            if (result > 0) {
                return success("提交审批成功");
            } else {
                return error("提交审批失败");
            }
        } catch (Exception e) {
            logger.error("提交发票审批失败", e);
            return error("提交审批失败：" + e.getMessage());
        }
    }

    /**
     * 审批发票
     */
    @PreAuthorize("@ss.hasPermi('crm:invoice:approval')")
    @Log(title = "发票审批", businessType = BusinessType.UPDATE)
    @PostMapping("/approve")
    public AjaxResult approve(@RequestBody ApprovalRequest request) {
        try {
            int result = crmInvoiceService.approveInvoice(
                request.getTaskId(), 
                request.isApproved(), 
                request.getComment()
            );
            if (result > 0) {
                return success("审批操作成功");
            } else {
                return error("审批操作失败");
            }
        } catch (Exception e) {
            logger.error("审批发票失败", e);
            return error("审批操作失败：" + e.getMessage());
        }
    }

    /**
     * 开票
     */
    @PreAuthorize("@ss.hasPermi('crm:invoice:issue')")
    @Log(title = "发票开票", businessType = BusinessType.UPDATE)
    @PostMapping("/issue/{id}")
    public AjaxResult issue(@PathVariable("id") Long id) {
        try {
            int result = crmInvoiceService.issueInvoice(id);
            if (result > 0) {
                return success("开票成功");
            } else {
                return error("开票失败");
            }
        } catch (Exception e) {
            logger.error("开票失败", e);
            return error("开票失败：" + e.getMessage());
        }
    }

    /**
     * 作废发票
     */
    @PreAuthorize("@ss.hasPermi('crm:invoice:cancel')")
    @Log(title = "发票作废", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{id}")
    public AjaxResult cancel(@PathVariable("id") Long id, @RequestBody CancelRequest request) {
        try {
            int result = crmInvoiceService.cancelInvoice(id, request.getReason());
            if (result > 0) {
                return success("作废成功");
            } else {
                return error("作废失败");
            }
        } catch (Exception e) {
            logger.error("作废发票失败", e);
            return error("作废失败：" + e.getMessage());
        }
    }

    /**
     * 生成发票编号
     */
    @PreAuthorize("@ss.hasPermi('crm:invoice:add')")
    @GetMapping("/generateNo")
    public AjaxResult generateInvoiceNo() {
        return success(crmInvoiceService.generateInvoiceNo());
    }

    /**
     * 审批请求对象
     */
    public static class ApprovalRequest {
        private String taskId;
        private boolean approved;
        private String comment;

        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public boolean isApproved() {
            return approved;
        }

        public void setApproved(boolean approved) {
            this.approved = approved;
        }

        public String getComment() {
            return comment;
        }

        public void setComment(String comment) {
            this.comment = comment;
        }
    }

    /**
     * 撤销/作废请求对象
     */
    public static class CancelRequest {
        private String reason;

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }
    }
}