#!/bin/bash
echo ""
echo "[信息] 使用Jar命令运行Web工程。"
echo ""

# 获取脚本所在的目录
SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)

# 切换到 admin 模块的 target 目录
cd "$SCRIPT_DIR/../ruoyi-admin/target"

# 检查 jar 文件是否存在
if [ ! -f "ruoyi-admin.jar" ]; then
    echo "[错误] ruoyi-admin.jar 文件未找到。"
    echo "请先执行 package.sh 或 package.bat 脚本进行打包。"
    exit 1
fi

# 设置Java虚拟机参数
export JAVA_OPTS="-Xms256m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"

# 运行jar包
java $JAVA_OPTS -jar ruoyi-admin.jar
