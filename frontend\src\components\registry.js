// 组件注册中心
const componentRegistry = new Map();

/**
 * 注册组件
 * @param {string} name 组件名称
 * @param {object} component 组件对象
 */
export const registerComponent = (name, component) => {
    componentRegistry.set(name, component);
};

/**
 * 获取组件
 * @param {string} name 组件名称
 * @returns {object} 组件对象
 */
export const getComponent = (name) => {
    return componentRegistry.get(name);
};

/**
 * 创建 Vue 插件
 */
export const createComponentRegistry = () => {
    return {
        async install(app) {
            // 扫描所有组件
            const modules = import.meta.glob('@/components/**/*.vue');
            
            for (const path in modules) {
                try {
                    const module = await modules[path]();
                    const component = module.default;
                    
                    // 如果组件有name属性，就注册
                    if (component.name) {
                        registerComponent(component.name, component);
                        // 同时注册为全局组件
                        app.component(component.name, component);
                    }
                } catch (error) {
                    console.error(`注册组件失败: ${path}`, error);
                }
            }
        }
    };
}; 