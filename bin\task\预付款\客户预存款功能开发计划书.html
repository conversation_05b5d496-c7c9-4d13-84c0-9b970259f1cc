<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM412客户预存款功能开发计划书</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            margin: -20px -20px 40px -20px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 25px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .section h2 {
            color: #667eea;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
            display: flex;
            align-items: center;
        }
        
        .section h2::before {
            content: attr(data-icon);
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .section h3 {
            color: #555;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
            padding-left: 15px;
            border-left: 4px solid #667eea;
        }
        
        .section h4 {
            color: #666;
            font-size: 1.2em;
            margin: 20px 0 10px 0;
        }
        
        .requirements-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .requirements-list ol {
            counter-reset: item;
            padding-left: 0;
        }
        
        .requirements-list li {
            display: block;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9ff;
            border-left: 4px solid #667eea;
            border-radius: 5px;
            position: relative;
        }
        
        .requirements-list li::before {
            content: counter(item);
            counter-increment: item;
            background: #667eea;
            color: white;
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 50%;
            position: absolute;
            left: -15px;
            top: 15px;
        }
        
        .requirements-list li strong {
            color: #667eea;
            display: block;
            margin-bottom: 8px;
            font-size: 1.1em;
        }
        
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9em;
            line-height: 1.6;
            position: relative;
            border: 1px solid #4a5568;
        }
        
        .sql-keyword {
            color: #63b3ed;
            font-weight: bold;
        }
        
        .sql-string {
            color: #68d391;
        }
        
        .sql-comment {
            color: #a0aec0;
            font-style: italic;
        }
        
        .sql-number {
            color: #f6ad55;
            font-weight: bold;
        }
        
        .directory-tree {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 15px 0;
            line-height: 1.8;
        }
        
        .directory-tree .folder {
            color: #fbb6ce;
            font-weight: bold;
        }
        
        .directory-tree .file {
            color: #90cdf4;
        }
        
        .task-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .task-table th,
        .task-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .task-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 0.9em;
            letter-spacing: 0.5px;
        }
        
        .task-table tr:hover {
            background: #f8f9ff;
        }
        
        .task-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .status-pending {
            background: #fef5e7;
            color: #d69e2e;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-progress {
            background: #ebf8ff;
            color: #3182ce;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-completed {
            background: #f0fff4;
            color: #38a169;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Consolas', 'Monaco', monospace;
            line-height: 1.6;
            position: relative;
            border: 1px solid #4a5568;
        }
        
        .java-keyword {
            color: #63b3ed;
            font-weight: bold;
        }
        
        .java-string {
            color: #68d391;
        }
        
        .java-comment {
            color: #a0aec0;
            font-style: italic;
        }
        
        .java-annotation {
            color: #fbb6ce;
        }
        
        .java-number {
            color: #f6ad55;
            font-weight: bold;
        }
        
        .json-key {
            color: #63b3ed;
            font-weight: bold;
        }
        
        .json-value {
            color: #68d391;
        }
        
        .copy-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #4a5568;
            color: #e2e8f0;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        
        .copy-button:hover {
            opacity: 1;
            background: #667eea;
        }
        
        .copy-button.copied {
            background: #48bb78;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #667eea20, #764ba220);
            border: 1px solid #667eea;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #667eea;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -37px;
            top: 25px;
            width: 12px;
            height: 12px;
            background: #667eea;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 0 3px #667eea;
        }
        
        .timeline-item h4 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 1.3em;
        }
        
        .timeline-item .duration {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 8px 0;
            position: relative;
            padding-left: 35px;
        }
        
        .checklist li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #38a169;
            font-weight: bold;
            font-size: 1.2em;
        }
        
        .summary-box {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-top: 40px;
        }
        
        .summary-box h3 {
            font-size: 1.8em;
            margin-bottom: 15px;
        }
        
        .summary-box p {
            font-size: 1.1em;
            line-height: 1.8;
        }
        
        .rocket {
            font-size: 2em;
            margin-top: 15px;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .task-table {
                font-size: 0.9em;
            }
            
            .task-table th,
            .task-table td {
                padding: 8px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 标题 -->
        <div class="header">
            <h1>📋 CRM412客户预存款功能开发计划书</h1>
            <div class="subtitle">基于客户需求的完整开发方案 | 制定日期：2025年7月2日</div>
        </div>

        <!-- 项目概述 -->
        <div class="section">
            <h2 data-icon="🎯">项目概述</h2>
            <p>根据客户需求截图，需要为CRM系统新增客户预存款功能，实现预存款的创建、充值、消费记录管理等核心功能。该功能将完善CRM系统的财务管理模块，提升客户资金管理效率。</p>
        </div>

        <!-- 需求分析 -->
        <div class="section">
            <h2 data-icon="📋">需求分析</h2>
            <h3>核心需求</h3>
            <div class="requirements-list">
                <ol>
                    <li>
                        <strong>创建预存款</strong>
                        <ul>
                            <li>预存款是一个建议合同（以发票样式为模板，有一个独有的预存款编号）</li>
                            <li>产品类别、规格型号、数量、单价、金额、税率</li>
                            <li>可修正预订合同流转到申请审批</li>
                            <li>团队锁定此预存款单</li>
                        </ul>
                    </li>
                    <li>
                        <strong>详情添加功能（预存款）</strong>
                        <ul>
                            <li>显示预存金额，有已审清消费的消费记录、余额</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <!-- 数据库设计 -->
        <div class="section">
            <h2 data-icon="🗂️">数据库设计</h2>
            
            <h3>1. 客户预存款主表 (crm_customer_prepaid)</h3>
            <div class="sql-code">
<button class="copy-button" onclick="copyCode(this)">复制</button>
<span class="sql-keyword">CREATE TABLE</span> `crm_customer_prepaid` (
  `id` <span class="sql-keyword">bigint</span>(<span class="sql-number">20</span>) <span class="sql-keyword">NOT NULL AUTO_INCREMENT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'预存款ID'</span>,
  `prepaid_no` <span class="sql-keyword">varchar</span>(<span class="sql-number">100</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'预存款编号'</span>,
  `customer_id` <span class="sql-keyword">bigint</span>(<span class="sql-number">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'客户ID'</span>,
  `customer_name` <span class="sql-keyword">varchar</span>(<span class="sql-number">255</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'客户名称'</span>,
  `contract_template_type` <span class="sql-keyword">varchar</span>(<span class="sql-number">50</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'INVOICE'</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'合同模板类型'</span>,
  `total_amount` <span class="sql-keyword">decimal</span>(<span class="sql-number">15,2</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'预存总金额'</span>,
  `consumed_amount` <span class="sql-keyword">decimal</span>(<span class="sql-number">15,2</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-number">0.00</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'已消费金额'</span>,
  `balance_amount` <span class="sql-keyword">decimal</span>(<span class="sql-number">15,2</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'余额'</span>,
  `status` <span class="sql-keyword">varchar</span>(<span class="sql-number">20</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'DRAFT'</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'状态：草稿/审批中/已审批/已锁定'</span>,
  `manager_id` <span class="sql-keyword">bigint</span>(<span class="sql-number">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'负责人ID'</span>,
  `team_id` <span class="sql-keyword">bigint</span>(<span class="sql-number">20</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'团队ID'</span>,
  `is_locked` <span class="sql-keyword">tinyint</span>(<span class="sql-number">1</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-number">0</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'是否被团队锁定'</span>,
  `approval_status` <span class="sql-keyword">varchar</span>(<span class="sql-number">20</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'PENDING'</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'审批状态'</span>,
  `remarks` <span class="sql-keyword">text</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'备注'</span>,
  `del_flag` <span class="sql-keyword">char</span>(<span class="sql-number">1</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'0'</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'删除标志'</span>,
  `create_by` <span class="sql-keyword">varchar</span>(<span class="sql-number">64</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建者'</span>,
  `create_time` <span class="sql-keyword">datetime</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建时间'</span>,
  `update_by` <span class="sql-keyword">varchar</span>(<span class="sql-number">64</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新者'</span>,
  `update_time` <span class="sql-keyword">datetime</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新时间'</span>,
  <span class="sql-keyword">PRIMARY KEY</span> (`id`),
  <span class="sql-keyword">UNIQUE KEY</span> `uk_prepaid_no` (`prepaid_no`),
  <span class="sql-keyword">KEY</span> `idx_customer_id` (`customer_id`),
  <span class="sql-keyword">KEY</span> `idx_manager_id` (`manager_id`),
  <span class="sql-keyword">KEY</span> `idx_status` (`status`)
) <span class="sql-keyword">ENGINE</span>=<span class="sql-keyword">InnoDB</span> <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'客户预存款主表'</span>;
            </div>

            <h3>2. 预存款明细表 (crm_prepaid_details)</h3>
            <div class="sql-code">
<button class="copy-button" onclick="copyCode(this)">复制</button>
<span class="sql-keyword">CREATE TABLE</span> `crm_prepaid_details` (
  `id` <span class="sql-keyword">bigint</span>(<span class="sql-number">20</span>) <span class="sql-keyword">NOT NULL AUTO_INCREMENT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'明细ID'</span>,
  `prepaid_id` <span class="sql-keyword">bigint</span>(<span class="sql-number">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'预存款ID'</span>,
  `product_category` <span class="sql-keyword">varchar</span>(<span class="sql-number">100</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'产品类别'</span>,
  `product_model` <span class="sql-keyword">varchar</span>(<span class="sql-number">100</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'规格型号'</span>,
  `quantity` <span class="sql-keyword">decimal</span>(<span class="sql-number">10,2</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'数量'</span>,
  `unit` <span class="sql-keyword">varchar</span>(<span class="sql-number">20</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'单位'</span>,
  `unit_price` <span class="sql-keyword">decimal</span>(<span class="sql-number">10,2</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'单价'</span>,
  `amount` <span class="sql-keyword">decimal</span>(<span class="sql-number">15,2</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'金额'</span>,
  `tax_rate` <span class="sql-keyword">decimal</span>(<span class="sql-number">5,2</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'税率'</span>,
  `tax_amount` <span class="sql-keyword">decimal</span>(<span class="sql-number">15,2</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'税额'</span>,
  `total_amount` <span class="sql-keyword">decimal</span>(<span class="sql-number">15,2</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'含税总额'</span>,
  `remarks` <span class="sql-keyword">text</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'备注'</span>,
  `create_time` <span class="sql-keyword">datetime</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建时间'</span>,
  <span class="sql-keyword">PRIMARY KEY</span> (`id`),
  <span class="sql-keyword">KEY</span> `idx_prepaid_id` (`prepaid_id`)
) <span class="sql-keyword">ENGINE</span>=<span class="sql-keyword">InnoDB</span> <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'预存款明细表'</span>;
            </div>

            <h3>3. 预存款消费记录表 (crm_prepaid_consumption)</h3>
            <div class="sql-code">
<button class="copy-button" onclick="copyCode(this)">复制</button>
<span class="sql-keyword">CREATE TABLE</span> `crm_prepaid_consumption` (
  `id` <span class="sql-keyword">bigint</span>(<span class="sql-number">20</span>) <span class="sql-keyword">NOT NULL AUTO_INCREMENT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'消费记录ID'</span>,
  `prepaid_id` <span class="sql-keyword">bigint</span>(<span class="sql-number">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'预存款ID'</span>,
  `consumption_no` <span class="sql-keyword">varchar</span>(<span class="sql-number">100</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'消费单号'</span>,
  `consumption_amount` <span class="sql-keyword">decimal</span>(<span class="sql-number">15,2</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'消费金额'</span>,
  `consumption_date` <span class="sql-keyword">datetime</span> <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'消费时间'</span>,
  `consumption_type` <span class="sql-keyword">varchar</span>(<span class="sql-number">50</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'消费类型'</span>,
  `related_order_id` <span class="sql-keyword">bigint</span>(<span class="sql-number">20</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'关联订单ID'</span>,
  `related_contract_id` <span class="sql-keyword">bigint</span>(<span class="sql-number">20</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'关联合同ID'</span>,
  `approval_status` <span class="sql-keyword">varchar</span>(<span class="sql-number">20</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'APPROVED'</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'审批状态'</span>,
  `description` <span class="sql-keyword">text</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'消费说明'</span>,
  `operator_id` <span class="sql-keyword">bigint</span>(<span class="sql-number">20</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'操作人ID'</span>,
  `operator_name` <span class="sql-keyword">varchar</span>(<span class="sql-number">100</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'操作人姓名'</span>,
  `create_time` <span class="sql-keyword">datetime</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建时间'</span>,
  <span class="sql-keyword">PRIMARY KEY</span> (`id`),
  <span class="sql-keyword">KEY</span> `idx_prepaid_id` (`prepaid_id`),
  <span class="sql-keyword">KEY</span> `idx_consumption_date` (`consumption_date`)
) <span class="sql-keyword">ENGINE</span>=<span class="sql-keyword">InnoDB</span> <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'预存款消费记录表'</span>;
            </div>
        </div>

        <!-- 系统架构设计 -->
        <div class="section">
            <h2 data-icon="🏗️">系统架构设计</h2>
            
            <h3>后端模块结构</h3>
            
            <h4>1. 实体类 (Domain层)</h4>
            <div class="directory-tree">
<span class="folder">ruoyi-crm/src/main/java/com/ruoyi/common/domain/entity/</span>
├── <span class="file">CrmCustomerPrepaid.java</span>           <span class="sql-comment"># 预存款主实体</span>
├── <span class="file">CrmPrepaidDetails.java</span>            <span class="sql-comment"># 预存款明细实体</span>
└── <span class="file">CrmPrepaidConsumption.java</span>        <span class="sql-comment"># 消费记录实体</span>
            </div>

            <h4>2. 数据访问层 (Mapper层)</h4>
            <div class="directory-tree">
<span class="folder">ruoyi-crm/src/main/java/com/ruoyi/common/mapper/</span>
├── <span class="file">CrmCustomerPrepaidMapper.java</span>
├── <span class="file">CrmPrepaidDetailsMapper.java</span>
└── <span class="file">CrmPrepaidConsumptionMapper.java</span>
            </div>

            <h4>3. 服务层 (Service层)</h4>
            <div class="directory-tree">
<span class="folder">ruoyi-crm/src/main/java/com/ruoyi/crm/service/</span>
├── <span class="file">ICrmCustomerPrepaidService.java</span>
├── <span class="file">ICrmPrepaidDetailsService.java</span>
└── <span class="file">ICrmPrepaidConsumptionService.java</span>

<span class="folder">ruoyi-crm/src/main/java/com/ruoyi/crm/service/impl/</span>
├── <span class="file">CrmCustomerPrepaidServiceImpl.java</span>
├── <span class="file">CrmPrepaidDetailsServiceImpl.java</span>
└── <span class="file">CrmPrepaidConsumptionServiceImpl.java</span>
            </div>

            <h4>4. 控制层 (Controller层)</h4>
            <div class="directory-tree">
<span class="folder">ruoyi-crm/src/main/java/com/ruoyi/crm/controller/</span>
├── <span class="file">CrmCustomerPrepaidController.java</span>
├── <span class="file">CrmPrepaidDetailsController.java</span>
└── <span class="file">CrmPrepaidConsumptionController.java</span>
            </div>

            <h3>前端模块结构</h3>
            
            <h4>1. 视图组件</h4>
            <div class="directory-tree">
<span class="folder">frontend/src/views/PrepaidManagement/</span>
├── <span class="file">index.vue</span>                         <span class="sql-comment"># 预存款列表页</span>
├── <span class="file">PrepaidCreate.vue</span>                 <span class="sql-comment"># 创建预存款页</span>
├── <span class="file">PrepaidDetail.vue</span>                 <span class="sql-comment"># 预存款详情页</span>
└── <span class="folder">components/</span>
    ├── <span class="file">PrepaidForm.vue</span>              <span class="sql-comment"># 预存款表单组件</span>
    ├── <span class="file">ConsumptionRecord.vue</span>        <span class="sql-comment"># 消费记录组件</span>
    └── <span class="file">PrepaidStatus.vue</span>            <span class="sql-comment"># 状态显示组件</span>
            </div>

            <h4>2. API接口</h4>
            <div class="directory-tree">
<span class="folder">frontend/src/api/prepaid/</span>
├── <span class="file">prepaid.js</span>                       <span class="sql-comment"># 预存款相关API</span>
├── <span class="file">consumption.js</span>                   <span class="sql-comment"># 消费记录API</span>
└── <span class="file">details.js</span>                       <span class="sql-comment"># 明细相关API</span>
            </div>
        </div>

        <!-- 开发任务计划表 -->
        <div class="section">
            <h2 data-icon="📝">开发任务计划表</h2>
            
            <div class="timeline">
                <div class="timeline-item">
                    <h4>第一阶段：基础架构搭建</h4>
                    <div class="duration">预计耗时：2-3天</div>
                    <table class="task-table">
                        <thead>
                            <tr>
                                <th>任务序号</th>
                                <th>任务内容</th>
                                <th>负责模块</th>
                                <th>预计耗时</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1.1</td>
                                <td>数据库表结构设计与创建</td>
                                <td>数据库</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>1.2</td>
                                <td>实体类创建</td>
                                <td>后端/Domain</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>1.3</td>
                                <td>Mapper接口与XML编写</td>
                                <td>后端/Mapper</td>
                                <td>1天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>1.4</td>
                                <td>Service接口定义</td>
                                <td>后端/Service</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>1.5</td>
                                <td>基础Controller框架</td>
                                <td>后端/Controller</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="timeline-item">
                    <h4>第二阶段：核心业务功能开发</h4>
                    <div class="duration">预计耗时：3-4天</div>
                    <table class="task-table">
                        <thead>
                            <tr>
                                <th>任务序号</th>
                                <th>任务内容</th>
                                <th>负责模块</th>
                                <th>预计耗时</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2.1</td>
                                <td>预存款创建功能</td>
                                <td>后端/Service</td>
                                <td>1天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>2.2</td>
                                <td>预存款编号生成逻辑</td>
                                <td>后端/Utils</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>2.3</td>
                                <td>预存款明细管理</td>
                                <td>后端/Service</td>
                                <td>1天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>2.4</td>
                                <td>团队锁定功能</td>
                                <td>后端/Service</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>2.5</td>
                                <td>消费记录管理</td>
                                <td>后端/Service</td>
                                <td>1天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="timeline-item">
                    <h4>第三阶段：前端界面开发</h4>
                    <div class="duration">预计耗时：3-4天</div>
                    <table class="task-table">
                        <thead>
                            <tr>
                                <th>任务序号</th>
                                <th>任务内容</th>
                                <th>负责模块</th>
                                <th>预计耗时</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>3.1</td>
                                <td>预存款列表页面</td>
                                <td>前端/Views</td>
                                <td>1天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>3.2</td>
                                <td>预存款创建表单</td>
                                <td>前端/Components</td>
                                <td>1天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>3.3</td>
                                <td>预存款详情页面</td>
                                <td>前端/Views</td>
                                <td>1天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>3.4</td>
                                <td>消费记录展示组件</td>
                                <td>前端/Components</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>3.5</td>
                                <td>API接口对接</td>
                                <td>前端/API</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="timeline-item">
                    <h4>第四阶段：集成与测试</h4>
                    <div class="duration">预计耗时：2-3天</div>
                    <table class="task-table">
                        <thead>
                            <tr>
                                <th>任务序号</th>
                                <th>任务内容</th>
                                <th>负责模块</th>
                                <th>预计耗时</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>4.1</td>
                                <td>功能集成测试</td>
                                <td>全栈</td>
                                <td>1天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>4.2</td>
                                <td>界面优化调整</td>
                                <td>前端</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>4.3</td>
                                <td>数据验证与校正</td>
                                <td>后端</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>4.4</td>
                                <td>权限配置</td>
                                <td>系统/权限</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>4.5</td>
                                <td>用户测试与反馈</td>
                                <td>测试</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="timeline-item">
                    <h4>第五阶段：部署与优化</h4>
                    <div class="duration">预计耗时：1-2天</div>
                    <table class="task-table">
                        <thead>
                            <tr>
                                <th>任务序号</th>
                                <th>任务内容</th>
                                <th>负责模块</th>
                                <th>预计耗时</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>5.1</td>
                                <td>生产环境部署</td>
                                <td>运维</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>5.2</td>
                                <td>性能优化</td>
                                <td>后端</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>5.3</td>
                                <td>文档编写</td>
                                <td>文档</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                            <tr>
                                <td>5.4</td>
                                <td>用户培训准备</td>
                                <td>产品</td>
                                <td>0.5天</td>
                                <td><span class="status-pending">待开始</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 技术要点 -->
        <div class="section">
            <h2 data-icon="🔧">技术要点</h2>
            
            <h3>1. 预存款编号生成规则</h3>
            <div class="code-block">
<button class="copy-button" onclick="copyCode(this)">复制</button>
<span class="java-comment">// 格式：PRE + YYYYMMDD + 6位序号</span>
<span class="java-comment">// 示例：PRE20250701000001</span>
<span class="java-keyword">public</span> <span class="java-keyword">String</span> generatePrepaidNo() {
    <span class="java-keyword">String</span> dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern(<span class="java-string">"yyyyMMdd"</span>));
    <span class="java-keyword">String</span> prefix = <span class="java-string">"PRE"</span> + dateStr;
    <span class="java-comment">// 基于Redis计数器生成序号</span>
    <span class="java-keyword">return</span> prefix + String.format(<span class="java-string">"%0<span class="java-number">6</span>d"</span>, getSequenceNumber(prefix));
}
            </div>

            <h3>2. 余额计算逻辑</h3>
            <div class="code-block">
<button class="copy-button" onclick="copyCode(this)">复制</button>
<span class="java-keyword">public</span> <span class="java-keyword">void</span> updateBalance(<span class="java-keyword">Long</span> prepaidId) {
    <span class="java-keyword">CrmCustomerPrepaid</span> prepaid = selectById(prepaidId);
    <span class="java-keyword">BigDecimal</span> totalConsumption = consumptionService.getTotalConsumption(prepaidId);
    <span class="java-keyword">BigDecimal</span> balance = prepaid.getTotalAmount().subtract(totalConsumption);
    prepaid.setConsumedAmount(totalConsumption);
    prepaid.setBalanceAmount(balance);
    updateById(prepaid);
}
            </div>

            <h3>3. 团队锁定机制</h3>
            <div class="code-block">
<button class="copy-button" onclick="copyCode(this)">复制</button>
<span class="java-annotation">@Transactional</span>
<span class="java-keyword">public</span> <span class="java-keyword">boolean</span> lockPrepaid(<span class="java-keyword">Long</span> prepaidId, <span class="java-keyword">Long</span> teamId) {
    <span class="java-keyword">return</span> prepaidMapper.lockPrepaid(prepaidId, teamId) > <span class="java-number">0</span>;
}
            </div>
        </div>

        <!-- 权限配置 -->
        <div class="section">
            <h2 data-icon="📋">权限配置</h2>
            
            <h3>菜单权限</h3>
            <div class="code-block">
<button class="copy-button" onclick="copyCode(this)">复制</button>
{
  <span class="json-key">"menuName"</span>: <span class="json-value">"预存款管理"</span>,
  <span class="json-key">"menuType"</span>: <span class="json-value">"M"</span>,
  <span class="json-key">"perms"</span>: <span class="json-value">"crm:prepaid:*"</span>,
  <span class="json-key">"children"</span>: [
    {
      <span class="json-key">"menuName"</span>: <span class="json-value">"预存款列表"</span>,
      <span class="json-key">"perms"</span>: <span class="json-value">"crm:prepaid:list"</span>
    },
    {
      <span class="json-key">"menuName"</span>: <span class="json-value">"新增预存款"</span>, 
      <span class="json-key">"perms"</span>: <span class="json-value">"crm:prepaid:add"</span>
    },
    {
      <span class="json-key">"menuName"</span>: <span class="json-value">"预存款详情"</span>,
      <span class="json-key">"perms"</span>: <span class="json-value">"crm:prepaid:query"</span>
    }
  ]
}
            </div>
        </div>

        <!-- 验收标准 -->
        <div class="section">
            <h2 data-icon="🎯">验收标准</h2>
            
            <div class="highlight-box">
                <h3>功能验收</h3>
                <ul class="checklist">
                    <li>能够创建预存款记录</li>
                    <li>支持产品明细录入</li>
                    <li>团队锁定功能正常</li>
                    <li>消费记录管理完整</li>
                    <li>余额计算准确</li>
                    <li>审批流程通畅</li>
                </ul>
            </div>

            <div class="highlight-box">
                <h3>性能验收</h3>
                <ul class="checklist">
                    <li>列表页面加载时间 < 2秒</li>
                    <li>创建操作响应时间 < 1秒</li>
                    <li>并发用户数支持 > 100</li>
                </ul>
            </div>

            <div class="highlight-box">
                <h3>界面验收</h3>
                <ul class="checklist">
                    <li>界面美观、操作便捷</li>
                    <li>响应式设计适配移动端</li>
                    <li>数据展示清晰准确</li>
                </ul>
            </div>
        </div>

        <!-- 总结 -->
        <div class="summary-box">
            <h3>📋 项目总结</h3>
            <p>本计划书详细规划了客户预存款功能的完整开发流程，预计总开发周期为 <strong>10-15天</strong>。</p>
            <p>按照分阶段推进的方式，确保每个阶段的质量和进度，最终交付符合客户需求的预存款管理系统。</p>
            <div class="rocket">🚀</div>
            <p><strong>准备就绪！等待您的确认，我将按照此计划表逐步实施开发任务。</strong></p>
        </div>
    </div>

    <script>
        function copyCode(button) {
            const codeBlock = button.parentElement;
            let codeText = '';
            
            // 获取代码文本，跳过按钮元素
            const walker = document.createTreeWalker(
                codeBlock,
                NodeFilter.SHOW_TEXT,
                {
                    acceptNode: function(node) {
                        // 跳过按钮内的文本
                        if (node.parentElement.tagName === 'BUTTON') {
                            return NodeFilter.FILTER_REJECT;
                        }
                        return NodeFilter.FILTER_ACCEPT;
                    }
                }
            );
            
            let node;
            while (node = walker.nextNode()) {
                codeText += node.textContent;
            }
            
            // 清理和格式化代码文本
            codeText = codeText.replace(/\s+/g, ' ').trim();
            codeText = codeText.replace(/;\s*/g, ';\n');
            codeText = codeText.replace(/{\s*/g, '{\n  ');
            codeText = codeText.replace(/}\s*/g, '\n}');
            codeText = codeText.replace(/,\s*/g, ',\n  ');
            
            // 复制到剪贴板
            navigator.clipboard.writeText(codeText).then(function() {
                // 显示成功状态
                const originalText = button.textContent;
                button.textContent = '已复制!';
                button.classList.add('copied');
                
                setTimeout(function() {
                    button.textContent = originalText;
                    button.classList.remove('copied');
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败: ', err);
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = codeText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                const originalText = button.textContent;
                button.textContent = '已复制!';
                button.classList.add('copied');
                
                setTimeout(function() {
                    button.textContent = originalText;
                    button.classList.remove('copied');
                }, 2000);
            });
        }

        // 页面加载完成后添加滚动效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有section添加滚动进入动画
            const sections = document.querySelectorAll('.section');
            
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };
            
            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);
            
            sections.forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(section);
            });
        });
    </script>
</body>
</html>
