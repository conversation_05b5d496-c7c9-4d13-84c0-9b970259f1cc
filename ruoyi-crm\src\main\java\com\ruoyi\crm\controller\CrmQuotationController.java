package com.ruoyi.crm.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.domain.CrmQuotation;
import com.ruoyi.crm.service.ICrmQuotationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 报价单Controller
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@RestController
@RequestMapping("/crm/quotation")
public class CrmQuotationController extends BaseController {
    @Autowired
    private ICrmQuotationService crmQuotationService;

    /**
     * 查询报价单列表
     */
    @PreAuthorize("@ss.hasPermi('crm:quotation:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmQuotation crmQuotation) {
        startPage();
        List<CrmQuotation> list = crmQuotationService.selectCrmQuotationList(crmQuotation);
        return getDataTable(list);
    }

    /**
     * 导出报价单列表
     */
    @PreAuthorize("@ss.hasPermi('crm:quotation:export')")
    @Log(title = "报价单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmQuotation crmQuotation) {
        List<CrmQuotation> list = crmQuotationService.selectCrmQuotationList(crmQuotation);
        ExcelUtil<CrmQuotation> util = new ExcelUtil<CrmQuotation>(CrmQuotation.class);
        util.exportExcel(response, list, "报价单数据");
    }

    /**
     * 获取报价单详细信息
     */
    @PreAuthorize("@ss.hasPermi('crm:quotation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(crmQuotationService.selectCrmQuotationById(id));
    }

    /**
     * 根据报价单编号获取报价单信息
     */
    @PreAuthorize("@ss.hasPermi('crm:quotation:query')")
    @GetMapping(value = "/quotationNo/{quotationNo}")
    public AjaxResult getInfoByQuotationNo(@PathVariable("quotationNo") String quotationNo) {
        return success(crmQuotationService.selectCrmQuotationByQuotationNo(quotationNo));
    }

    /**
     * 新增报价单
     */
    @PreAuthorize("@ss.hasPermi('crm:quotation:add')")
    @Log(title = "报价单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmQuotation crmQuotation) {
        return toAjax(crmQuotationService.insertCrmQuotation(crmQuotation));
    }

    /**
     * 修改报价单
     */
    @PreAuthorize("@ss.hasPermi('crm:quotation:edit')")
    @Log(title = "报价单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmQuotation crmQuotation) {
        return toAjax(crmQuotationService.updateCrmQuotation(crmQuotation));
    }

    /**
     * 删除报价单
     */
    @PreAuthorize("@ss.hasPermi('crm:quotation:remove')")
    @Log(title = "报价单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(crmQuotationService.deleteCrmQuotationByIds(ids));
    }

    /**
     * 提交报价单审批
     */
    @PreAuthorize("@ss.hasPermi('crm:quotation:approval')")
    @Log(title = "报价单审批", businessType = BusinessType.UPDATE)
    @PostMapping("/submit/{id}")
    public AjaxResult submitApproval(@PathVariable("id") Long id) {
        try {
            int result = crmQuotationService.submitQuotationApproval(id, "quotation-approval");
            if (result > 0) {
                return success("提交审批成功");
            } else {
                return error("提交审批失败");
            }
        } catch (Exception e) {
            logger.error("提交报价单审批失败", e);
            return error("提交审批失败：" + e.getMessage());
        }
    }

    /**
     * 审批报价单
     */
    @PreAuthorize("@ss.hasPermi('crm:quotation:approval')")
    @Log(title = "报价单审批", businessType = BusinessType.UPDATE)
    @PostMapping("/approve")
    public AjaxResult approve(@RequestBody ApprovalRequest request) {
        try {
            int result = crmQuotationService.approveQuotation(
                request.getTaskId(), 
                request.isApproved(), 
                request.getComment()
            );
            if (result > 0) {
                return success("审批操作成功");
            } else {
                return error("审批操作失败");
            }
        } catch (Exception e) {
            logger.error("审批报价单失败", e);
            return error("审批操作失败：" + e.getMessage());
        }
    }

    /**
     * 撤销报价单审批
     */
    @PreAuthorize("@ss.hasPermi('crm:quotation:approval')")
    @Log(title = "报价单审批", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{id}")
    public AjaxResult cancelApproval(@PathVariable("id") Long id, @RequestBody CancelRequest request) {
        try {
            int result = crmQuotationService.cancelQuotationApproval(id, request.getReason());
            if (result > 0) {
                return success("撤销审批成功");
            } else {
                return error("撤销审批失败");
            }
        } catch (Exception e) {
            logger.error("撤销报价单审批失败", e);
            return error("撤销审批失败：" + e.getMessage());
        }
    }

    /**
     * 生成报价单编号
     */
    @PreAuthorize("@ss.hasPermi('crm:quotation:add')")
    @GetMapping("/generateNo")
    public AjaxResult generateQuotationNo() {
        return success(crmQuotationService.generateQuotationNo());
    }

    /**
     * 审批请求对象
     */
    public static class ApprovalRequest {
        private String taskId;
        private boolean approved;
        private String comment;

        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public boolean isApproved() {
            return approved;
        }

        public void setApproved(boolean approved) {
            this.approved = approved;
        }

        public String getComment() {
            return comment;
        }

        public void setComment(String comment) {
            this.comment = comment;
        }
    }

    /**
     * 撤销请求对象
     */
    public static class CancelRequest {
        private String reason;

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }
    }
}