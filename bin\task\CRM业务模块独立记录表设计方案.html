<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM业务模块独立记录表设计方案</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        h4 {
            color: #27ae60;
            margin-top: 20px;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .problem {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .solution {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .sql-block {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .sql-keyword {
            color: #3498db;
            font-weight: bold;
        }
        .sql-string {
            color: #e74c3c;
        }
        .sql-comment {
            color: #95a5a6;
            font-style: italic;
        }
        .task-list {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .task-item {
            margin: 10px 0;
            padding: 10px;
            background-color: white;
            border-left: 4px solid #28a745;
            border-radius: 3px;
        }
        .priority-high {
            border-left-color: #dc3545;
        }
        .priority-medium {
            border-left-color: #ffc107;
        }
        .priority-low {
            border-left-color: #28a745;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
        }
        code {
            background-color: #f1f2f6;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .status-pending {
            background-color: #ffc107;
        }
        .status-in-progress {
            background-color: #17a2b8;
        }
        .status-completed {
            background-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CRM业务模块独立记录表设计方案</h1>
        
        <div class="highlight">
            <strong>项目目标：</strong>为每个CRM业务模块创建独立的记录表，替代通用的 <code>crm_business_follow_up_records</code> 表，提供更好的数据架构和业务隔离。
        </div>

        <h2>1. 项目背景</h2>
        
        <h3>1.1 现状分析</h3>
        
        <h4>已完成功能</h4>
        <ul>
            <li>✅ 基础CRM业务模块表结构已建立</li>
            <li>✅ 通用跟进记录表 <code>crm_business_follow_up_records</code> 已实现</li>
            <li>✅ 基本的业务模块功能已开发</li>
        </ul>
        
        <h4>发现的关键问题</h4>
        <div class="problem">
            <strong>通用记录表的局限性：</strong>
            <ul>
                <li>🔴 <strong>数据混合存储：</strong>所有业务模块的记录混合在一个表中，通过 <code>module_type</code> 字段区分</li>
                <li>🔴 <strong>字段冗余：</strong>不同业务模块需要不同的字段，导致大量NULL值</li>
                <li>🔴 <strong>查询性能：</strong>需要额外的条件过滤，影响查询效率</li>
                <li>🔴 <strong>业务隔离性差：</strong>不同业务模块的数据耦合度高</li>
                <li>🔴 <strong>扩展性限制：</strong>新增业务特定字段困难</li>
                <li>🔴 <strong>数据完整性：</strong>难以建立有效的外键约束</li>
            </ul>
        </div>

        <h3>1.2 改进目标</h3>
        <div class="solution">
            <strong>设计目标：</strong>
            <ul>
                <li>🎯 为每个业务模块创建独立的记录表</li>
                <li>🎯 提高数据查询性能和业务隔离性</li>
                <li>🎯 增强数据完整性和扩展性</li>
                <li>🎯 简化业务逻辑和代码维护</li>
            </ul>
        </div>

        <h2>2. 技术架构设计</h2>
        
        <h3>2.1 业务模块分析</h3>
        
        <p>根据现有数据库结构分析，CRM系统包含以下核心业务模块：</p>
        
        <table>
            <thead>
                <tr>
                    <th>业务模块</th>
                    <th>主表</th>
                    <th>当前记录表</th>
                    <th>新设计记录表</th>
                    <th>业务特点</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>联系人管理</td>
                    <td><code>crm_business_contacts</code></td>
                    <td><code>crm_business_follow_up_records</code></td>
                    <td><code>crm_contact_followup_records</code></td>
                    <td>个人联系记录，关注沟通频次</td>
                </tr>
                <tr>
                    <td>客户管理</td>
                    <td><code>crm_business_customers</code></td>
                    <td><code>crm_business_follow_up_records</code></td>
                    <td><code>crm_customer_followup_records</code></td>
                    <td>企业客户跟进，关注商业价值</td>
                </tr>
                <tr>
                    <td>线索管理</td>
                    <td><code>crm_business_leads</code></td>
                    <td><code>crm_business_follow_up_records</code></td>
                    <td><code>crm_lead_followup_records</code></td>
                    <td>潜在客户挖掘，关注转化率</td>
                </tr>
                <tr>
                    <td>商机管理</td>
                    <td><code>crm_business_opportunities</code></td>
                    <td><code>crm_business_follow_up_records</code></td>
                    <td><code>crm_opportunity_followup_records</code></td>
                    <td>销售机会跟踪，关注成交概率</td>
                </tr>
                <tr>
                    <td>合同管理</td>
                    <td><code>crm_business_contracts</code></td>
                    <td><code>crm_business_follow_up_records</code></td>
                    <td><code>crm_contract_followup_records</code></td>
                    <td>合同执行跟踪，关注履约状态</td>
                </tr>
                <tr>
                    <td>回款管理</td>
                    <td><code>crm_business_payments</code></td>
                    <td><code>crm_business_follow_up_records</code></td>
                    <td><code>crm_payment_followup_records</code></td>
                    <td>回款进度跟踪，关注资金回收</td>
                </tr>
            </tbody>
        </table>

        <h3>2.2 数据库架构改进</h3>
        
        <h4>新架构ER图</h4>
        <div class="mermaid">
            erDiagram
                crm_business_contacts ||--o{ crm_contact_followup_records : "一对多"
                crm_business_customers ||--o{ crm_customer_followup_records : "一对多"
                crm_business_leads ||--o{ crm_lead_followup_records : "一对多"
                crm_business_opportunities ||--o{ crm_opportunity_followup_records : "一对多"
                crm_business_contracts ||--o{ crm_contract_followup_records : "一对多"
                crm_business_payments ||--o{ crm_payment_followup_records : "一对多"
                sys_user ||--o{ crm_contact_followup_records : "创建人"
                sys_user ||--o{ crm_customer_followup_records : "创建人"
                sys_user ||--o{ crm_lead_followup_records : "创建人"
                sys_user ||--o{ crm_opportunity_followup_records : "创建人"
                sys_user ||--o{ crm_contract_followup_records : "创建人"
                sys_user ||--o{ crm_payment_followup_records : "创建人"
                
                crm_contact_followup_records {
                    bigint id PK
                    bigint contact_id FK
                    text follow_up_content
                    varchar next_contact_method
                    varchar follow_up_method
                    datetime next_contact_time
                    varchar communication_result
                    text meeting_summary
                    bigint creator_id FK
                    datetime created_at
                    datetime updated_at
                }
                
                crm_customer_followup_records {
                    bigint id PK
                    bigint customer_id FK
                    text follow_up_content
                    varchar follow_up_type
                    varchar business_stage
                    decimal potential_value
                    varchar cooperation_intention
                    text competitor_analysis
                    bigint creator_id FK
                    datetime created_at
                    datetime updated_at
                }
                
                crm_lead_followup_records {
                    bigint id PK
                    bigint lead_id FK
                    text follow_up_content
                    varchar lead_status
                    varchar qualification_level
                    decimal estimated_value
                    varchar conversion_probability
                    datetime expected_conversion_date
                    bigint creator_id FK
                    datetime created_at
                    datetime updated_at
                }
                
                crm_opportunity_followup_records {
                    bigint id PK
                    bigint opportunity_id FK
                    text follow_up_content
                    varchar opportunity_stage
                    decimal win_probability
                    varchar competitor_info
                    text negotiation_points
                    datetime expected_close_date
                    bigint creator_id FK
                    datetime created_at
                    datetime updated_at
                }
                
                crm_contract_followup_records {
                    bigint id PK
                    bigint contract_id FK
                    text follow_up_content
                    varchar execution_status
                    varchar milestone_progress
                    text risk_assessment
                    varchar payment_status
                    datetime next_review_date
                    bigint creator_id FK
                    datetime created_at
                    datetime updated_at
                }
                
                crm_payment_followup_records {
                    bigint id PK
                    bigint payment_id FK
                    text follow_up_content
                    varchar payment_status
                    decimal outstanding_amount
                    varchar collection_method
                    text delay_reason
                    datetime expected_payment_date
                    bigint creator_id FK
                    datetime created_at
                    datetime updated_at
                }
        </div>

        <h2>3. 数据库表结构设计</h2>
        
        <h3>3.1 联系人跟进记录表</h3>
        <div class="sql-block">
<span class="sql-comment">-- 联系人跟进记录表</span>
<span class="sql-keyword">DROP TABLE IF EXISTS</span> `crm_contact_followup_records`;
<span class="sql-keyword">CREATE TABLE</span> `crm_contact_followup_records` (
  `id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL AUTO_INCREMENT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'主键ID'</span>,
  `contact_id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'联系人ID'</span>,
  `follow_up_content` <span class="sql-keyword">TEXT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'跟进内容'</span>,
  `next_contact_method` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">100</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'下次联系方式'</span>,
  `follow_up_method` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">100</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'跟进方式'</span>,
  `next_contact_time` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'下次联系时间'</span>,
  `communication_result` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">200</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'沟通结果'</span>,
  `meeting_summary` <span class="sql-keyword">TEXT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'会议纪要'</span>,
  `contact_quality` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">50</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'联系质量评级'</span>,
  `related_files` <span class="sql-keyword">JSON</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'相关文件'</span>,
  `creator_id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建人ID'</span>,
  `created_at` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">NOT NULL DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建时间'</span>,
  `updated_at` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新时间'</span>,
  <span class="sql-keyword">PRIMARY KEY</span> (`id`),
  <span class="sql-keyword">FOREIGN KEY</span> (contact_id) <span class="sql-keyword">REFERENCES</span> crm_business_contacts(id),
  <span class="sql-keyword">FOREIGN KEY</span> (creator_id) <span class="sql-keyword">REFERENCES</span> sys_user(user_id),
  <span class="sql-keyword">INDEX</span> idx_contact_id (contact_id),
  <span class="sql-keyword">INDEX</span> idx_creator_time (creator_id, created_at),
  <span class="sql-keyword">INDEX</span> idx_next_contact (next_contact_time)
) <span class="sql-keyword">ENGINE</span>=<span class="sql-string">InnoDB</span> <span class="sql-keyword">DEFAULT CHARSET</span>=<span class="sql-string">utf8mb4</span> <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'联系人跟进记录表'</span>;
        </div>

        <h3>3.2 客户跟进记录表</h3>
        <div class="sql-block">
<span class="sql-comment">-- 客户跟进记录表</span>
<span class="sql-keyword">DROP TABLE IF EXISTS</span> `crm_customer_followup_records`;
<span class="sql-keyword">CREATE TABLE</span> `crm_customer_followup_records` (
  `id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL AUTO_INCREMENT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'主键ID'</span>,
  `customer_id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'客户ID'</span>,
  `follow_up_content` <span class="sql-keyword">TEXT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'跟进内容'</span>,
  `follow_up_type` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">100</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'跟进类型'</span>,
  `business_stage` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">100</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'业务阶段'</span>,
  `potential_value` <span class="sql-keyword">DECIMAL</span>(<span class="sql-string">15,2</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'潜在价值'</span>,
  `cooperation_intention` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">200</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'合作意向'</span>,
  `competitor_analysis` <span class="sql-keyword">TEXT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'竞争对手分析'</span>,
  `decision_maker_info` <span class="sql-keyword">TEXT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'决策人信息'</span>,
  `next_action_plan` <span class="sql-keyword">TEXT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'下一步行动计划'</span>,
  `related_files` <span class="sql-keyword">JSON</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'相关文件'</span>,
  `creator_id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建人ID'</span>,
  `created_at` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">NOT NULL DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建时间'</span>,
  `updated_at` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新时间'</span>,
  <span class="sql-keyword">PRIMARY KEY</span> (`id`),
  <span class="sql-keyword">FOREIGN KEY</span> (customer_id) <span class="sql-keyword">REFERENCES</span> crm_business_customers(id),
  <span class="sql-keyword">FOREIGN KEY</span> (creator_id) <span class="sql-keyword">REFERENCES</span> sys_user(user_id),
  <span class="sql-keyword">INDEX</span> idx_customer_id (customer_id),
  <span class="sql-keyword">INDEX</span> idx_creator_time (creator_id, created_at),
  <span class="sql-keyword">INDEX</span> idx_business_stage (business_stage)
) <span class="sql-keyword">ENGINE</span>=<span class="sql-string">InnoDB</span> <span class="sql-keyword">DEFAULT CHARSET</span>=<span class="sql-string">utf8mb4</span> <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'客户跟进记录表'</span>;
        </div>

        <h3>3.3 线索跟进记录表</h3>
        <div class="sql-block">
<span class="sql-comment">-- 线索跟进记录表</span>
<span class="sql-keyword">DROP TABLE IF EXISTS</span> `crm_lead_followup_records`;
<span class="sql-keyword">CREATE TABLE</span> `crm_lead_followup_records` (
  `id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL AUTO_INCREMENT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'主键ID'</span>,
  `lead_id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'线索ID'</span>,
  `follow_up_content` <span class="sql-keyword">TEXT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'跟进内容'</span>,
  `lead_status` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">100</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'线索状态'</span>,
  `qualification_level` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">50</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'资质等级'</span>,
  `estimated_value` <span class="sql-keyword">DECIMAL</span>(<span class="sql-string">15,2</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'预估价值'</span>,
  `conversion_probability` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">50</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'转化概率'</span>,
  `expected_conversion_date` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'预期转化时间'</span>,
  `contact_preference` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">100</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'联系偏好'</span>,
  `pain_points` <span class="sql-keyword">TEXT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'痛点分析'</span>,
  `related_files` <span class="sql-keyword">JSON</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'相关文件'</span>,
  `creator_id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建人ID'</span>,
  `created_at` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">NOT NULL DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建时间'</span>,
  `updated_at` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新时间'</span>,
  <span class="sql-keyword">PRIMARY KEY</span> (`id`),
  <span class="sql-keyword">FOREIGN KEY</span> (lead_id) <span class="sql-keyword">REFERENCES</span> crm_business_leads(id),
  <span class="sql-keyword">FOREIGN KEY</span> (creator_id) <span class="sql-keyword">REFERENCES</span> sys_user(user_id),
  <span class="sql-keyword">INDEX</span> idx_lead_id (lead_id),
  <span class="sql-keyword">INDEX</span> idx_creator_time (creator_id, created_at),
  <span class="sql-keyword">INDEX</span> idx_lead_status (lead_status),
  <span class="sql-keyword">INDEX</span> idx_conversion_date (expected_conversion_date)
) <span class="sql-keyword">ENGINE</span>=<span class="sql-string">InnoDB</span> <span class="sql-keyword">DEFAULT CHARSET</span>=<span class="sql-string">utf8mb4</span> <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'线索跟进记录表'</span>;
        </div>

        <h3>3.4 商机跟进记录表</h3>
        <div class="sql-block">
<span class="sql-comment">-- 商机跟进记录表</span>
<span class="sql-keyword">DROP TABLE IF EXISTS</span> `crm_opportunity_followup_records`;
<span class="sql-keyword">CREATE TABLE</span> `crm_opportunity_followup_records` (
  `id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL AUTO_INCREMENT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'主键ID'</span>,
  `opportunity_id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'商机ID'</span>,
  `follow_up_content` <span class="sql-keyword">TEXT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'跟进内容'</span>,
  `opportunity_stage` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">100</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'商机阶段'</span>,
  `win_probability` <span class="sql-keyword">DECIMAL</span>(<span class="sql-string">5,2</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'赢单概率'</span>,
  `competitor_info` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">500</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'竞争对手信息'</span>,
  `negotiation_points` <span class="sql-keyword">TEXT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'谈判要点'</span>,
  `expected_close_date` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'预期成交时间'</span>,
  `budget_confirmed` <span class="sql-keyword">TINYINT</span>(<span class="sql-string">1</span>) <span class="sql-keyword">DEFAULT 0</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'预算是否确认'</span>,
  `decision_timeline` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">200</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'决策时间线'</span>,
  `related_files` <span class="sql-keyword">JSON</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'相关文件'</span>,
  `creator_id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建人ID'</span>,
  `created_at` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">NOT NULL DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建时间'</span>,
  `updated_at` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新时间'</span>,
  <span class="sql-keyword">PRIMARY KEY</span> (`id`),
  <span class="sql-keyword">FOREIGN KEY</span> (opportunity_id) <span class="sql-keyword">REFERENCES</span> crm_business_opportunities(id),
  <span class="sql-keyword">FOREIGN KEY</span> (creator_id) <span class="sql-keyword">REFERENCES</span> sys_user(user_id),
  <span class="sql-keyword">INDEX</span> idx_opportunity_id (opportunity_id),
  <span class="sql-keyword">INDEX</span> idx_creator_time (creator_id, created_at),
  <span class="sql-keyword">INDEX</span> idx_opportunity_stage (opportunity_stage),
  <span class="sql-keyword">INDEX</span> idx_close_date (expected_close_date)
) <span class="sql-keyword">ENGINE</span>=<span class="sql-string">InnoDB</span> <span class="sql-keyword">DEFAULT CHARSET</span>=<span class="sql-string">utf8mb4</span> <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'商机跟进记录表'</span>;
        </div>

        <h3>3.5 合同跟进记录表</h3>
        <div class="sql-block">
<span class="sql-comment">-- 合同跟进记录表</span>
<span class="sql-keyword">DROP TABLE IF EXISTS</span> `crm_contract_followup_records`;
<span class="sql-keyword">CREATE TABLE</span> `crm_contract_followup_records` (
  `id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL AUTO_INCREMENT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'主键ID'</span>,
  `contract_id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'合同ID'</span>,
  `follow_up_content` <span class="sql-keyword">TEXT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'跟进内容'</span>,
  `execution_status` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">100</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'执行状态'</span>,
  `milestone_progress` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">200</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'里程碑进度'</span>,
  `risk_assessment` <span class="sql-keyword">TEXT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'风险评估'</span>,
  `payment_status` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">100</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'付款状态'</span>,
  `next_review_date` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'下次审查时间'</span>,
  `delivery_progress` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">200</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'交付进度'</span>,
  `customer_satisfaction` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">50</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'客户满意度'</span>,
  `related_files` <span class="sql-keyword">JSON</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'相关文件'</span>,
  `creator_id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建人ID'</span>,
  `created_at` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">NOT NULL DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建时间'</span>,
  `updated_at` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新时间'</span>,
  <span class="sql-keyword">PRIMARY KEY</span> (`id`),
  <span class="sql-keyword">FOREIGN KEY</span> (contract_id) <span class="sql-keyword">REFERENCES</span> crm_business_contracts(id),
  <span class="sql-keyword">FOREIGN KEY</span> (creator_id) <span class="sql-keyword">REFERENCES</span> sys_user(user_id),
  <span class="sql-keyword">INDEX</span> idx_contract_id (contract_id),
  <span class="sql-keyword">INDEX</span> idx_creator_time (creator_id, created_at),
  <span class="sql-keyword">INDEX</span> idx_execution_status (execution_status),
  <span class="sql-keyword">INDEX</span> idx_review_date (next_review_date)
) <span class="sql-keyword">ENGINE</span>=<span class="sql-string">InnoDB</span> <span class="sql-keyword">DEFAULT CHARSET</span>=<span class="sql-string">utf8mb4</span> <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'合同跟进记录表'</span>;
        </div>

        <h3>3.6 回款跟进记录表</h3>
        <div class="sql-block">
<span class="sql-comment">-- 回款跟进记录表</span>
<span class="sql-keyword">DROP TABLE IF EXISTS</span> `crm_payment_followup_records`;
<span class="sql-keyword">CREATE TABLE</span> `crm_payment_followup_records` (
  `id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL AUTO_INCREMENT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'主键ID'</span>,
  `payment_id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'回款ID'</span>,
  `follow_up_content` <span class="sql-keyword">TEXT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'跟进内容'</span>,
  `payment_status` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">100</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'回款状态'</span>,
  `outstanding_amount` <span class="sql-keyword">DECIMAL</span>(<span class="sql-string">15,2</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'未回款金额'</span>,
  `collection_method` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">100</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'催收方式'</span>,
  `delay_reason` <span class="sql-keyword">TEXT</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'延迟原因'</span>,
  `expected_payment_date` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'预期回款时间'</span>,
  `collection_difficulty` <span class="sql-keyword">VARCHAR</span>(<span class="sql-string">50</span>) <span class="sql-keyword">COMMENT</span> <span class="sql-string">'催收难度'</span>,
  `legal_action_required` <span class="sql-keyword">TINYINT</span>(<span class="sql-string">1</span>) <span class="sql-keyword">DEFAULT 0</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'是否需要法律行动'</span>,
  `related_files` <span class="sql-keyword">JSON</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'相关文件'</span>,
  `creator_id` <span class="sql-keyword">BIGINT</span>(<span class="sql-string">20</span>) <span class="sql-keyword">NOT NULL</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建人ID'</span>,
  `created_at` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">NOT NULL DEFAULT CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'创建时间'</span>,
  `updated_at` <span class="sql-keyword">DATETIME</span> <span class="sql-keyword">NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span> <span class="sql-keyword">COMMENT</span> <span class="sql-string">'更新时间'</span>,
  <span class="sql-keyword">PRIMARY KEY</span> (`id`),
  <span class="sql-keyword">FOREIGN KEY</span> (payment_id) <span class="sql-keyword">REFERENCES</span> crm_business_payments(id),
  <span class="sql-keyword">FOREIGN KEY</span> (creator_id) <span class="sql-keyword">REFERENCES</span> sys_user(user_id),
  <span class="sql-keyword">INDEX</span> idx_payment_id (payment_id),
  <span class="sql-keyword">INDEX</span> idx_creator_time (creator_id, created_at),
  <span class="sql-keyword">INDEX</span> idx_payment_status (payment_status),
  <span class="sql-keyword">INDEX</span> idx_expected_date (expected_payment_date)
) <span class="sql-keyword">ENGINE</span>=<span class="sql-string">InnoDB</span> <span class="sql-keyword">DEFAULT CHARSET</span>=<span class="sql-string">utf8mb4</span> <span class="sql-keyword">COMMENT</span>=<span class="sql-string">'回款跟进记录表'</span>;
        </div>

        <h2>4. 实施计划</h2>
        
        <div class="task-list">
            <h3>4.1 阶段一：数据库结构改进 <span class="status-badge status-pending">待开始</span></h3>
            
            <div class="task-item priority-high">
                <h4>任务1.1：创建独立记录表</h4>
                <p><strong>工期：</strong>2天</p>
                <p><strong>负责人：</strong>数据库开发工程师</p>
                <p><strong>内容：</strong></p>
                <ul>
                    <li>执行上述6个独立记录表的SQL创建脚本</li>
                    <li>创建必要的索引和外键约束</li>
                    <li>验证表结构的正确性</li>
                </ul>
            </div>
            
            <div class="task-item priority-high">
                <h4>任务1.2：数据迁移脚本</h4>
                <p><strong>工期：</strong>3天</p>
                <p><strong>负责人：</strong>数据库开发工程师</p>
                <p><strong>内容：</strong></p>
                <ul>
                    <li>编写从 <code>crm_business_follow_up_records</code> 到各独立表的数据迁移脚本</li>
                    <li>根据 <code>module_type</code> 字段分类迁移数据</li>
                    <li>验证数据迁移的完整性和准确性</li>
                    <li>制定回滚方案</li>
                </ul>
            </div>
            
            <div class="task-item priority-medium">
                <h4>任务1.3：数据库性能优化</h4>
                <p><strong>工期：</strong>1天</p>
                <p><strong>负责人：</strong>数据库开发工程师</p>
                <p><strong>内容：</strong></p>
                <ul>
                    <li>分析查询性能，优化索引策略</li>
                    <li>设置合适的表分区策略（如有需要）</li>
                    <li>配置数据库连接池参数</li>
                </ul>
            </div>
        </div>
        
        <div class="task-list">
            <h3>4.2 阶段二：后端API开发 <span class="status-badge status-pending">待开始</span></h3>
            
            <div class="task-item priority-high">
                <h4>任务2.1：实体类和Mapper开发</h4>
                <p><strong>工期：</strong>3天</p>
                <p><strong>负责人：</strong>后端开发工程师</p>
                <p><strong>内容：</strong></p>
                <ul>
                    <li>为每个独立记录表创建对应的实体类</li>
                    <li>开发MyBatis Mapper接口和XML文件</li>
                    <li>实现基础的CRUD操作</li>
                </ul>
            </div>
            
            <div class="task-item priority-high">
                <h4>任务2.2：Service层重构</h4>
                <p><strong>工期：</strong>4天</p>
                <p><strong>负责人：</strong>后端开发工程师</p>
                <p><strong>内容：</strong></p>
                <ul>
                    <li>重构现有的跟进记录Service，拆分为独立的业务Service</li>
                    <li>实现 <code>ContactFollowupService</code>、<code>CustomerFollowupService</code> 等</li>
                    <li>保持原有API接口的兼容性</li>
                    <li>添加业务特定的查询和统计方法</li>
                </ul>
            </div>
            
            <div class="task-item priority-medium">
                <h4>任务2.3：Controller层适配</h4>
                <p><strong>工期：</strong>2天</p>
                <p><strong>负责人：</strong>后端开发工程师</p>
                <p><strong>内容：</strong></p>
                <ul>
                    <li>修改现有Controller，调用新的Service方法</li>
                    <li>添加业务特定的API端点</li>
                    <li>更新API文档</li>
                </ul>
            </div>
        </div>
        
        <div class="task-list">
            <h3>4.3 阶段三：前端界面适配 <span class="status-badge status-pending">待开始</span></h3>
            
            <div class="task-item priority-medium">
                <h4>任务3.1：跟进记录组件重构</h4>
                <p><strong>工期：</strong>3天</p>
                <p><strong>负责人：</strong>前端开发工程师</p>
                <p><strong>内容：</strong></p>
                <ul>
                    <li>重构跟进记录表单组件，支持业务特定字段</li>
                    <li>优化用户界面，提供更好的用户体验</li>
                    <li>实现字段的动态显示和隐藏</li>
                </ul>
            </div>
            
            <div class="task-item priority-medium">
                <h4>任务3.2：业务模块页面更新</h4>
                <p><strong>工期：</strong>4天</p>
                <p><strong>负责人：</strong>前端开发工程师</p>
                <p><strong>内容：</strong></p>
                <ul>
                    <li>更新联系人、客户、线索等模块的跟进记录显示</li>
                    <li>添加业务特定的筛选和排序功能</li>
                    <li>优化数据展示和交互体验</li>
                </ul>
            </div>
        </div>
        
        <div class="task-list">
            <h3>4.4 阶段四：测试和部署 <span class="status-badge status-pending">待开始</span></h3>
            
            <div class="task-item priority-high">
                <h4>任务4.1：单元测试和集成测试</h4>
                <p><strong>工期：</strong>3天</p>
                <p><strong>负责人：</strong>测试工程师</p>
                <p><strong>内容：</strong></p>
                <ul>
                    <li>编写新功能的单元测试用例</li>
                    <li>执行集成测试，验证各模块间的协作</li>
                    <li>性能测试，确保系统性能不受影响</li>
                </ul>
            </div>
            
            <div class="task-item priority-high">
                <h4>任务4.2：用户验收测试</h4>
                <p><strong>工期：</strong>2天</p>
                <p><strong>负责人：</strong>产品经理</p>
                <p><strong>内容：</strong></p>
                <ul>
                    <li>组织用户进行功能验收</li>
                    <li>收集用户反馈并进行必要的调整</li>
                    <li>确认功能满足业务需求</li>
                </ul>
            </div>
            
            <div class="task-item priority-medium">
                <h4>任务4.3：生产环境部署</h4>
                <p><strong>工期：</strong>1天</p>
                <p><strong>负责人：</strong>运维工程师</p>
                <p><strong>内容：</strong></p>
                <ul>
                    <li>制定详细的部署计划</li>
                    <li>执行数据库迁移和应用部署</li>
                    <li>监控系统运行状态</li>
                    <li>准备应急回滚方案</li>
                </ul>
            </div>
        </div>

        <h2>5. 风险评估与应对</h2>
        
        <table>
            <thead>
                <tr>
                    <th>风险类型</th>
                    <th>风险描述</th>
                    <th>影响程度</th>
                    <th>应对措施</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>数据迁移风险</td>
                    <td>数据迁移过程中可能出现数据丢失或损坏</td>
                    <td>高</td>
                    <td>制定详细的备份和回滚方案，分批次迁移，充分测试</td>
                </tr>
                <tr>
                    <td>性能风险</td>
                    <td>新架构可能影响系统查询性能</td>
                    <td>中</td>
                    <td>提前进行性能测试，优化索引策略，监控系统性能</td>
                </tr>
                <tr>
                    <td>兼容性风险</td>
                    <td>现有功能可能受到影响</td>
                    <td>中</td>
                    <td>保持API接口兼容性，充分的回归测试</td>
                </tr>
                <tr>
                    <td>开发进度风险</td>
                    <td>开发任务可能延期</td>
                    <td>低</td>
                    <td>合理安排开发资源，设置里程碑检查点</td>
                </tr>
            </tbody>
        </table>

        <h2>6. 预期收益</h2>
        
        <div class="solution">
            <h3>6.1 技术收益</h3>
            <ul>
                <li>🚀 <strong>查询性能提升：</strong>独立表结构减少不必要的数据扫描，提高查询效率</li>
                <li>🔧 <strong>维护性增强：</strong>业务逻辑更清晰，代码维护更容易</li>
                <li>📈 <strong>扩展性提升：</strong>每个业务模块可以独立扩展特定字段</li>
                <li>🛡️ <strong>数据完整性：</strong>通过外键约束保证数据一致性</li>
            </ul>
            
            <h3>6.2 业务收益</h3>
            <ul>
                <li>📊 <strong>业务分析：</strong>每个业务模块的数据更容易进行专业分析</li>
                <li>⚡ <strong>响应速度：</strong>用户操作响应更快，提升用户体验</li>
                <li>🎯 <strong>业务专业化：</strong>不同业务模块可以有针对性的功能优化</li>
                <li>📋 <strong>报表优化：</strong>业务报表生成更高效，数据更准确</li>
            </ul>
        </div>

        <h2>7. 总结</h2>
        
        <p>本方案通过将通用的 <code>crm_business_follow_up_records</code> 表拆分为6个独立的业务记录表，实现了更好的数据架构设计。新架构不仅提高了系统性能和可维护性，还为各业务模块的专业化发展奠定了基础。</p>
        
        <div class="highlight">
            <strong>项目总工期：</strong>约25个工作日<br>
            <strong>涉及人员：</strong>数据库工程师、后端工程师、前端工程师、测试工程师、产品经理、运维工程师<br>
            <strong>预期上线时间：</strong>项目启动后5周内完成
        </div>
        
        <p>通过本次改进，CRM系统将具备更强的业务适应性和技术先进性，为未来的功能扩展和性能优化提供坚实的基础。</p>
    </div>
    
    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加任务状态切换功能
            const taskItems = document.querySelectorAll('.task-item');
            taskItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 可以添加任务详情展开功能
                    console.log('Task clicked:', this.querySelector('h4').textContent);
                });
            });
            
            // 添加进度统计
            updateProgressStats();
        });
        
        function updateProgressStats() {
            const totalTasks = document.querySelectorAll('.task-item').length;
            const completedTasks = document.querySelectorAll('.status-completed').length;
            const inProgressTasks = document.querySelectorAll('.status-in-progress').length;
            const pendingTasks = document.querySelectorAll('.status-pending').length;
            
            console.log(`项目进度统计: 总任务${totalTasks}个, 已完成${completedTasks}个, 进行中${inProgressTasks}个, 待开始${pendingTasks}个`);
        }
        
        // 添加表格排序功能
        function sortTable(tableId, columnIndex) {
            const table = document.getElementById(tableId);
            if (!table) return;
            
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            rows.sort((a, b) => {
                const aText = a.cells[columnIndex].textContent.trim();
                const bText = b.cells[columnIndex].textContent.trim();
                return aText.localeCompare(bText);
            });
            
            rows.forEach(row => tbody.appendChild(row));
        }
    </script>
</body>
</html>