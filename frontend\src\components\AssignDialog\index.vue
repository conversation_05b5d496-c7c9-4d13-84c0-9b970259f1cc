<template>
    <el-dialog v-model="dialogVisible" title="分配负责人" width="30%" append-to-body>
        <el-form label-width="80px">
            <el-form-item label="负责人" required>
                <el-select v-model="selectedOwnerId" placeholder="请选择负责人" filterable style="width: 100%">
                    <el-option v-for="item in userOptions" :key="item.value" :label="`${item.nickName} (${item.label}) `" :value="item.value" />
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { UserOption } from '@/types/user';
import { ElMessage } from 'element-plus';
import { ref, watch } from 'vue';
interface Props {
    modelValue: boolean;
    userOptions: UserOption[];
}

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue', 'confirm', 'cancel']);

const dialogVisible = ref(props.modelValue);
const selectedOwnerId = ref(0);

// 监听对话框可见性
watch(() => props.modelValue, (newVal: boolean) => {
    dialogVisible.value = newVal;
    if (newVal) {
        selectedOwnerId.value = 0; // 重置选中的负责人
        // 如果有用户选项数据，则默认选中第一个
        if (props.userOptions && props.userOptions.length > 0) {
            selectedOwnerId.value = props.userOptions[0].value;
        }
    }
});

watch(() => dialogVisible.value, (newVal: boolean) => {
    emit('update:modelValue', newVal);
});

// 监听用户选项变化
watch(() => props.userOptions, (newOptions) => {
    // 如果对话框是打开状态，且有数据，则设置默认选中第一项
    if (dialogVisible.value && newOptions && newOptions.length > 0 && !selectedOwnerId.value) {
        selectedOwnerId.value = newOptions[0].value;
    }
}, { immediate: true });

const handleConfirm = () => {
    if (!selectedOwnerId.value) {
        ElMessage.warning('请选择负责人');
        return;
    }
    emit('confirm', selectedOwnerId.value);
};

const handleCancel = () => {
    selectedOwnerId.value = 0;
    emit('cancel');
};
</script>

<style scoped>
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}
</style>
