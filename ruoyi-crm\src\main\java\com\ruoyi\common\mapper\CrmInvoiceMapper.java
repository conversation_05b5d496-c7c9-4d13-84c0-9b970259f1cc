package com.ruoyi.common.mapper;

import java.util.List;
import com.ruoyi.common.domain.CrmInvoice;
import com.ruoyi.common.domain.CrmInvoiceItem;
import com.ruoyi.common.domain.CrmInvoiceAttachment;

/**
 * 发票Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface CrmInvoiceMapper {
    /**
     * 查询发票
     * 
     * @param id 发票主键
     * @return 发票
     */
    public CrmInvoice selectCrmInvoiceById(Long id);

    /**
     * 查询发票列表
     * 
     * @param crmInvoice 发票
     * @return 发票集合
     */
    public List<CrmInvoice> selectCrmInvoiceList(CrmInvoice crmInvoice);

    /**
     * 新增发票
     * 
     * @param crmInvoice 发票
     * @return 结果
     */
    public int insertCrmInvoice(CrmInvoice crmInvoice);

    /**
     * 修改发票
     * 
     * @param crmInvoice 发票
     * @return 结果
     */
    public int updateCrmInvoice(CrmInvoice crmInvoice);

    /**
     * 删除发票
     * 
     * @param id 发票主键
     * @return 结果
     */
    public int deleteCrmInvoiceById(Long id);

    /**
     * 批量删除发票
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCrmInvoiceByIds(Long[] ids);

    /**
     * 批量删除发票明细
     * 
     * @param invoiceIds 发票ID集合
     * @return 结果
     */
    public int deleteCrmInvoiceItemByInvoiceIds(Long[] invoiceIds);
    
    /**
     * 批量新增发票明细
     * 
     * @param crmInvoiceItemList 发票明细列表
     * @return 结果
     */
    public int batchCrmInvoiceItem(List<CrmInvoiceItem> crmInvoiceItemList);
    
    /**
     * 通过发票主键删除发票明细信息
     * 
     * @param invoiceId 发票ID
     * @return 结果
     */
    public int deleteCrmInvoiceItemByInvoiceId(Long invoiceId);

    /**
     * 查询发票明细列表
     * 
     * @param invoiceId 发票ID
     * @return 发票明细集合
     */
    public List<CrmInvoiceItem> selectCrmInvoiceItemList(Long invoiceId);

    /**
     * 批量删除发票附件
     * 
     * @param invoiceIds 发票ID集合
     * @return 结果
     */
    public int deleteCrmInvoiceAttachmentByInvoiceIds(Long[] invoiceIds);
    
    /**
     * 批量新增发票附件
     * 
     * @param crmInvoiceAttachmentList 发票附件列表
     * @return 结果
     */
    public int batchCrmInvoiceAttachment(List<CrmInvoiceAttachment> crmInvoiceAttachmentList);
    
    /**
     * 通过发票主键删除发票附件信息
     * 
     * @param invoiceId 发票ID
     * @return 结果
     */
    public int deleteCrmInvoiceAttachmentByInvoiceId(Long invoiceId);

    /**
     * 查询发票附件列表
     * 
     * @param invoiceId 发票ID
     * @return 发票附件集合
     */
    public List<CrmInvoiceAttachment> selectCrmInvoiceAttachmentList(Long invoiceId);

    /**
     * 根据发票编号查询发票
     * 
     * @param invoiceNo 发票编号
     * @return 发票
     */
    public CrmInvoice selectCrmInvoiceByInvoiceNo(String invoiceNo);

    /**
     * 根据流程实例ID查询发票
     * 
     * @param processInstanceId 流程实例ID
     * @return 发票
     */
    public CrmInvoice selectCrmInvoiceByProcessInstanceId(String processInstanceId);

    /**
     * 根据报价单ID查询发票列表
     * 
     * @param quotationId 报价单ID
     * @return 发票集合
     */
    public List<CrmInvoice> selectCrmInvoiceByQuotationId(Long quotationId);
}