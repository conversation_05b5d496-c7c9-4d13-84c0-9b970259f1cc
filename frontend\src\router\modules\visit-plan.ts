import { RouteRecordRaw } from 'vue-router';
import Layout from '@/layout/index.vue';

const visitPlanRouter: RouteRecordRaw = {
  path: '/crm',
  component: Layout,
  redirect: '/crm/visitPlan',
  name: 'CRM',
  meta: {
    title: 'CRM管理',
    icon: 'monitor',
    alwaysShow: true
  },
  children: [
    {
      path: 'visitPlan',
      component: () => import('@/views/crm/visitPlan/index.vue'),
      name: 'VisitPlan',
      meta: {
        title: '拜访计划',
        icon: 'calendar',
        roles: ['admin', 'crm:visitPlan:list']
      }
    },
    {
      path: 'visitPlanReminder',
      component: () => import('@/views/crm/visitPlanReminder/index.vue'),
      name: 'VisitPlanReminder',
      meta: {
        title: '拜访提醒管理',
        icon: 'bell',
        roles: ['admin', 'crm:visitPlanReminder:list']
      }
    },
    {
      path: 'visitPlanLog',
      component: () => import('@/views/crm/visitPlanLog/index.vue'),
      name: 'VisitPlanLog',
      meta: {
        title: '拜访日志管理',
        icon: 'documentation',
        roles: ['admin', 'crm:visitPlanLog:list']
      }
    }
  ]
};

export default visitPlanRouter;
