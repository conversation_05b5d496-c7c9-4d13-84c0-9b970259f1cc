package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CrmBusinessApProcessInstanceMapper;
import com.ruoyi.common.domain.entity.CrmBusinessApProcessInstance;
import com.ruoyi.system.service.ICrmBusinessApProcessInstanceService;

/**
 * 流程实例Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class CrmBusinessApProcessInstanceServiceImpl implements ICrmBusinessApProcessInstanceService 
{
    @Autowired
    private CrmBusinessApProcessInstanceMapper crmBusinessApProcessInstanceMapper;

    /**
     * 查询流程实例
     * 
     * @param instanceId 流程实例主键
     * @return 流程实例
     */
    @Override
    public CrmBusinessApProcessInstance selectCrmBusinessApProcessInstanceByInstanceId(Long instanceId)
    {
        return crmBusinessApProcessInstanceMapper.selectCrmBusinessApProcessInstanceByInstanceId(instanceId);
    }

    /**
     * 查询流程实例列表
     * 
     * @param crmBusinessApProcessInstance 流程实例
     * @return 流程实例
     */
    @Override
    public List<CrmBusinessApProcessInstance> selectCrmBusinessApProcessInstanceList(CrmBusinessApProcessInstance crmBusinessApProcessInstance)
    {
        return crmBusinessApProcessInstanceMapper.selectCrmBusinessApProcessInstanceList(crmBusinessApProcessInstance);
    }

    /**
     * 新增流程实例
     * 
     * @param crmBusinessApProcessInstance 流程实例
     * @return 结果
     */
    @Override
    public int insertCrmBusinessApProcessInstance(CrmBusinessApProcessInstance crmBusinessApProcessInstance)
    {
        return crmBusinessApProcessInstanceMapper.insertCrmBusinessApProcessInstance(crmBusinessApProcessInstance);
    }

    /**
     * 修改流程实例
     * 
     * @param crmBusinessApProcessInstance 流程实例
     * @return 结果
     */
    @Override
    public int updateCrmBusinessApProcessInstance(CrmBusinessApProcessInstance crmBusinessApProcessInstance)
    {
        return crmBusinessApProcessInstanceMapper.updateCrmBusinessApProcessInstance(crmBusinessApProcessInstance);
    }

    /**
     * 批量删除流程实例
     * 
     * @param instanceIds 需要删除的流程实例主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessApProcessInstanceByInstanceIds(Long[] instanceIds)
    {
        return crmBusinessApProcessInstanceMapper.deleteCrmBusinessApProcessInstanceByInstanceIds(instanceIds);
    }

    /**
     * 删除流程实例信息
     * 
     * @param instanceId 流程实例主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessApProcessInstanceByInstanceId(Long instanceId)
    {
        return crmBusinessApProcessInstanceMapper.deleteCrmBusinessApProcessInstanceByInstanceId(instanceId);
    }
}
