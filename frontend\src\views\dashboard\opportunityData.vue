<template>
    <div class="chart-container">
        <!-- ECharts Line Chart -->
        <div ref="chart" class="opportunity-chart"></div>

        <!-- Element UI Table -->
        <el-table :data="[opportunityRow]" style="width: 100%; margin-top: 20px;">
            <el-table-column v-for="item in opportunityData" :key="item.month" :prop="item.month" :label="item.month"
                width="100">
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    data() {
        return {
            chart: null,
            opportunityData: [
                { month: '2024年1月', quantity: 10 },
                { month: '2024年2月', quantity: 15 },
                { month: '2024年3月', quantity: 8 },
                { month: '2024年4月', quantity: 20 },
                { month: '2024年5月', quantity: 18 },
                { month: '2024年6月', quantity: 25 },
                { month: '2024年7月', quantity: 22 },
                { month: '2024年8月', quantity: 30 },
                { month: '2024年9月', quantity: 12 },
                { month: '2024年10月', quantity: 27 },
                { month: '2024年11月', quantity: 23 },
                { month: '2024年12月', quantity: 28 },
            ]
        };
    },
    computed: {
        opportunityRow() {
            const row = {};
            this.opportunityData.forEach(item => {
                row[item.month] = item.quantity;
            });
            return row;
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart();
            window.addEventListener('resize', this.handleResize);
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.chart) {
            this.chart.dispose();
            this.chart = null;
        }
    },
    methods: {
        handleResize() {
            if (this.chart) {
                this.chart.resize();
            }
        },
        initChart() {
            if (this.chart) {
                this.chart.dispose();
            }

            const chartDom = this.$refs.chart;
            if (!chartDom) return;

            this.chart = echarts.init(chartDom);
            const option = {
                xAxis: {
                    type: 'category',
                    data: this.opportunityData.map(item => item.month)
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        data: this.opportunityData.map(item => item.quantity),
                        type: 'line'
                    }
                ],
                tooltip: {
                    trigger: 'axis'
                }
            };
            this.chart.setOption(option);
        }
    }
};
</script>

<style scoped>
.chart-container {
    width: 100%;
    height: 100%;
}
.opportunity-chart {
    width: 100%;
    height: 400px;
}
</style>
