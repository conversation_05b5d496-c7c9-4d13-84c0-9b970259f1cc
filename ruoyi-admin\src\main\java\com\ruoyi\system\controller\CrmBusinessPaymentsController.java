package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.CrmBusinessPayments;
import com.ruoyi.system.service.ICrmBusinessPaymentsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.*;

/**
 * 回款Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Api(tags = "回款管理")
@RestController
@RequestMapping("/system/payments")
public class CrmBusinessPaymentsController extends BaseController {
    @Autowired
    private ICrmBusinessPaymentsService crmBusinessPaymentsService;

    /**
     * 查询回款列表
     */
    @ApiOperation("查询回款列表")
    @PreAuthorize("@ss.hasPermi('system:payments:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusinessPayments crmBusinessPayments) {
        startPage();
        List<CrmBusinessPayments> list = crmBusinessPaymentsService.selectCrmBusinessPaymentsList(crmBusinessPayments);
        return getDataTable(list);
    }

    /**
     * 导出回款列表
     */
    @ApiOperation("导出回款列表")
    @PreAuthorize("@ss.hasPermi('system:payments:export')")
    @Log(title = "回款", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmBusinessPayments crmBusinessPayments) {
        List<CrmBusinessPayments> list = crmBusinessPaymentsService.selectCrmBusinessPaymentsList(crmBusinessPayments);
        ExcelUtil<CrmBusinessPayments> util = new ExcelUtil<CrmBusinessPayments>(CrmBusinessPayments.class);
        util.exportExcel(response, list, "回款数据");
    }

    /**
     * 获取回款详细信息
     */
    @ApiOperation("获取回款详细信息")
    @PreAuthorize("@ss.hasPermi('system:payments:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("回款ID") @PathVariable("id") Long id) {
        return success(crmBusinessPaymentsService.selectCrmBusinessPaymentsById(id));
    }

    /**
     * 新增回款
     */
    @ApiOperation("新增回款")
    @PreAuthorize("@ss.hasPermi('system:payments:add')")
    @Log(title = "回款", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("回款信息") @RequestBody CrmBusinessPayments crmBusinessPayments) {
        return toAjax(crmBusinessPaymentsService.insertCrmBusinessPayments(crmBusinessPayments));
    }

    /**
     * 修改回款
     */
    @ApiOperation("修改回款")
    @PreAuthorize("@ss.hasPermi('system:payments:edit')")
    @Log(title = "回款", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("回款信息") @RequestBody CrmBusinessPayments crmBusinessPayments) {
        return toAjax(crmBusinessPaymentsService.updateCrmBusinessPayments(crmBusinessPayments));
    }

    /**
     * 删除回款
     */
    @ApiOperation("删除回款")
    @PreAuthorize("@ss.hasPermi('system:payments:remove')")
    @Log(title = "回款", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("回款ID数组") @PathVariable Long[] ids) {
        return toAjax(crmBusinessPaymentsService.deleteCrmBusinessPaymentsByIds(ids));
    }
}
