<template>
    <div ref="mapChart" style="width: 100%; height: 400px;"></div>
</template>

<script setup>
// import chinaMap from '@echarts/map/json/china.json';
import * as echarts from 'echarts';
import { nextTick, onMounted, ref } from 'vue';

const mapChartRef = ref(null);

const initChart = () => {
    if (mapChartRef.value) {
        const mapChart = echarts.init(mapChartRef.value);
        echarts.registerMap('china', chinaMap);

        const option = {
            title: {
                text: '中国地图'
            },
            tooltip: {
                trigger: 'item'
            },
            visualMap: {
                min: 0,
                max: 1000,
                left: 'left',
                top: 'bottom',
                text: ['高', '低'],
                calculable: true
            },
            series: [
                {
                    name: '数据名称',
                    type: 'map',
                    map: 'china',
                    roam: true,
                    label: {
                        show: true
                    },
                    data: [
                        { name: '北京', value: 100 },
                        { name: '上海', value: 200 }
                        // 更多数据
                    ]
                }
            ]
        };

        mapChart.setOption(option);
    }
};

onMounted(() => {
    nextTick(() => {
        initChart();
    });
});
</script>

<style scoped>
.chart {
    width: 100%;
    height: 400px;
}
</style>