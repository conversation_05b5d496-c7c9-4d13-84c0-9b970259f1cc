package com.ruoyi.common.mapper;

import java.util.List;
import com.ruoyi.common.domain.CrmQuotation;
import com.ruoyi.common.domain.CrmQuotationItem;

/**
 * 报价单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface CrmQuotationMapper {
    /**
     * 查询报价单
     * 
     * @param id 报价单主键
     * @return 报价单
     */
    public CrmQuotation selectCrmQuotationById(Long id);

    /**
     * 查询报价单列表
     * 
     * @param crmQuotation 报价单
     * @return 报价单集合
     */
    public List<CrmQuotation> selectCrmQuotationList(CrmQuotation crmQuotation);

    /**
     * 新增报价单
     * 
     * @param crmQuotation 报价单
     * @return 结果
     */
    public int insertCrmQuotation(CrmQuotation crmQuotation);

    /**
     * 修改报价单
     * 
     * @param crmQuotation 报价单
     * @return 结果
     */
    public int updateCrmQuotation(CrmQuotation crmQuotation);

    /**
     * 删除报价单
     * 
     * @param id 报价单主键
     * @return 结果
     */
    public int deleteCrmQuotationById(Long id);

    /**
     * 批量删除报价单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCrmQuotationByIds(Long[] ids);

    /**
     * 批量删除报价单明细
     * 
     * @param quotationIds 报价单ID集合
     * @return 结果
     */
    public int deleteCrmQuotationItemByQuotationIds(Long[] quotationIds);
    
    /**
     * 批量新增报价单明细
     * 
     * @param crmQuotationItemList 报价单明细列表
     * @return 结果
     */
    public int batchCrmQuotationItem(List<CrmQuotationItem> crmQuotationItemList);
    
    /**
     * 通过报价单主键删除报价单明细信息
     * 
     * @param quotationId 报价单ID
     * @return 结果
     */
    public int deleteCrmQuotationItemByQuotationId(Long quotationId);

    /**
     * 查询报价单明细列表
     * 
     * @param quotationId 报价单ID
     * @return 报价单明细集合
     */
    public List<CrmQuotationItem> selectCrmQuotationItemList(Long quotationId);

    /**
     * 根据报价单编号查询报价单
     * 
     * @param quotationNo 报价单编号
     * @return 报价单
     */
    public CrmQuotation selectCrmQuotationByQuotationNo(String quotationNo);

    /**
     * 根据流程实例ID查询报价单
     * 
     * @param processInstanceId 流程实例ID
     * @return 报价单
     */
    public CrmQuotation selectCrmQuotationByProcessInstanceId(String processInstanceId);
}