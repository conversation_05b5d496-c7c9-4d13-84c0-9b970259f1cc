package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.CrmBusinessContractUserRelations;
import com.ruoyi.system.service.ICrmBusinessContractUserRelationsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.*;

/**
 * 合同用户关系Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Api(tags = "合同用户关系管理")
@RestController
@RequestMapping("/system/relations")
public class CrmBusinessContractUserRelationsController extends BaseController {
    @Autowired
    private ICrmBusinessContractUserRelationsService crmBusinessContractUserRelationsService;

    /**
     * 查询合同用户关系列表
     */
    @ApiOperation("查询合同用户关系列表")
    @PreAuthorize("@ss.hasPermi('system:relations:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusinessContractUserRelations crmBusinessContractUserRelations) {
        startPage();
        List<CrmBusinessContractUserRelations> list = crmBusinessContractUserRelationsService
                .selectCrmBusinessContractUserRelationsList(crmBusinessContractUserRelations);
        return getDataTable(list);
    }

    /**
     * 导出合同用户关系列表
     */
    @ApiOperation("导出合同用户关系列表")
    @PreAuthorize("@ss.hasPermi('system:relations:export')")
    @Log(title = "合同用户关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,
            CrmBusinessContractUserRelations crmBusinessContractUserRelations) {
        List<CrmBusinessContractUserRelations> list = crmBusinessContractUserRelationsService
                .selectCrmBusinessContractUserRelationsList(crmBusinessContractUserRelations);
        ExcelUtil<CrmBusinessContractUserRelations> util = new ExcelUtil<CrmBusinessContractUserRelations>(
                CrmBusinessContractUserRelations.class);
        util.exportExcel(response, list, "合同用户关系数据");
    }

    /**
     * 获取合同用户关系详细信息
     */
    @ApiOperation("获取合同用户关系详细信息")
    @PreAuthorize("@ss.hasPermi('system:relations:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("合同用户关系ID") @PathVariable("id") Long id) {
        return success(crmBusinessContractUserRelationsService.selectCrmBusinessContractUserRelationsById(id));
    }

    /**
     * 新增合同用户关系
     */
    @ApiOperation("新增合同用户关系")
    @PreAuthorize("@ss.hasPermi('system:relations:add')")
    @Log(title = "合同用户关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(
            @ApiParam("合同用户关系信息") @RequestBody CrmBusinessContractUserRelations crmBusinessContractUserRelations) {
        return toAjax(crmBusinessContractUserRelationsService
                .insertCrmBusinessContractUserRelations(crmBusinessContractUserRelations));
    }

    /**
     * 修改合同用户关系
     */
    @ApiOperation("修改合同用户关系")
    @PreAuthorize("@ss.hasPermi('system:relations:edit')")
    @Log(title = "合同用户关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(
            @ApiParam("合同用户关系信息") @RequestBody CrmBusinessContractUserRelations crmBusinessContractUserRelations) {
        return toAjax(crmBusinessContractUserRelationsService
                .updateCrmBusinessContractUserRelations(crmBusinessContractUserRelations));
    }

    /**
     * 删除合同用户关系
     */
    @ApiOperation("删除合同用户关系")
    @PreAuthorize("@ss.hasPermi('system:relations:remove')")
    @Log(title = "合同用户关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("合同用户关系ID列表") @PathVariable Long[] ids) {
        return toAjax(crmBusinessContractUserRelationsService.deleteCrmBusinessContractUserRelationsByIds(ids));
    }
}
