<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM对账单管理邮件模板</title>
    <style>
        /* 邮件兼容性样式 */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #2c3e50;
            line-height: 1.6;
        }
        
        .email-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #ffffff;
        }
        
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .email-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        
        .email-content {
            padding: 20px;
        }
        
        .section-title {
            color: #667eea;
            font-size: 18px;
            font-weight: bold;
            margin: 30px 0 15px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .stats-table td {
            padding: 15px;
            text-align: center;
            background-color: #f8f9ff;
            border: 1px solid #e0e6ff;
        }
        
        .stats-value {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
        }
        
        .stats-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: #ffffff;
        }
        
        .data-table th {
            background-color: #667eea;
            color: white;
            padding: 12px 8px;
            font-size: 12px;
            font-weight: bold;
            text-align: left;
        }
        
        .data-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #e9ecef;
            font-size: 12px;
        }
        
        .data-table tr:nth-child(even) {
            background-color: #f8f9ff;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-approved {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-invoiced {
            background-color: #e7f3ff;
            color: #0066cc;
        }
        
        .status-paid {
            background-color: #d1f2eb;
            color: #00875a;
        }
        
        .amount {
            font-weight: bold;
            color: #28a745;
        }
        
        .record-no {
            font-family: 'Consolas', monospace;
            color: #667eea;
            font-weight: bold;
        }
        
        .customer-name {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .form-section {
            background-color: #f8f9ff;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        
        .form-row {
            display: table;
            width: 100%;
            margin-bottom: 15px;
        }
        
        .form-group {
            display: table-cell;
            width: 50%;
            padding-right: 10px;
        }
        
        .form-group:last-child {
            padding-right: 0;
            padding-left: 10px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            color: #2c3e50;
            font-weight: bold;
            font-size: 12px;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-size: 12px;
            box-sizing: border-box;
        }
        
        .form-select {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-size: 12px;
            background-color: white;
        }
        
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .summary-table th {
            background-color: #667eea;
            color: white;
            padding: 10px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .summary-table td {
            padding: 10px;
            border-bottom: 1px solid #e9ecef;
            font-size: 12px;
        }
        
        .summary-total {
            background-color: #667eea;
            color: white;
            font-weight: bold;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn-primary {
            background-color: #667eea;
            color: white;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .email-footer {
            background-color: #f8f9ff;
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #6c757d;
        }
        
        /* 响应式设计 */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
            }
            
            .form-group {
                display: block;
                width: 100%;
                padding-right: 0;
                padding-left: 0;
                margin-bottom: 10px;
            }
            
            .data-table {
                font-size: 10px;
            }
            
            .data-table th,
            .data-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- 邮件头部 -->
        <div class="email-header">
            <h1>📊 CRM对账单管理</h1>
            <p>现代化对账单管理系统</p>
        </div>

        <!-- 邮件内容 -->
        <div class="email-content">
            <!-- 1. 对账单概览 -->
            <div class="section-title">📋 对账单概览</div>
            
            <table class="stats-table">
                <tr>
                    <td>
                        <div class="stats-value">156</div>
                        <div class="stats-label">本月对账单</div>
                    </td>
                    <td>
                        <div class="stats-value">23</div>
                        <div class="stats-label">待审核</div>
                    </td>
                    <td>
                        <div class="stats-value">¥2.3M</div>
                        <div class="stats-label">对账金额</div>
                    </td>
                    <td>
                        <div class="stats-value">89%</div>
                        <div class="stats-label">完成率</div>
                    </td>
                </tr>
            </table>

            <!-- 2. 对账单列表 -->
            <div class="section-title">📊 对账单列表</div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>对账单编号</th>
                        <th>客户名称</th>
                        <th>对账金额</th>
                        <th>预收抵扣</th>
                        <th>净对账额</th>
                        <th>状态</th>
                        <th>创建时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="record-no">REC20250702000001</span></td>
                        <td><span class="customer-name">杭州科德旺有限公司</span></td>
                        <td><span class="amount">¥156,800.00</span></td>
                        <td><span class="amount">¥20,000.00</span></td>
                        <td><span class="amount">¥136,800.00</span></td>
                        <td><span class="status-badge status-approved">已审核</span></td>
                        <td>2025-07-02 09:15</td>
                    </tr>
                    <tr>
                        <td><span class="record-no">REC20250701000002</span></td>
                        <td><span class="customer-name">上海泰科科技有限公司</span></td>
                        <td><span class="amount">¥89,500.00</span></td>
                        <td><span class="amount">¥0.00</span></td>
                        <td><span class="amount">¥89,500.00</span></td>
                        <td><span class="status-badge status-invoiced">已开票</span></td>
                        <td>2025-07-01 14:30</td>
                    </tr>
                    <tr>
                        <td><span class="record-no">REC20250701000001</span></td>
                        <td><span class="customer-name">北京智能制造股份有限公司</span></td>
                        <td><span class="amount">¥268,900.00</span></td>
                        <td><span class="amount">¥50,000.00</span></td>
                        <td><span class="amount">¥218,900.00</span></td>
                        <td><span class="status-badge status-paid">已回款</span></td>
                        <td>2025-07-01 10:45</td>
                    </tr>
                </tbody>
            </table>

            <!-- 3. 创建对账单表单 -->
            <div class="section-title">➕ 创建对账单</div>
            
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">对账单编号</label>
                        <input type="text" class="form-input" value="REC20250702000003" readonly />
                    </div>
                    <div class="form-group">
                        <label class="form-label">选择客户 *</label>
                        <select class="form-select">
                            <option>请选择客户...</option>
                            <option selected>杭州科德旺有限公司</option>
                            <option>上海泰科科技有限公司</option>
                            <option>北京智能制造股份有限公司</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">对账周期</label>
                        <input type="text" class="form-input" value="2025年7月" />
                    </div>
                    <div class="form-group">
                        <label class="form-label">负责人</label>
                        <select class="form-select">
                            <option selected>张经理</option>
                            <option>李经理</option>
                            <option>王经理</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 4. 订单选择 -->
            <div class="section-title">📦 关联订单</div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>订单编号</th>
                        <th>订单描述</th>
                        <th>订单日期</th>
                        <th>订单状态</th>
                        <th>订单金额</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="record-no">ORD20250701001</span></td>
                        <td>3D打印材料采购</td>
                        <td>2025-07-01</td>
                        <td>已发货</td>
                        <td><span class="amount">¥45,600.00</span></td>
                    </tr>
                    <tr>
                        <td><span class="record-no">ORD20250628002</span></td>
                        <td>设备维护服务合同</td>
                        <td>2025-06-28</td>
                        <td>已完成</td>
                        <td><span class="amount">¥68,900.00</span></td>
                    </tr>
                    <tr>
                        <td><span class="record-no">ORD20250625003</span></td>
                        <td>3D打印设备采购</td>
                        <td>2025-06-25</td>
                        <td>已交付</td>
                        <td><span class="amount">¥129,800.00</span></td>
                    </tr>
                </tbody>
            </table>

            <!-- 5. 预收款抵扣 -->
            <div class="section-title">💰 预收款抵扣</div>
            
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">客户预收款余额</label>
                        <input type="text" class="form-input" value="¥64,320.00" readonly />
                    </div>
                    <div class="form-group">
                        <label class="form-label">本次抵扣金额</label>
                        <input type="number" class="form-input" value="20000.00" />
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">抵扣后余额</label>
                        <input type="text" class="form-input" value="¥44,320.00" readonly />
                    </div>
                    <div class="form-group">
                        <label class="form-label">抵扣说明</label>
                        <input type="text" class="form-input" value="7月份订单预收款抵扣" />
                    </div>
                </div>
            </div>

            <!-- 6. 金额汇总 -->
            <div class="section-title">💰 对账金额汇总</div>
            
            <table class="summary-table">
                <thead>
                    <tr>
                        <th>项目</th>
                        <th>金额</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>订单总金额</td>
                        <td><span class="amount">¥114,500.00</span></td>
                        <td>包含所有关联订单</td>
                    </tr>
                    <tr>
                        <td>手动添加</td>
                        <td><span class="amount">¥8,000.00</span></td>
                        <td>额外费用项目</td>
                    </tr>
                    <tr>
                        <td>小计金额</td>
                        <td><span class="amount">¥122,500.00</span></td>
                        <td>订单+手动添加</td>
                    </tr>
                    <tr>
                        <td>预收抵扣</td>
                        <td><span class="amount">-¥20,000.00</span></td>
                        <td>预收款抵扣</td>
                    </tr>
                    <tr class="summary-total">
                        <td>净对账金额</td>
                        <td><span class="amount">¥102,500.00</span></td>
                        <td>最终对账金额</td>
                    </tr>
                </tbody>
            </table>

            <!-- 7. 操作按钮 -->
            <div style="text-align: center; margin: 30px 0; padding: 20px; background-color: #f8f9ff; border-radius: 8px;">
                <a href="#" class="btn btn-secondary">💾 保存草稿</a>
                <a href="#" class="btn btn-warning">📤 提交审核</a>
                <a href="#" class="btn btn-primary">✅ 创建对账单</a>
            </div>

            <!-- 8. 开票申请 -->
            <div class="section-title">📄 开票申请</div>
            
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">开票类型</label>
                        <select class="form-select">
                            <option selected>增值税专用发票</option>
                            <option>增值税普通发票</option>
                            <option>电子发票</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">发票金额</label>
                        <input type="number" class="form-input" value="102500.00" readonly />
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">税率</label>
                        <select class="form-select">
                            <option selected>13%</option>
                            <option>6%</option>
                            <option>0%</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">税额</label>
                        <input type="number" class="form-input" value="13325.00" readonly />
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">价税合计</label>
                        <input type="number" class="form-input" value="115825.00" readonly />
                    </div>
                    <div class="form-group">
                        <label class="form-label">开票日期</label>
                        <input type="date" class="form-input" value="2025-07-02" />
                    </div>
                </div>
            </div>

            <!-- 9. 回款管理 -->
            <div class="section-title">💰 回款管理</div>
            
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">选择对账单</label>
                        <select class="form-select">
                            <option>请选择对账单...</option>
                            <option selected>REC20250702000001 - 杭州科德旺有限公司 - ¥102,500.00</option>
                            <option>REC20250701000002 - 上海泰科科技有限公司 - ¥89,500.00</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">回款金额</label>
                        <input type="number" class="form-input" value="102500.00" />
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">回款方式</label>
                        <select class="form-select">
                            <option>银行转账</option>
                            <option selected>支票</option>
                            <option>现金</option>
                            <option>承兑汇票</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">回款日期</label>
                        <input type="date" class="form-input" value="2025-07-02" />
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">银行信息</label>
                        <input type="text" class="form-input" value="中国银行杭州分行" />
                    </div>
                    <div class="form-group">
                        <label class="form-label">凭证号码</label>
                        <input type="text" class="form-input" placeholder="回款凭证号码" />
                    </div>
                </div>
            </div>

            <!-- 10. 回款分配 -->
            <div class="section-title">📊 回款金额分配</div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>订单编号</th>
                        <th>订单描述</th>
                        <th>分配金额</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="record-no">ORD20250701001</span></td>
                        <td>3D打印材料采购</td>
                        <td><span class="amount">¥45,600.00</span></td>
                    </tr>
                    <tr>
                        <td><span class="record-no">ORD20250628002</span></td>
                        <td>设备维护服务合同</td>
                        <td><span class="amount">¥56,900.00</span></td>
                    </tr>
                    <tr style="background-color: #667eea; color: white; font-weight: bold;">
                        <td colspan="2">总计分配金额</td>
                        <td><span class="amount">¥102,500.00</span></td>
                    </tr>
                </tbody>
            </table>

            <!-- 11. 操作按钮 -->
            <div style="text-align: center; margin: 30px 0; padding: 20px; background-color: #f8f9ff; border-radius: 8px;">
                <a href="#" class="btn btn-secondary">💾 保存</a>
                <a href="#" class="btn btn-primary">✅ 确认回款</a>
            </div>
        </div>

        <!-- 邮件底部 -->
        <div class="email-footer">
            <p>📧 此邮件由CRM对账单管理系统自动生成</p>
            <p>📞 如有疑问，请联系系统管理员</p>
            <p>© 2025 CRM系统 - 对账单管理模块</p>
        </div>
    </div>
</body>
</html>
