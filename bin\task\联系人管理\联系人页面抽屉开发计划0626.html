<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人页面抽屉开发计划</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
            margin-bottom: 10px;
        }
        h4 {
            color: #27ae60;
            margin-top: 20px;
            margin-bottom: 8px;
        }
        .status-completed {
            color: #27ae60;
            font-weight: bold;
        }
        .status-pending {
            color: #e74c3c;
            font-weight: bold;
        }
        .priority-high {
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .priority-medium {
            background: #f39c12;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .priority-low {
            background: #95a5a6;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        .risk-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        ul, ol {
            margin: 10px 0;
            padding-left: 30px;
        }
        li {
            margin: 5px 0;
        }
        .phase-box {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>联系人页面抽屉开发计划</h1>
        
        <h2>项目概述</h2>
        <p>基于参考图片和现有的线索管理模块（AssociationManagement），为联系人管理页面开发完整的抽屉功能。当前联系人页面已有基本的表格展示，但抽屉内容基本为空，需要参考线索管理的实现方式来完善。</p>
        
        <h2>当前状态分析</h2>
        
        <h3>已完成部分</h3>
        <ul>
            <li><span class="status-completed">✅</span> 联系人主页面表格展示</li>
            <li><span class="status-completed">✅</span> 基础的 common-drawer 组件集成</li>
            <li><span class="status-completed">✅</span> 基本的配置文件结构（config/index.ts）</li>
            <li><span class="status-completed">✅</span> 新建联系人对话框功能</li>
        </ul>
        
        <h3>待开发部分</h3>
        <ul>
            <li><span class="status-pending">❌</span> 联系人抽屉内的各个Tab组件</li>
            <li><span class="status-pending">❌</span> 联系人详情头部组件（头部组件类似线索详情头部组件）</li>
            <li><span class="status-pending">❌</span> 抽屉配置的完善</li>
        </ul>
        
        <h2>开发计划</h2>
        
        <div class="phase-box">
            <h3>第一阶段：创建基础Tab组件结构</h3>
            
            <h4>1.1 创建tabs目录和基础组件文件</h4>
            <p>需要创建以下组件文件：</p>
            <ul>
                <li><code>ContactHeaderTab.vue</code> - 联系人头部信息组件</li>
                <li><code>ContactDetailsTab.vue</code> - 联系人详细资料组件</li>
                <li><code>ContactActivityTab.vue</code> - 联系人活动记录组件</li>
                <li><code>ContactAttachmentsTab.vue</code> - 联系人附件管理组件</li>
                <li><code>ContactOperationsTab.vue</code> - 联系人操作记录组件</li>
            </ul>
            
            <h4>1.2 参考线索管理的组件结构</h4>
            <p>基于 <code>AssociationManagement/tabs/</code> 下的组件：</p>
            <ul>
                <li><code>LeadHeaderTab.vue</code> → <code>ContactHeaderTab.vue</code></li>
                <li><code>LeadDetailsTab.vue</code> → <code>ContactDetailsTab.vue</code></li>
                <li><code>LeadActivityTab.vue</code> → <code>ContactActivityTab.vue</code></li>
                <li><code>LeadAttachmentsTab.vue</code> → <code>ContactAttachmentsTab.vue</code></li>
                <li><code>LeadOperationsTab.vue</code> → <code>ContactOperationsTab.vue</code></li>
            </ul>
        </div>
        
        <div class="phase-box">
            <h3>第二阶段：实现核心功能组件</h3>
            
            <h4>2.1 ContactHeaderTab.vue <span class="priority-high">优先级：高</span></h4>
            <p><strong>功能需求：</strong></p>
            <ul>
                <li>显示联系人基本信息（姓名、职位、电话、邮箱）</li>
                <li>支持快速编辑功能（双击编辑）</li>
                <li>显示所属客户信息</li>
                <li>操作按钮区域（编辑、转移、删除等）</li>
            </ul>
            <p><strong>技术实现：</strong></p>
            <ul>
                <li>参考 <code>LeadHeaderTab.vue</code> 的布局和交互逻辑</li>
                <li>适配联系人特有的字段结构</li>
                <li>实现内联编辑功能</li>
            </ul>
            
            <h4>2.2 ContactDetailsTab.vue <span class="priority-high">优先级：高</span></h4>
            <p><strong>功能需求：</strong></p>
            <ul>
                <li>基本信息展示和编辑</li>
                <li>联系方式管理</li>
                <li>客户关联信息</li>
                <li>备注信息管理</li>
            </ul>
            <p><strong>技术实现：</strong></p>
            <ul>
                <li>使用 <code>BaseDetailsTab</code> 组件</li>
                <li>配置联系人特有的字段</li>
                <li>实现表单验证和数据更新</li>
            </ul>
            
            <h4>2.3 ContactActivityTab.vue <span class="priority-medium">优先级：中</span></h4>
            <p><strong>功能需求：</strong></p>
            <ul>
                <li>显示联系人相关的活动记录</li>
                <li>支持添加跟进记录</li>
                <li>活动类型分类（电话、邮件、会议、拜访等）</li>
                <li>时间线展示</li>
            </ul>
            <p><strong>技术实现：</strong></p>
            <ul>
                <li>参考 <code>LeadActivityTab.vue</code> 的时间线组件</li>
                <li>适配联系人活动数据结构</li>
                <li>实现活动记录的增删改查</li>
            </ul>
        </div>
        
        <div class="phase-box">
            <h3>第三阶段：完善辅助功能</h3>
            
            <h4>3.1 ContactAttachmentsTab.vue <span class="priority-medium">优先级：中</span></h4>
            <p><strong>功能需求：</strong></p>
            <ul>
                <li>文件上传功能</li>
                <li>附件列表展示</li>
                <li>文件下载和删除</li>
                <li>文件类型图标显示</li>
            </ul>
            <p><strong>技术实现：</strong></p>
            <ul>
                <li>复用 <code>LeadAttachmentsTab.vue</code> 的上传逻辑</li>
                <li>适配联系人模块的文件管理API</li>
                <li>实现文件预览功能</li>
            </ul>
            
            <h4>3.2 ContactOperationsTab.vue <span class="priority-low">优先级：低</span></h4>
            <p><strong>功能需求：</strong></p>
            <ul>
                <li>操作日志记录</li>
                <li>操作类型分类</li>
                <li>操作人员信息</li>
                <li>操作时间排序</li>
            </ul>
            <p><strong>技术实现：</strong></p>
            <ul>
                <li>参考 <code>LeadOperationsTab.vue</code> 的表格展示</li>
                <li>集成系统操作日志API</li>
                <li>实现操作记录的筛选和搜索</li>
            </ul>
        </div>
        
        <div class="phase-box">
            <h3>第四阶段：配置文件完善</h3>
            
            <h4>4.1 更新 config/index.ts</h4>
            <p><strong>需要完善的配置：</strong></p>
            <div class="code-block">
// 抽屉配置更新
export const drawerConfig: DrawerConfig = {
    menuItems: [
        {
            key: 'details',
            label: '详细资料',
            icon: 'Document',
            component: markRaw(ContactDetailsTab)
        },
        {
            key: 'activity',
            label: '活动记录',
            icon: 'Timer',
            component: markRaw(ContactActivityTab)
        },
        {
            key: 'attachments',
            label: '附件',
            icon: 'Paperclip',
            component: markRaw(ContactAttachmentsTab),
            badge: true
        },
        {
            key: 'operations',
            label: '操作记录',
            icon: 'List',
            component: markRaw(ContactOperationsTab)
        }
    ]
};
            </div>
            
            <h4>4.2 更新主页面集成</h4>
            <p><strong>需要修改 ContactManagement/index.vue：</strong></p>
            <ul>
                <li>导入头部组件：<code>headerComponent</code></li>
                <li>更新 <code>localDrawerConfig</code> 配置</li>
                <li>完善抽屉操作按钮配置</li>
            </ul>
        </div>
        
        <div class="phase-box">
            <h3>第五阶段：API集成和数据联调</h3>
            
            <h4>5.1 API接口对接</h4>
            <ul>
                <li>联系人详情获取API</li>
                <li>联系人更新API</li>
                <li>活动记录相关API</li>
                <li>附件管理API</li>
                <li>操作日志API</li>
            </ul>
            
            <h4>5.2 数据流优化</h4>
            <ul>
                <li>实现组件间数据同步</li>
                <li>优化数据加载性能</li>
                <li>添加错误处理机制</li>
            </ul>
        </div>
        
        <h2>技术要点</h2>
        
        <h3>1. 组件复用策略</h3>
        <ul>
            <li>最大化复用线索管理的组件逻辑</li>
            <li>通过配置化方式适配不同实体类型</li>
            <li>保持UI风格的一致性</li>
        </ul>
        
        <h3>2. 数据结构适配</h3>
        <ul>
            <li>联系人实体字段映射</li>
            <li>API响应数据格式统一</li>
            <li>表单验证规则定制</li>
        </ul>
        
        <h3>3. 用户体验优化</h3>
        <ul>
            <li>快速编辑功能</li>
            <li>数据实时同步</li>
            <li>加载状态提示</li>
            <li>错误信息友好展示</li>
        </ul>
        
        <h2>开发时间估算</h2>
        
        <table>
            <thead>
                <tr>
                    <th>阶段</th>
                    <th>预估时间</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>第一阶段</td>
                    <td>0.5天</td>
                    <td>创建基础文件结构</td>
                </tr>
                <tr>
                    <td>第二阶段</td>
                    <td>2天</td>
                    <td>核心功能组件开发</td>
                </tr>
                <tr>
                    <td>第三阶段</td>
                    <td>1.5天</td>
                    <td>辅助功能完善</td>
                </tr>
                <tr>
                    <td>第四阶段</td>
                    <td>0.5天</td>
                    <td>配置文件更新</td>
                </tr>
                <tr>
                    <td>第五阶段</td>
                    <td>1天</td>
                    <td>API集成和联调</td>
                </tr>
                <tr style="background-color: #e8f4fd; font-weight: bold;">
                    <td><strong>总计</strong></td>
                    <td><strong>5.5天</strong></td>
                    <td>包含测试和优化时间</td>
                </tr>
            </tbody>
        </table>
        
        <h2>验收标准</h2>
        
        <h3>功能验收</h3>
        <ul>
            <li><span class="status-completed">✅</span> 联系人详情抽屉正常打开和关闭</li>
            <li><span class="status-completed">✅</span> 各个Tab页面正常切换和显示</li>
            <li><span class="status-completed">✅</span> 联系人信息编辑和保存功能正常</li>
            <li><span class="status-completed">✅</span> 活动记录添加和展示功能正常</li>
            <li><span class="status-completed">✅</span> 附件上传和管理功能正常</li>
            <li><span class="status-completed">✅</span> 操作记录正常记录和展示</li>
        </ul>
        
        <h3>性能验收</h3>
        <ul>
            <li><span class="status-completed">✅</span> 抽屉打开速度 < 500ms</li>
            <li><span class="status-completed">✅</span> 数据加载响应时间 < 1s</li>
            <li><span class="status-completed">✅</span> 文件上传进度正常显示</li>
        </ul>
        
        <h3>兼容性验收</h3>
        <ul>
            <li><span class="status-completed">✅</span> 主流浏览器兼容性测试通过</li>
            <li><span class="status-completed">✅</span> 响应式布局适配正常</li>
            <li><span class="status-completed">✅</span> 移动端基本可用</li>
        </ul>
        
        <h2>风险评估</h2>
        
        <div class="risk-box">
            <h3>技术风险</h3>
            <ul>
                <li><strong>中等风险</strong>：API接口可能需要调整适配</li>
                <li><strong>低风险</strong>：组件复用过程中的样式冲突</li>
            </ul>
            
            <h3>时间风险</h3>
            <ul>
                <li><strong>中等风险</strong>：如果API接口不稳定，可能影响联调时间</li>
                <li><strong>低风险</strong>：UI细节调整可能增加开发时间</li>
            </ul>
            
            <h3>解决方案</h3>
            <ul>
                <li>提前与后端确认API接口规范</li>
                <li>预留20%的缓冲时间用于问题解决</li>
                <li>采用渐进式开发，优先完成核心功能</li>
            </ul>
        </div>
        
        <div class="highlight">
            <h3>下一步行动：</h3>
            <ol>
                <li>确认开发计划和时间安排</li>
                <li>开始第一阶段的基础组件文件创建</li>
                <li>逐步实现各个Tab组件的功能</li>
                <li>持续进行功能测试和优化</li>
            </ol>
        </div>
        
        <hr>
        <p style="text-align: center; color: #7f8c8d; margin-top: 30px;">
            <em>文档生成时间：2024年12月</em><br>
            <em>项目：CRM系统 - 联系人管理模块</em>
        </p>
    </div>
</body>
</html>