package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmBusinessContractWatchlist;

/**
 * 合同关注Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface ICrmBusinessContractWatchlistService 
{
    /**
     * 查询合同关注
     * 
     * @param id 合同关注主键
     * @return 合同关注
     */
    public CrmBusinessContractWatchlist selectCrmBusinessContractWatchlistById(Long id);

    /**
     * 查询合同关注列表
     * 
     * @param crmBusinessContractWatchlist 合同关注
     * @return 合同关注集合
     */
    public List<CrmBusinessContractWatchlist> selectCrmBusinessContractWatchlistList(CrmBusinessContractWatchlist crmBusinessContractWatchlist);

    /**
     * 新增合同关注
     * 
     * @param crmBusinessContractWatchlist 合同关注
     * @return 结果
     */
    public int insertCrmBusinessContractWatchlist(CrmBusinessContractWatchlist crmBusinessContractWatchlist);

    /**
     * 修改合同关注
     * 
     * @param crmBusinessContractWatchlist 合同关注
     * @return 结果
     */
    public int updateCrmBusinessContractWatchlist(CrmBusinessContractWatchlist crmBusinessContractWatchlist);

    /**
     * 批量删除合同关注
     * 
     * @param ids 需要删除的合同关注主键集合
     * @return 结果
     */
    public int deleteCrmBusinessContractWatchlistByIds(Long[] ids);

    /**
     * 删除合同关注信息
     * 
     * @param id 合同关注主键
     * @return 结果
     */
    public int deleteCrmBusinessContractWatchlistById(Long id);
}
