package com.ruoyi.crm.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.domain.CrmQuotation;
import com.ruoyi.common.domain.CrmQuotationItem;
import com.ruoyi.common.mapper.CrmQuotationMapper;
import com.ruoyi.crm.service.ICrmQuotationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 报价单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Service
public class CrmQuotationServiceImpl implements ICrmQuotationService {
    private static final Logger logger = LoggerFactory.getLogger(CrmQuotationServiceImpl.class);

    @Autowired
    private CrmQuotationMapper crmQuotationMapper;

    /**
     * 查询报价单
     * 
     * @param id 报价单主键
     * @return 报价单
     */
    @Override
    public CrmQuotation selectCrmQuotationById(Long id) {
        return crmQuotationMapper.selectCrmQuotationById(id);
    }

    /**
     * 查询报价单列表
     * 
     * @param crmQuotation 报价单
     * @return 报价单
     */
    @Override
    public List<CrmQuotation> selectCrmQuotationList(CrmQuotation crmQuotation) {
        return crmQuotationMapper.selectCrmQuotationList(crmQuotation);
    }

    /**
     * 新增报价单
     * 
     * @param crmQuotation 报价单
     * @return 结果
     */
    @Transactional
    @Override
    public int insertCrmQuotation(CrmQuotation crmQuotation) {
        // 生成报价单编号
        if (StringUtils.isEmpty(crmQuotation.getQuotationNo())) {
            crmQuotation.setQuotationNo(generateQuotationNo());
        }
        
        // 计算总金额
        calculateTotalAmount(crmQuotation);
        
        // 设置默认状态
        if (StringUtils.isEmpty(crmQuotation.getStatus())) {
            crmQuotation.setStatus("draft");
        }
        if (StringUtils.isEmpty(crmQuotation.getApprovalStatus())) {
            crmQuotation.setApprovalStatus("pending");
        }
        if (StringUtils.isEmpty(crmQuotation.getCurrency())) {
            crmQuotation.setCurrency("CNY");
        }
        
        crmQuotation.setCreateTime(DateUtils.getNowDate());
        crmQuotation.setCreateBy(SecurityUtils.getUsername());
        
        int rows = crmQuotationMapper.insertCrmQuotation(crmQuotation);
        insertCrmQuotationItem(crmQuotation);
        
        logger.info("新增报价单: {}, ID: {}", crmQuotation.getQuotationName(), crmQuotation.getId());
        return rows;
    }

    /**
     * 修改报价单
     * 
     * @param crmQuotation 报价单
     * @return 结果
     */
    @Transactional
    @Override
    public int updateCrmQuotation(CrmQuotation crmQuotation) {
        // 计算总金额
        calculateTotalAmount(crmQuotation);
        
        crmQuotation.setUpdateTime(DateUtils.getNowDate());
        crmQuotation.setUpdateBy(SecurityUtils.getUsername());
        
        crmQuotationMapper.deleteCrmQuotationItemByQuotationId(crmQuotation.getId());
        insertCrmQuotationItem(crmQuotation);
        
        int rows = crmQuotationMapper.updateCrmQuotation(crmQuotation);
        
        logger.info("修改报价单: {}, ID: {}", crmQuotation.getQuotationName(), crmQuotation.getId());
        return rows;
    }

    /**
     * 批量删除报价单
     * 
     * @param ids 需要删除的报价单主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteCrmQuotationByIds(Long[] ids) {
        crmQuotationMapper.deleteCrmQuotationItemByQuotationIds(ids);
        int rows = crmQuotationMapper.deleteCrmQuotationByIds(ids);
        
        logger.info("批量删除报价单数量: {}", ids.length);
        return rows;
    }

    /**
     * 删除报价单信息
     * 
     * @param id 报价单主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteCrmQuotationById(Long id) {
        crmQuotationMapper.deleteCrmQuotationItemByQuotationId(id);
        int rows = crmQuotationMapper.deleteCrmQuotationById(id);
        
        logger.info("删除报价单 ID: {}", id);
        return rows;
    }

    /**
     * 新增报价单明细信息
     * 
     * @param crmQuotation 报价单对象
     */
    public void insertCrmQuotationItem(CrmQuotation crmQuotation) {
        List<CrmQuotationItem> crmQuotationItemList = crmQuotation.getQuotationItems();
        Long quotationId = crmQuotation.getId();
        if (!CollectionUtils.isEmpty(crmQuotationItemList)) {
            int sortOrder = 1;
            for (CrmQuotationItem crmQuotationItem : crmQuotationItemList) {
                crmQuotationItem.setQuotationId(quotationId);
                if (crmQuotationItem.getSortOrder() == null) {
                    crmQuotationItem.setSortOrder(sortOrder++);
                }
                // 计算明细金额
                if (crmQuotationItem.getQuantity() != null && crmQuotationItem.getUnitPrice() != null) {
                    BigDecimal totalPrice = crmQuotationItem.getQuantity().multiply(crmQuotationItem.getUnitPrice());
                    crmQuotationItem.setTotalPrice(totalPrice);
                    
                    // 计算折扣后金额
                    BigDecimal finalAmount = totalPrice;
                    if (crmQuotationItem.getDiscountAmount() != null) {
                        finalAmount = totalPrice.subtract(crmQuotationItem.getDiscountAmount());
                    } else if (crmQuotationItem.getDiscountRate() != null) {
                        BigDecimal discountAmount = totalPrice.multiply(crmQuotationItem.getDiscountRate());
                        crmQuotationItem.setDiscountAmount(discountAmount);
                        finalAmount = totalPrice.subtract(discountAmount);
                    }
                    crmQuotationItem.setFinalAmount(finalAmount);
                }
            }
            crmQuotationMapper.batchCrmQuotationItem(crmQuotationItemList);
        }
    }

    @Override
    public CrmQuotation selectCrmQuotationByQuotationNo(String quotationNo) {
        return crmQuotationMapper.selectCrmQuotationByQuotationNo(quotationNo);
    }

    @Override
    public int submitQuotationApproval(Long quotationId, String processDefinitionKey) {
        // TODO: 集成Activiti工作流
        CrmQuotation quotation = crmQuotationMapper.selectCrmQuotationById(quotationId);
        if (quotation == null) {
            throw new RuntimeException("报价单不存在");
        }
        
        if (!"draft".equals(quotation.getStatus())) {
            throw new RuntimeException("只有草稿状态的报价单才能提交审批");
        }
        
        // 更新状态
        quotation.setStatus("submitted");
        quotation.setApprovalStatus("pending");
        quotation.setUpdateTime(DateUtils.getNowDate());
        quotation.setUpdateBy(SecurityUtils.getUsername());
        
        int rows = crmQuotationMapper.updateCrmQuotation(quotation);
        
        logger.info("提交报价单审批: {}, ID: {}", quotation.getQuotationName(), quotationId);
        return rows;
    }

    @Override
    public int approveQuotation(String taskId, boolean approved, String comment) {
        // TODO: 集成Activiti工作流处理审批
        logger.info("审批报价单任务: {}, 审批结果: {}, 意见: {}", taskId, approved, comment);
        return 1;
    }

    @Override
    public int cancelQuotationApproval(Long quotationId, String reason) {
        CrmQuotation quotation = crmQuotationMapper.selectCrmQuotationById(quotationId);
        if (quotation == null) {
            throw new RuntimeException("报价单不存在");
        }
        
        // 更新状态
        quotation.setStatus("cancelled");
        quotation.setRemarks(StringUtils.isNotEmpty(quotation.getRemarks()) 
                ? quotation.getRemarks() + "\n撤销原因：" + reason 
                : "撤销原因：" + reason);
        quotation.setUpdateTime(DateUtils.getNowDate());
        quotation.setUpdateBy(SecurityUtils.getUsername());
        
        int rows = crmQuotationMapper.updateCrmQuotation(quotation);
        
        logger.info("撤销报价单审批: {}, ID: {}, 原因: {}", quotation.getQuotationName(), quotationId, reason);
        return rows;
    }

    @Override
    public String generateQuotationNo() {
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String prefix = "QUO" + dateStr;
        
        // TODO: 实现自增序号逻辑，确保编号唯一性
        long currentTimeMillis = System.currentTimeMillis();
        String suffix = String.format("%04d", currentTimeMillis % 10000);
        
        return prefix + suffix;
    }

    @Override
    public void calculateTotalAmount(CrmQuotation crmQuotation) {
        if (CollectionUtils.isEmpty(crmQuotation.getQuotationItems())) {
            crmQuotation.setTotalAmount(BigDecimal.ZERO);
            return;
        }
        
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (CrmQuotationItem item : crmQuotation.getQuotationItems()) {
            if (item.getFinalAmount() != null) {
                totalAmount = totalAmount.add(item.getFinalAmount());
            } else if (item.getTotalPrice() != null) {
                totalAmount = totalAmount.add(item.getTotalPrice());
            }
        }
        
        crmQuotation.setTotalAmount(totalAmount);
    }
}