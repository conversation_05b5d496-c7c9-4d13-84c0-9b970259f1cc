<template>
  <div :class="{'hidden':hidden}" class="pagination-container">
    <el-pagination
      :background="background"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :layout="layout"
      :page-sizes="pageSizes"
      :pager-count="pagerCount"
      :total="total"
      v-bind="$attrs"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { scrollTo } from '@/utils/scroll-to';
import { computed } from 'vue';

interface Props {
  total: number;
  page?: number;
  limit?: number;
  pageSizes?: number[];
  pagerCount?: number;
  layout?: string;
  background?: boolean;
  autoScroll?: boolean;
  hidden?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  page: 1,
  limit: 20,
  pageSizes: () => [10, 20, 30, 50],
  pagerCount: document.body.clientWidth < 992 ? 5 : 7,
  layout: 'total, sizes, prev, pager, next, jumper',
  background: true,
  autoScroll: true,
  hidden: false
});

const emit = defineEmits<{
  (e: 'update:page', value: number): void;
  (e: 'update:limit', value: number): void;
  (e: 'pagination', value: { page: number; limit: number }): void;
}>();

const currentPage = computed({
  get: () => props.page,
  set: (val) => emit('update:page', val)
});

const pageSize = computed({
  get: () => props.limit,
  set: (val) => emit('update:limit', val)
});

const handleSizeChange = (val: number) => {
  let newPage = currentPage.value;
  if (newPage * val > props.total) {
    newPage = 1;
    emit('update:page', newPage);
  }
  emit('pagination', { page: newPage, limit: val });
  if (props.autoScroll) {
    scrollTo(0, 800);
  }
};

const handleCurrentChange = (val: number) => {
  emit('pagination', { page: val, limit: pageSize.value });
  if (props.autoScroll) {
    scrollTo(0, 800);
  }
};
</script>

<style scoped>
.pagination-container {
  background-color: transparent;
  padding: 32px 16px;
}
.pagination-container.hidden {
  display: none;
}
</style>
