package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.domain.entity.CrmBusinessApApprovalProcess;
import com.ruoyi.system.service.ICrmBusinessApApprovalProcessService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 审批处理Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Api(tags = "审批处理管理")
@RestController
@RequestMapping("/system/process")
public class CrmBusinessApApprovalProcessController extends BaseController {
    @Autowired
    private ICrmBusinessApApprovalProcessService crmBusinessApApprovalProcessService;

    /**
     * 查询审批处理列表
     */
    @ApiOperation("查询审批处理列表")
    @PreAuthorize("@ss.hasPermi('system:process:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusinessApApprovalProcess crmBusinessApApprovalProcess) {
        startPage();
        List<CrmBusinessApApprovalProcess> list = crmBusinessApApprovalProcessService
                .selectCrmBusinessApApprovalProcessList(crmBusinessApApprovalProcess);
        return getDataTable(list);
    }

    /**
     * 导出审批处理列表
     */
    @ApiOperation("导出审批处理列表")
    @PreAuthorize("@ss.hasPermi('system:process:export')")
    @Log(title = "审批处理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmBusinessApApprovalProcess crmBusinessApApprovalProcess) {
        List<CrmBusinessApApprovalProcess> list = crmBusinessApApprovalProcessService
                .selectCrmBusinessApApprovalProcessList(crmBusinessApApprovalProcess);
        ExcelUtil<CrmBusinessApApprovalProcess> util = new ExcelUtil<CrmBusinessApApprovalProcess>(
                CrmBusinessApApprovalProcess.class);
        util.exportExcel(response, list, "审批处理数据");
    }

    /**
     * 获取审批处理详细信息
     */
    @ApiOperation("获取审批处理详细信息")
    @PreAuthorize("@ss.hasPermi('system:process:query')")
    @GetMapping(value = "/{approvalId}")
    public AjaxResult getInfo(@ApiParam("审批处理ID") @PathVariable("approvalId") Long approvalId) {
        return success(crmBusinessApApprovalProcessService.selectCrmBusinessApApprovalProcessByApprovalId(approvalId));
    }

    /**
     * 新增审批处理
     */
    @ApiOperation("新增审批处理")
    @PreAuthorize("@ss.hasPermi('system:process:add')")
    @Log(title = "审批处理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("审批处理信息") @RequestBody CrmBusinessApApprovalProcess crmBusinessApApprovalProcess) {
        return toAjax(
                crmBusinessApApprovalProcessService.insertCrmBusinessApApprovalProcess(crmBusinessApApprovalProcess));
    }

    /**
     * 修改审批处理
     */
    @ApiOperation("修改审批处理")
    @PreAuthorize("@ss.hasPermi('system:process:edit')")
    @Log(title = "审批处理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("审批处理信息") @RequestBody CrmBusinessApApprovalProcess crmBusinessApApprovalProcess) {
        return toAjax(
                crmBusinessApApprovalProcessService.updateCrmBusinessApApprovalProcess(crmBusinessApApprovalProcess));
    }

    /**
     * 删除审批处理
     */
    @ApiOperation("删除审批处理")
    @PreAuthorize("@ss.hasPermi('system:process:remove')")
    @Log(title = "审批处理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{approvalIds}")
    public AjaxResult remove(@ApiParam("审批处理ID数组") @PathVariable Long[] approvalIds) {
        return toAjax(crmBusinessApApprovalProcessService.deleteCrmBusinessApApprovalProcessByApprovalIds(approvalIds));
    }
}
