<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:activiti="http://activiti.org/bpmn"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="http://www.ruoyi.com/quotation">

  <process id="quotation-approval" name="报价单审批流程" isExecutable="true">
    
    <!-- 开始事件 -->
    <startEvent id="startEvent" name="提交审批">
      <extensionElements>
        <activiti:executionListener event="start" delegateExpression="${quotationStatusListener}">
          <activiti:field name="eventType" stringValue="start"/>
        </activiti:executionListener>
      </extensionElements>
    </startEvent>
    
    <!-- 部门经理审批 -->
    <userTask id="deptManagerApproval" name="部门经理审批" 
              activiti:candidateGroups="dept_manager">
      <documentation>部门经理审核报价单内容和价格</documentation>
      <extensionElements>
        <activiti:formProperty id="approvalResult" name="审批结果" type="enum" required="true">
          <activiti:value id="approved" name="同意"/>
          <activiti:value id="rejected" name="驳回"/>
          <activiti:value id="needModify" name="需要修改"/>
        </activiti:formProperty>
        <activiti:formProperty id="approvalComments" name="审批意见" type="string"/>
        <activiti:formProperty id="customerVerified" name="客户信息验证" type="boolean" default="false"/>
        <activiti:formProperty id="priceVerified" name="价格验证" type="boolean" default="false"/>
        
        <activiti:taskListener event="create" delegateExpression="${quotationTaskAssignmentListener}"/>
        <activiti:taskListener event="complete" delegateExpression="${quotationStatusListener}">
          <activiti:field name="eventType" stringValue="deptApproval"/>
        </activiti:taskListener>
      </extensionElements>
    </userTask>
    
    <!-- 审批结果判断网关 -->
    <exclusiveGateway id="approvalGateway" name="审批结果判断"/>
    
    <!-- 金额判断网关 -->
    <exclusiveGateway id="amountGateway" name="金额判断"/>
    
    <!-- 总经理审批 -->
    <userTask id="ceoApproval" name="总经理审批" 
              activiti:candidateGroups="ceo">
      <documentation>总经理审批大额报价单（>10万元）</documentation>
      <extensionElements>
        <activiti:formProperty id="approvalResult" name="审批结果" type="enum" required="true">
          <activiti:value id="approved" name="同意"/>
          <activiti:value id="rejected" name="驳回"/>
          <activiti:value id="returnToDept" name="退回部门"/>
        </activiti:formProperty>
        <activiti:formProperty id="approvalComments" name="审批意见" type="string"/>
        
        <activiti:taskListener event="create" delegateExpression="${quotationTaskAssignmentListener}"/>
        <activiti:taskListener event="complete" delegateExpression="${quotationStatusListener}">
          <activiti:field name="eventType" stringValue="ceoApproval"/>
        </activiti:taskListener>
      </extensionElements>
    </userTask>
    
    <!-- 董事长审批（超大额） -->
    <userTask id="chairmanApproval" name="董事长审批" 
              activiti:candidateGroups="chairman">
      <documentation>董事长审批超大额报价单（>50万元）</documentation>
      <extensionElements>
        <activiti:formProperty id="approvalResult" name="审批结果" type="enum" required="true">
          <activiti:value id="approved" name="同意"/>
          <activiti:value id="rejected" name="驳回"/>
          <activiti:value id="returnToCeo" name="退回总经理"/>
        </activiti:formProperty>
        <activiti:formProperty id="approvalComments" name="审批意见" type="string"/>
        
        <activiti:taskListener event="create" delegateExpression="${quotationTaskAssignmentListener}"/>
        <activiti:taskListener event="complete" delegateExpression="${quotationStatusListener}">
          <activiti:field name="eventType" stringValue="chairmanApproval"/>
        </activiti:taskListener>
      </extensionElements>
    </userTask>
    
    <!-- 修改任务 -->
    <userTask id="modifyQuotation" name="修改报价单" 
              activiti:assignee="${initiator}">
      <documentation>申请人修改报价单信息</documentation>
      <extensionElements>
        <activiti:formProperty id="modifyResult" name="修改结果" type="enum" required="true">
          <activiti:value id="resubmit" name="重新提交"/>
          <activiti:value id="cancel" name="取消申请"/>
        </activiti:formProperty>
        <activiti:formProperty id="modifyComments" name="修改说明" type="string"/>
        
        <activiti:taskListener event="complete" delegateExpression="${quotationStatusListener}">
          <activiti:field name="eventType" stringValue="modify"/>
        </activiti:taskListener>
      </extensionElements>
    </userTask>
    
    <!-- 状态同步服务任务 -->
    <serviceTask id="syncApprovedStatus" name="同步审批通过状态"
                 activiti:delegateExpression="${quotationStatusListener}">
      <extensionElements>
        <activiti:field name="eventType" stringValue="approved"/>
      </extensionElements>
    </serviceTask>
    
    <serviceTask id="syncRejectedStatus" name="同步审批驳回状态"
                 activiti:delegateExpression="${quotationStatusListener}">
      <extensionElements>
        <activiti:field name="eventType" stringValue="rejected"/>
      </extensionElements>
    </serviceTask>
    
    <!-- 结束事件 -->
    <endEvent id="approvedEndEvent" name="审批通过"/>
    <endEvent id="rejectedEndEvent" name="审批驳回"/>
    <endEvent id="cancelledEndEvent" name="申请取消"/>
    
    <!-- 流程连线 -->
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="deptManagerApproval"/>
    <sequenceFlow id="flow2" sourceRef="deptManagerApproval" targetRef="approvalGateway"/>
    
    <!-- 部门经理审批结果分支 -->
    <sequenceFlow id="deptApproved" sourceRef="approvalGateway" targetRef="amountGateway">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'approved'}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="deptRejected" sourceRef="approvalGateway" targetRef="syncRejectedStatus">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'rejected'}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="deptNeedModify" sourceRef="approvalGateway" targetRef="modifyQuotation">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'needModify'}</conditionExpression>
    </sequenceFlow>
    
    <!-- 金额判断分支 -->
    <sequenceFlow id="smallAmount" sourceRef="amountGateway" targetRef="syncApprovedStatus">
      <conditionExpression xsi:type="tFormalExpression">${totalAmount <= 100000}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="mediumAmount" sourceRef="amountGateway" targetRef="ceoApproval">
      <conditionExpression xsi:type="tFormalExpression">${totalAmount > 100000 && totalAmount <= 500000}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="largeAmount" sourceRef="amountGateway" targetRef="ceoApproval">
      <conditionExpression xsi:type="tFormalExpression">${totalAmount > 500000}</conditionExpression>
    </sequenceFlow>
    
    <!-- 总经理审批结果分支 -->
    <exclusiveGateway id="ceoResultGateway" name="总经理审批结果"/>
    <sequenceFlow id="flowToCeoResult" sourceRef="ceoApproval" targetRef="ceoResultGateway"/>
    
    <sequenceFlow id="ceoApproved" sourceRef="ceoResultGateway" targetRef="syncApprovedStatus">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'approved' && totalAmount <= 500000}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="ceoApprovedToChairman" sourceRef="ceoResultGateway" targetRef="chairmanApproval">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'approved' && totalAmount > 500000}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="ceoRejected" sourceRef="ceoResultGateway" targetRef="syncRejectedStatus">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'rejected'}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="ceoReturnToDept" sourceRef="ceoResultGateway" targetRef="deptManagerApproval">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'returnToDept'}</conditionExpression>
    </sequenceFlow>
    
    <!-- 董事长审批结果分支 -->
    <sequenceFlow id="chairmanApproved" sourceRef="chairmanApproval" targetRef="syncApprovedStatus">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'approved'}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="chairmanRejected" sourceRef="chairmanApproval" targetRef="syncRejectedStatus">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'rejected'}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="chairmanReturnToCeo" sourceRef="chairmanApproval" targetRef="ceoApproval">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == 'returnToCeo'}</conditionExpression>
    </sequenceFlow>
    
    <!-- 修改结果分支 -->
    <sequenceFlow id="resubmit" sourceRef="modifyQuotation" targetRef="deptManagerApproval">
      <conditionExpression xsi:type="tFormalExpression">${modifyResult == 'resubmit'}</conditionExpression>
    </sequenceFlow>
    
    <sequenceFlow id="cancelApplication" sourceRef="modifyQuotation" targetRef="cancelledEndEvent">
      <conditionExpression xsi:type="tFormalExpression">${modifyResult == 'cancel'}</conditionExpression>
    </sequenceFlow>
    
    <!-- 结束连线 -->
    <sequenceFlow id="flowToApprovedEnd" sourceRef="syncApprovedStatus" targetRef="approvedEndEvent"/>
    <sequenceFlow id="flowToRejectedEnd" sourceRef="syncRejectedStatus" targetRef="rejectedEndEvent"/>
    
  </process>

  <!-- BPMN图形信息（可选，用于流程图显示） -->
  <bpmndi:BPMNDiagram id="BPMNDiagram_quotation-approval">
    <bpmndi:BPMNPlane bpmnElement="quotation-approval" id="BPMNPlane_quotation-approval">
      <!-- 这里可以添加图形布局信息，用于流程设计器显示 -->
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>

</definitions>
