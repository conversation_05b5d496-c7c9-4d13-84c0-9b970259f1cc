<template>
  <div class="test-container">
    <h1>MessageBox 测试页面</h1>
    <div class="test-buttons">
      <el-button type="primary" @click="testBasicMessageBox">基础 MessageBox</el-button>
      <el-button type="warning" @click="testConfirmMessageBox">确认对话框</el-button>
      <el-button type="danger" @click="testDeleteConfirm">删除确认</el-button>
      <el-button type="info" @click="testWithOptions">带选项的确认</el-button>
    </div>
    <div class="test-info">
      <p>当前样式加载状态：</p>
      <ul>
        <li>Element Plus CSS: {{ hasElementCSS ? '✅' : '❌' }}</li>
        <li>自定义样式: {{ hasCustomCSS ? '✅' : '❌' }}</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, ref } from 'vue';

const hasElementCSS = ref(false);
const hasCustomCSS = ref(false);

// 检查样式加载状态
const checkStylesLoaded = () => {
  // 检查 Element Plus 样式是否加载
  const elementStyleSheets = Array.from(document.styleSheets).find(sheet => 
    sheet.href && sheet.href.includes('element-plus')
  );
  hasElementCSS.value = !!elementStyleSheets;
  
  // 检查自定义样式是否加载
  const customStyleSheets = Array.from(document.styleSheets).find(sheet => 
    sheet.href && (sheet.href.includes('index.scss') || sheet.href.includes('element-override'))
  );
  hasCustomCSS.value = !!customStyleSheets;
};

// 最基础的 MessageBox
const testBasicMessageBox = async () => {
  try {
    console.log('测试基础 MessageBox');
    await ElMessageBox.alert('这是一个基础的消息框', '提示');
    ElMessage.success('基础测试成功');
  } catch (error) {
    console.error('基础测试失败:', error);
  }
};

// 确认对话框
const testConfirmMessageBox = async () => {
  try {
    console.log('测试确认对话框');
    await ElMessageBox.confirm('这是一个确认对话框', '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    ElMessage.success('确认测试成功');
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('用户取消了操作');
    } else {
      console.error('确认测试失败:', error);
    }
  }
};

// 删除确认
const testDeleteConfirm = async () => {
  try {
    console.log('测试删除确认');
    await ElMessageBox.confirm(
      '确认删除这个项目吗？此操作不可恢复！',
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    ElMessage.success('删除测试成功');
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('用户取消了删除');
    } else {
      console.error('删除测试失败:', error);
    }
  }
};

// 带完整选项的确认
const testWithOptions = async () => {
  try {
    console.log('测试带选项的确认');
    await ElMessageBox.confirm(
      '这是一个带完整选项的确认对话框',
      '完整测试',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
        closeOnClickModal: false,
        closeOnPressEscape: true,
        showClose: true,
        lockScroll: true
      }
    );
    ElMessage.success('完整测试成功');
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('用户取消了完整测试');
    } else {
      console.error('完整测试失败:', error);
    }
  }
};

onMounted(() => {
  checkStylesLoaded();
  console.log('MessageBox 测试页面已加载');
});
</script>

<style scoped>
.test-container {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  text-align: center;
  margin-bottom: 40px;
  color: #333;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
}

.test-buttons .el-button {
  height: 50px;
  font-size: 16px;
}

.test-info {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}

.test-info p {
  margin: 0 0 10px 0;
  font-weight: 600;
  color: #333;
}

.test-info ul {
  margin: 0;
  padding-left: 20px;
}

.test-info li {
  margin: 5px 0;
  color: #666;
}
</style> 