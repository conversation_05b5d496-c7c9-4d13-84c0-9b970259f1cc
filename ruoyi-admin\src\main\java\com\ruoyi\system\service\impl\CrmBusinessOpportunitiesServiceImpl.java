package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CrmBusinessOpportunitiesMapper;
import com.ruoyi.system.domain.CrmBusinessOpportunities;
import com.ruoyi.system.service.ICrmBusinessOpportunitiesService;

/**
 * 商机Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class CrmBusinessOpportunitiesServiceImpl implements ICrmBusinessOpportunitiesService 
{
    @Autowired
    private CrmBusinessOpportunitiesMapper crmBusinessOpportunitiesMapper;

    /**
     * 查询商机
     * 
     * @param id 商机主键
     * @return 商机
     */
    @Override
    public CrmBusinessOpportunities selectCrmBusinessOpportunitiesById(Long id)
    {
        return crmBusinessOpportunitiesMapper.selectCrmBusinessOpportunitiesById(id);
    }

    /**
     * 查询商机列表
     * 
     * @param crmBusinessOpportunities 商机
     * @return 商机
     */
    @Override
    public List<CrmBusinessOpportunities> selectCrmBusinessOpportunitiesList(CrmBusinessOpportunities crmBusinessOpportunities)
    {
        return crmBusinessOpportunitiesMapper.selectCrmBusinessOpportunitiesList(crmBusinessOpportunities);
    }

    /**
     * 新增商机
     * 
     * @param crmBusinessOpportunities 商机
     * @return 结果
     */
    @Override
    public int insertCrmBusinessOpportunities(CrmBusinessOpportunities crmBusinessOpportunities)
    {
        return crmBusinessOpportunitiesMapper.insertCrmBusinessOpportunities(crmBusinessOpportunities);
    }

    /**
     * 修改商机
     * 
     * @param crmBusinessOpportunities 商机
     * @return 结果
     */
    @Override
    public int updateCrmBusinessOpportunities(CrmBusinessOpportunities crmBusinessOpportunities)
    {
        return crmBusinessOpportunitiesMapper.updateCrmBusinessOpportunities(crmBusinessOpportunities);
    }

    /**
     * 批量删除商机
     * 
     * @param ids 需要删除的商机主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessOpportunitiesByIds(Long[] ids)
    {
        return crmBusinessOpportunitiesMapper.deleteCrmBusinessOpportunitiesByIds(ids);
    }

    /**
     * 删除商机信息
     * 
     * @param id 商机主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessOpportunitiesById(Long id)
    {
        return crmBusinessOpportunitiesMapper.deleteCrmBusinessOpportunitiesById(id);
    }
}
