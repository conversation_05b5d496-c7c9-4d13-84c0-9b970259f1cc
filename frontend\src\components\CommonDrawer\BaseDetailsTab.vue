<template>
    <div class="base-details-tab">
        <!-- 基本信息 -->
        <div class="basic-info">
            <div class="info-header">
                <h3>{{ basicTitle }}</h3>
                <el-button type="primary" @click="handleEditBasicInfo">
                    <el-icon><Edit /></el-icon>
                    编辑
                </el-button>
            </div>
            <el-descriptions :column="2" border>
                <el-descriptions-item 
                    v-for="field in basicFields"
                    :key="field.field"
                    :label="field.label"
                >
                    {{ entityData[field.field] }}
                </el-descriptions-item>
            </el-descriptions>
        </div>

        <!-- 系统信息 -->
        <div class="system-info">
            <div class="info-header">
                <h3>{{ systemTitle }}</h3>
                <el-button type="primary" @click="handleEditSystemInfo">
                    <el-icon><Edit /></el-icon>
                    编辑
                </el-button>
            </div>
            <el-descriptions :column="2" border>
                <el-descriptions-item 
                    v-for="field in systemFields"
                    :key="field.field"
                    :label="field.label"
                >
                    {{ entityData[field.field] }}
                </el-descriptions-item>
            </el-descriptions>
        </div>

        <!-- 编辑基本信息对话框 -->
        <el-dialog
            v-model="basicDialogVisible"
            :title="'编辑' + basicTitle"
            width="600px"
        >
            <el-form :model="editBasicForm" label-width="100px">
                <el-form-item 
                    v-for="field in basicFields"
                    :key="field.field"
                    :label="field.label"
                >
                    <component 
                        :is="field.component || 'el-input'"
                        v-model="editBasicForm[field.field]"
                        v-bind="field.props || {}"
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="basicDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveBasicInfo">保存</el-button>
            </template>
        </el-dialog>

        <!-- 编辑系统信息对话框 -->
        <el-dialog
            v-model="systemDialogVisible"
            :title="'编辑' + systemTitle"
            width="600px"
        >
            <el-form :model="editSystemForm" label-width="100px">
                <el-form-item 
                    v-for="field in systemFields"
                    :key="field.field"
                    :label="field.label"
                >
                    <component 
                        :is="field.component || 'el-input'"
                        v-model="editSystemForm[field.field]"
                        v-bind="field.props || {}"
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="systemDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveSystemInfo">保存</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'BaseDetailsTab',
    props: {
        entityData: {
            type: Object,
            required: true
        },
        entityType: {
            type: String,
            required: true
        },
        // 基本信息字段配置
        basicFields: {
            type: Array,
            required: true
        },
        // 系统信息字段配置
        systemFields: {
            type: Array,
            required: true
        },
        // 基本信息标题
        basicTitle: {
            type: String,
            default: '基本信息'
        },
        // 系统信息标题
        systemTitle: {
            type: String,
            default: '系统信息'
        }
    },
    emits: ['update:entity'],
    data() {
        return {
            basicDialogVisible: false,
            systemDialogVisible: false,
            editBasicForm: {},
            editSystemForm: {}
        };
    },
    methods: {
        // 处理编辑基本信息
        handleEditBasicInfo() {
            this.editBasicForm = { ...this.entityData };
            this.basicDialogVisible = true;
        },

        // 处理编辑系统信息
        handleEditSystemInfo() {
            this.editSystemForm = { ...this.entityData };
            this.systemDialogVisible = true;
        },

        // 保存基本信息
        saveBasicInfo() {
            this.$emit('update:entity', { ...this.entityData, ...this.editBasicForm });
            this.basicDialogVisible = false;
        },

        // 保存系统信息
        saveSystemInfo() {
            this.$emit('update:entity', { ...this.entityData, ...this.editSystemForm });
            this.systemDialogVisible = false;
        }
    }
};
</script>

<style scoped>
.base-details-tab {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.basic-info,
.system-info {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.basic-info:hover,
.system-info:hover {
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
            content: '';
            width: 4px;
            height: 18px;
            background: var(--el-color-primary);
            border-radius: 2px;
            display: inline-block;
        }
    }
}

:deep(.el-descriptions) {
    padding: 0;
    background: transparent;
}

:deep(.el-descriptions__header) {
    margin-bottom: 0;
}

:deep(.el-descriptions__body) {
    background-color: transparent;
}

:deep(.el-descriptions__label) {
    background: rgba(247, 248, 250, 0.6);
    backdrop-filter: blur(5px);
    font-weight: 500;
    color: #333;
}

:deep(.el-descriptions__content) {
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(5px);
}

:deep(.el-descriptions__cell) {
    padding: 16px 20px;
}

:deep(.el-button) {
    border-radius: 8px;
    padding: 8px 16px;
    transition: all 0.3s ease;
    
    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
}

/* 对话框样式 */
:deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
}

:deep(.el-dialog__header) {
    background: rgba(247, 248, 250, 0.8);
    backdrop-filter: blur(10px);
    margin: 0;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

:deep(.el-dialog__body) {
    padding: 24px;
}

:deep(.el-form-item) {
    margin-bottom: 24px;
}

:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper),
:deep(.el-textarea__wrapper) {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: none;
    transition: all 0.3s ease;

    &:hover {
        background: rgba(255, 255, 255, 0.95);
        border-color: var(--el-color-primary-light-5);
    }

    &:focus-within {
        background: #ffffff;
        border-color: var(--el-color-primary);
        box-shadow: 0 0 0 1px var(--el-color-primary-light-5);
    }
}

:deep(.el-form-item__label) {
    font-weight: 500;
    color: #333;
    padding-bottom: 8px;
}

:deep(.el-dialog__footer) {
    background: rgba(247, 248, 250, 0.8);
    backdrop-filter: blur(10px);
    padding: 16px 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}
</style> 