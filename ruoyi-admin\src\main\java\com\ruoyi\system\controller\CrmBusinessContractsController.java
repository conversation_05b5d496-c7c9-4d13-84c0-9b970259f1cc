package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.CrmBusinessContracts;
import com.ruoyi.system.service.ICrmBusinessContractsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 合同Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Api(tags = "合同管理")
@RestController
@RequestMapping("/system/contracts")
public class CrmBusinessContractsController extends BaseController {
    @Autowired
    private ICrmBusinessContractsService crmBusinessContractsService;

    /**
     * 查询合同列表
     */
    @ApiOperation("查询合同列表")
    @PreAuthorize("@ss.hasPermi('system:contracts:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusinessContracts crmBusinessContracts) {
        startPage();
        List<CrmBusinessContracts> list = crmBusinessContractsService
                .selectCrmBusinessContractsList(crmBusinessContracts);
        return getDataTable(list);
    }

    /**
     * 导出合同列表
     */
    @ApiOperation("导出合同列表")
    @PreAuthorize("@ss.hasPermi('system:contracts:export')")
    @Log(title = "合同", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmBusinessContracts crmBusinessContracts) {
        List<CrmBusinessContracts> list = crmBusinessContractsService
                .selectCrmBusinessContractsList(crmBusinessContracts);
        ExcelUtil<CrmBusinessContracts> util = new ExcelUtil<CrmBusinessContracts>(CrmBusinessContracts.class);
        util.exportExcel(response, list, "合同数据");
    }

    /**
     * 获取合同详细信息
     */
    @ApiOperation("获取合同详细信息")
    @PreAuthorize("@ss.hasPermi('system:contracts:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("合同ID") @PathVariable("id") Long id) {
        return success(crmBusinessContractsService.selectCrmBusinessContractsById(id));
    }

    /**
     * 新增合同
     */
    @ApiOperation("新增合同")
    @PreAuthorize("@ss.hasPermi('system:contracts:add')")
    @Log(title = "合同", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("合同信息") @RequestBody CrmBusinessContracts crmBusinessContracts) {
        return toAjax(crmBusinessContractsService.insertCrmBusinessContracts(crmBusinessContracts));
    }

    /**
     * 修改合同
     */
    @ApiOperation("修改合同")
    @PreAuthorize("@ss.hasPermi('system:contracts:edit')")
    @Log(title = "合同", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("合同信息") @RequestBody CrmBusinessContracts crmBusinessContracts) {
        return toAjax(crmBusinessContractsService.updateCrmBusinessContracts(crmBusinessContracts));
    }

    /**
     * 删除合同
     */
    @ApiOperation("删除合同")
    @PreAuthorize("@ss.hasPermi('system:contracts:remove')")
    @Log(title = "合同", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("合同ID数组") @PathVariable Long[] ids) {
        return toAjax(crmBusinessContractsService.deleteCrmBusinessContractsByIds(ids));
    }
}
