import request from '@/utils/request'

// 查询客户列表
export function listCustomers(query) {
  return request({
    url: '/front/crm/customer/list',
    method: 'get',
    params: query
  })
}

// 查询客户详细
export function getCustomers(id) {
  return request({
    url: '/front/crm/customer/' + id,
    method: 'get'
  })
}

// 新增客户
export function addCustomers(data) {
  return request({
    url: '/front/crm/customer',
    method: 'post',
    data: data
  })
}

// 修改客户
export function updateCustomers(data) {
  return request({
    url: '/front/crm/customer',
    method: 'put',
    data: data
  })
}

// 删除客户
export function delCustomers(id) {
  return request({
    url: '/front/crm/customer/' + id,
    method: 'delete'
  })
}

// 搜索客户
export function searchCustomers(keyword) {
  return request({
    url: '/front/crm/customer/search',
    method: 'get',
    params: { 
      keyword: keyword || '',
      pageNum: 1,
      pageSize: 100  // 获取足够多的结果用于选择
    }
  })
}
