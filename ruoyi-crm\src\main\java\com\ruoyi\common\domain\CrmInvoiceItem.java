package com.ruoyi.common.domain;

import java.math.BigDecimal;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 发票明细对象 crm_invoice_items
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
public class CrmInvoiceItem {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 发票ID */
    private Long invoiceId;

    /** 项目名称 */
    private String itemName;

    /** 项目编码 */
    private String itemCode;

    /** 规格型号 */
    private String specification;

    /** 单位 */
    private String unit;

    /** 数量 */
    private BigDecimal quantity;

    /** 单价 */
    private BigDecimal unitPrice;

    /** 金额 */
    private BigDecimal amount;

    /** 税率 */
    private BigDecimal taxRate;

    /** 税额 */
    private BigDecimal taxAmount;

    /** 排序 */
    private Integer sortOrder;

    /** 备注 */
    private String remarks;
}