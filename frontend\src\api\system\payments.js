import request from '@/utils/request'

// 查询回款列表
export function listPayments(query) {
  return request({
    url: '/system/payments/list',
    method: 'get',
    params: query
  })
}

// 查询回款详细
export function getPayments(id) {
  return request({
    url: '/system/payments/' + id,
    method: 'get'
  })
}

// 新增回款
export function addPayments(data) {
  return request({
    url: '/system/payments',
    method: 'post',
    data: data
  })
}

// 修改回款
export function updatePayments(data) {
  return request({
    url: '/system/payments',
    method: 'put',
    data: data
  })
}

// 删除回款
export function delPayments(id) {
  return request({
    url: '/system/payments/' + id,
    method: 'delete'
  })
}
