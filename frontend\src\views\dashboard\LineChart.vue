<template>
    <div ref="chart" class="chart"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    props: {
        chartData: {
            type: Array,
            default: () => [0, 0, 0, 0, 0, 0]
        }
    },
    data() {
        return {
            chart: null
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart();
            window.addEventListener('resize', this.handleResize);
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.chart) {
            this.chart.dispose();
            this.chart = null;
        }
    },
    methods: {
        handleResize() {
            if (this.chart) {
                this.chart.resize();
            }
        },
        initChart() {
            if (this.chart) {
                this.chart.dispose();
            }

            const chartDom = this.$refs.chart;
            if (!chartDom) return;

            this.chart = echarts.init(chartDom);
            const option = {
                xAxis: {
                    type: 'category',
                    data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    show: false
                },
                yAxis: {
                    type: 'value',
                    show: false
                },
                series: [{
                    data: this.chartData,
                    type: 'line',
                    smooth: true
                }]
            };
            this.chart.setOption(option);
        }
    }
}
</script>

<style scoped>
.chart {
    width: 100%;
    height: 100px;
}
</style>
