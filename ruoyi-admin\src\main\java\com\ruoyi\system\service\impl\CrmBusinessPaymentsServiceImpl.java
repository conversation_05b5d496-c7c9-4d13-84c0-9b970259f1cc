package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CrmBusinessPaymentsMapper;
import com.ruoyi.system.domain.CrmBusinessPayments;
import com.ruoyi.system.service.ICrmBusinessPaymentsService;

/**
 * 回款Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class CrmBusinessPaymentsServiceImpl implements ICrmBusinessPaymentsService 
{
    @Autowired
    private CrmBusinessPaymentsMapper crmBusinessPaymentsMapper;

    /**
     * 查询回款
     * 
     * @param id 回款主键
     * @return 回款
     */
    @Override
    public CrmBusinessPayments selectCrmBusinessPaymentsById(Long id)
    {
        return crmBusinessPaymentsMapper.selectCrmBusinessPaymentsById(id);
    }

    /**
     * 查询回款列表
     * 
     * @param crmBusinessPayments 回款
     * @return 回款
     */
    @Override
    public List<CrmBusinessPayments> selectCrmBusinessPaymentsList(CrmBusinessPayments crmBusinessPayments)
    {
        return crmBusinessPaymentsMapper.selectCrmBusinessPaymentsList(crmBusinessPayments);
    }

    /**
     * 新增回款
     * 
     * @param crmBusinessPayments 回款
     * @return 结果
     */
    @Override
    public int insertCrmBusinessPayments(CrmBusinessPayments crmBusinessPayments)
    {
        return crmBusinessPaymentsMapper.insertCrmBusinessPayments(crmBusinessPayments);
    }

    /**
     * 修改回款
     * 
     * @param crmBusinessPayments 回款
     * @return 结果
     */
    @Override
    public int updateCrmBusinessPayments(CrmBusinessPayments crmBusinessPayments)
    {
        return crmBusinessPaymentsMapper.updateCrmBusinessPayments(crmBusinessPayments);
    }

    /**
     * 批量删除回款
     * 
     * @param ids 需要删除的回款主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessPaymentsByIds(Long[] ids)
    {
        return crmBusinessPaymentsMapper.deleteCrmBusinessPaymentsByIds(ids);
    }

    /**
     * 删除回款信息
     * 
     * @param id 回款主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessPaymentsById(Long id)
    {
        return crmBusinessPaymentsMapper.deleteCrmBusinessPaymentsById(id);
    }
}
