<template>
    <div class="contact-header-tab">
        <!-- 顶部标题区 -->
        <div class="header-section">
            <div class="title-area">
                <div class="entity-type">{{ modelName }}</div>
                <h2 class="entity-name">
                    {{ entityData.name }}
                    <el-icon 
                        class="star-icon" 
                        :class="{ 'star-filled': entityData.isFollowing }"
                        @click="handleToggleFollow"
                    >
                        <StarFilled v-if="entityData.isFollowing" />
                        <Star v-else />
                    </el-icon>
                </h2>
            </div>
            
            <!-- 操作按钮区 -->
            <div class="action-buttons">
                <template v-for="(action, index) in actions" :key="index">
                    <button
                        :class="getButtonClass(action)"
                        @click="handleActionClick(action)"
                    >
                        <el-icon v-if="action.icon" class="button-icon">
                            <component :is="action.icon" />
                        </el-icon>
                        {{ action.label }}
                    </button>
                </template>
            </div>
        </div>

        <!-- 基本信息区 -->
        <div class="info-section">
            <div class="info-grid">
                <div class="info-row">
                    <div class="info-item" @dblclick="handleFieldEdit('customerName')">
                        <span class="info-label">所属客户</span>
                        <div class="info-value-wrapper">
                            <template v-if="editingField === 'customerName'">
                                <el-input
                                    v-model="editingValue"
                                    size="small"
                                    placeholder="请输入客户名称"
                                    @blur="handleSaveField"
                                    @keyup.enter="handleSaveField"
                                    v-focus
                                />
                            </template>
                            <span v-else class="info-value">
                                {{ entityData.customerName || '-' }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="info-item" @dblclick="handleFieldEdit('position')">
                        <span class="info-label">职位</span>
                        <div class="info-value-wrapper">
                            <template v-if="editingField === 'position'">
                                <el-input
                                    v-model="editingValue"
                                    size="small"
                                    placeholder="请输入职位"
                                    @blur="handleSaveField"
                                    @keyup.enter="handleSaveField"
                                    v-focus
                                />
                            </template>
                            <span v-else class="info-value">
                                {{ entityData.position || '-' }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="info-row">
                    <div class="info-item" @dblclick="handleFieldEdit('phone')">
                        <span class="info-label">手机</span>
                        <div class="info-value-wrapper">
                            <template v-if="editingField === 'phone'">
                                <el-input
                                    v-model="editingValue"
                                    size="small"
                                    placeholder="请输入手机号码"
                                    @blur="handleSaveField"
                                    @keyup.enter="handleSaveField"
                                    v-focus
                                />
                            </template>
                            <span v-else class="info-value">
                                {{ entityData.phone || '-' }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="info-item" @dblclick="handleFieldEdit('email')">
                        <span class="info-label">邮箱</span>
                        <div class="info-value-wrapper">
                            <template v-if="editingField === 'email'">
                                <el-input
                                    v-model="editingValue"
                                    size="small"
                                    placeholder="请输入邮箱"
                                    @blur="handleSaveField"
                                    @keyup.enter="handleSaveField"
                                    v-focus
                                />
                            </template>
                            <span v-else class="info-value">
                                {{ entityData.email || '-' }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">创建时间</span>
                        <div class="info-value-wrapper">
                            <span class="info-value">
                                {{ entityData.createTime || '-' }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="info-item" @dblclick="handleFieldEdit('responsiblePersonId')">
                        <span class="info-label">负责人</span>
                        <div class="info-value-wrapper">
                            <template v-if="editingField === 'responsiblePersonId'">
                                <el-select
                                    v-model="editingValue"
                                    size="small"
                                    placeholder="请选择负责人"
                                    @blur="handleSaveField"
                                    v-focus
                                >
                                    <el-option
                                        v-for="user in userOptions"
                                        :key="user.value"
                                        :label="user.label"
                                        :value="user.value"
                                    />
                                </el-select>
                            </template>
                            <span v-else class="info-value">
                                {{ getUserName(entityData.responsiblePersonId) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ContactEntity } from '@/views/ContactManagement/types';
import { Star, StarFilled } from '@element-plus/icons-vue';
import { onMounted, ref, watch } from 'vue';
import { UserOption } from '~/types/user';

export interface Action {
    label: string;
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default';
    icon?: string;
    size?: 'large' | 'default' | 'small';
    disabled?: boolean;
    handler?: (data: any) => void;
}

interface Props {
    entityData: ContactEntity;
    modelName: string;
    actions?: Action[];
    userOptions?: any[];  // 用户选项列表
}

defineOptions({
    name: 'ContactHeaderTab'
});

const props = defineProps<Props>();



const emit = defineEmits<{
    (e: 'update:entity', value: Record<string, any>): void;
    (e: 'action', action: Action): void;
    (e: 'toggle-follow'): void;
}>();

// 用户列表
const userOptions = ref<UserOption[]>([]);
const loading = ref(false);

// 编辑状态控制
const editingField = ref('');
const editingValue = ref('');

// 初始化用户选项
const initUserOptions = () => {
    if (props.userOptions && props.userOptions.length > 0) {
        userOptions.value = props.userOptions.map(user => ({
            value: user.id || user.userId,
            label: user.nickName || user.userName || user.name
        }));
    }
};

// 监听用户选项变化
watch(() => props.userOptions, () => {
    initUserOptions();
}, { immediate: true });



// 处理关注状态切换
const handleToggleFollow = () => {
    emit('toggle-follow');
};

// 获取用户名称
const getUserName = (userId: number | string | undefined) => {
    if (!userId) return '-';
    const user = userOptions.value.find(u => u.value === userId as number);
    return user ? user.label : userId;
};

// 处理字段编辑
const handleFieldEdit = (field: string) => {
    editingField.value = field;
    editingValue.value = (props.entityData as any)[field] || '';
};

// 保存字段
const handleSaveField = async () => {
    if (editingField.value && editingValue.value !== (props.entityData as any)[editingField.value]) {
        try {
            const updatedData = {
                ...props.entityData,
                [editingField.value]: editingValue.value
            };
            
            emit('update:entity', updatedData);
        } catch (error) {
            console.error('保存失败:', error);
        }
    }
    
    editingField.value = '';
    editingValue.value = '';
};

// 处理操作按钮点击
const handleActionClick = (action: Action) => {
    if (action.handler) {
        action.handler(props.entityData);
    }
    emit('action', action);
};

// 获取按钮样式类
const getButtonClass = (action: Action) => {
    const classes = ['glass-button'];
    if (action.type === 'success') classes.push('glass-button--success');
    if (action.type === 'danger') classes.push('glass-button--danger');
    if (action.type === 'primary') classes.push('glass-button--primary');
    return classes.join(' ');
};

// 自定义指令：自动聚焦
const vFocus = {
    mounted: (el: HTMLElement) => {
        const input = el.querySelector('input') || el.querySelector('.el-input__inner');
        if (input) {
            (input as HTMLElement).focus();
        }
    }
};

onMounted(() => {
    initUserOptions();
});
</script>

<style scoped>
.contact-header-tab {
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    margin: 0;
    padding: 0;
}

.header-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 8px 12px;
    background: #fff;
    border-bottom: 1px solid #e5e7eb;
    position: relative;
}

.title-area {
    flex: 1;
}

.entity-type {
    font-size: 11px;
    color: rgba(107, 114, 128, 0.8);
    margin-bottom: 2px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.entity-name {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 15px;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
    line-height: 1.1;
    text-shadow: 0 1px 3px rgba(255, 255, 255, 0.8);
}

.star-icon {
    font-size: 14px;
    color: rgba(209, 213, 219, 0.8);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.6));
    
    &:hover {
        color: #fbbf24;
        transform: scale(1.1) rotate(5deg);
        filter: drop-shadow(0 2px 4px rgba(251, 191, 36, 0.3));
    }
    
    &.star-filled {
        color: #fbbf24;
        filter: drop-shadow(0 2px 4px rgba(251, 191, 36, 0.3));
    }
}

.action-buttons {
    display: flex;
    gap: 4px;
    align-items: center;
}

.glass-button {
    padding: 6px 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: #fff;
    color: #606266;
    font-weight: 400;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 3px;
    outline: none;
}

.glass-button:hover {
    color: #409eff;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
}

.glass-button--primary {
    background: #409eff;
    color: white;
    border-color: #409eff;
}

.glass-button--primary:hover {
    background: #66b1ff;
    border-color: #66b1ff;
    color: white;
}

.glass-button--success {
    background: #67c23a;
    color: white;
    border-color: #67c23a;
}

.glass-button--success:hover {
    background: #85ce61;
    border-color: #85ce61;
    color: white;
}

.glass-button--danger {
    background: #f56c6c;
    color: white;
    border-color: #f56c6c;
}

.glass-button--danger:hover {
    background: #f78989;
    border-color: #f78989;
    color: white;
}

.button-icon {
    font-size: 14px;
}

.info-section {
    padding: 8px 12px;
}

.info-grid {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.info-row {
    display: flex;
    gap: 16px;
}

.info-item {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 2px 4px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    min-height: 20px;
}

.info-item:hover {
    background: rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.info-label {
    font-size: 11px;
    color: rgba(107, 114, 128, 0.9);
    font-weight: 500;
    min-width: 45px;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.info-value-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
}

.info-value {
    font-size: 12px;
    color: #1f2937;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.6);
}

.info-item .el-input,
.info-item .el-select {
    width: 150px;
}

:deep(.el-input__wrapper) {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.9) 0%, 
        rgba(255, 255, 255, 0.7) 100%);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 4px;
    box-shadow: 
        0 1px 3px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.2s;
    padding: 1px 8px;
}

:deep(.el-input__wrapper:hover) {
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 
        0 1px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

:deep(.el-input__wrapper.is-focus) {
    border-color: rgba(59, 130, 246, 0.7);
    box-shadow: 
        0 0 0 2px rgba(59, 130, 246, 0.2),
        0 1px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

:deep(.el-select .el-input__wrapper) {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.9) 0%, 
        rgba(255, 255, 255, 0.7) 100%);
}

:deep(.el-input__inner) {
    font-size: 12px;
    line-height: 20px;
}

@media (max-width: 768px) {
    .header-section {
        flex-direction: column;
        gap: 6px;
        align-items: flex-start;
        padding: 6px 10px 4px 10px;
    }
    
    .action-buttons {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 3px;
    }
    
    .info-section {
        padding: 4px 10px 6px 10px;
    }
    
    .info-row {
        flex-direction: column;
        gap: 2px;
    }
    
    .info-item {
        gap: 4px;
        min-height: 18px;
        padding: 1px 2px;
    }
    
    .entity-name {
        font-size: 13px;
    }
    
    .info-item .el-input,
    .info-item .el-select {
        width: 110px;
    }
    
    .glass-button {
        padding: 4px 8px;
        font-size: 12px;
    }
}
</style>