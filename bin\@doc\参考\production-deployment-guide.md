# 生产环境部署配置指南

## 📋 配置文件使用说明

### 🎯 **配置文件层级结构**

```
ruoyi-admin/src/main/resources/
├── application.yml              # 基础配置（总是加载）
├── application-prod.yml         # 生产环境配置（覆盖基础配置）
├── application-druid.yml        # 数据源配置
└── application-sharding.yml     # 分表配置（CRM模块）
```

### ⚡ **配置加载机制**

#### 开发环境
```bash
# 默认启动（开发环境）
java -jar ruoyi-admin.jar

# 加载顺序：
# 1. application.yml (基础配置)
# 2. application-druid.yml (数据源配置)
# 3. application-sharding.yml (分表配置)
```

#### 生产环境
```bash
# 生产环境启动
java -jar ruoyi-admin.jar --spring.profiles.active=prod

# 加载顺序：
# 1. application.yml (基础配置)
# 2. application-druid.yml (数据源配置)
# 3. application-sharding.yml (分表配置)
# 4. application-prod.yml (生产配置，优先级最高)
```

### 🔧 **关键配置差异对比**

| 配置项 | 开发环境 (application.yml) | 生产环境 (application-prod.yml) |
|--------|---------------------------|--------------------------------|
| **端口** | 8080 | 8080 |
| **日志级别** | debug | info |
| **文件路径** | D:/ruoyi/uploadPath | /home/<USER>/uploadPath |
| **Redis密码** | 无 | your_redis_password |
| **数据库连接池** | 较小 | 更大 (max: 100) |
| **Token密钥** | 简单密钥 | 强密钥 |
| **Swagger** | 启用 | 禁用 |
| **热部署** | 启用 | 禁用 |

### 🚀 **生产环境部署步骤**

#### 1. 环境变量配置

创建 `production.env` 文件：

```bash
# 数据库配置
SPRING_DATASOURCE_DRUID_URL=********************************************************************************************************************************************
SPRING_DATASOURCE_DRUID_USERNAME=crm_user
SPRING_DATASOURCE_DRUID_PASSWORD=your_strong_db_password

# 连接池配置
SPRING_DATASOURCE_DRUID_INITIAL_SIZE=20
SPRING_DATASOURCE_DRUID_MIN_IDLE=30
SPRING_DATASOURCE_DRUID_MAX_ACTIVE=200

# Druid监控配置
SPRING_DATASOURCE_DRUID_ALLOW=10.0.0.0/8,172.16.0.0/12,192.168.0.0/16
SPRING_DATASOURCE_DRUID_ADMIN_USERNAME=druid_admin
SPRING_DATASOURCE_DRUID_ADMIN_PASSWORD=your_druid_password
```

#### 2. 启动脚本

创建 `start-prod.sh`：

```bash
#!/bin/bash

# 加载环境变量
source production.env

# 设置JVM参数
export JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# 启动应用
java $JAVA_OPTS \
  -Dspring.profiles.active=prod \
  -Dfile.encoding=UTF-8 \
  -Duser.timezone=GMT+08 \
  -jar ruoyi-admin.jar \
  --server.port=8080 \
  --logging.file.path=/home/<USER>/logs
```

#### 3. Docker部署

创建 `Dockerfile`：

```dockerfile
FROM openjdk:8-jre-alpine

# 设置工作目录
WORKDIR /app

# 复制应用文件
COPY ruoyi-admin.jar app.jar

# 创建日志目录
RUN mkdir -p /home/<USER>/logs

# 设置环境变量
ENV SPRING_PROFILES_ACTIVE=prod
ENV JAVA_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC"

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

创建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  crm-app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_DRUID_URL=*******************************************************************************************************************************************
      - SPRING_DATASOURCE_DRUID_USERNAME=root
      - SPRING_DATASOURCE_DRUID_PASSWORD=your_mysql_password
    volumes:
      - ./logs:/home/<USER>/logs
      - ./uploads:/home/<USER>/uploadPath
    depends_on:
      - mysql
      - redis
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=your_mysql_password
      - MYSQL_DATABASE=crm41
      - MYSQL_USER=crm_user
      - MYSQL_PASSWORD=your_db_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    command: redis-server --requirepass your_redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
```

### 🔒 **安全配置检查清单**

#### 数据库安全
- [ ] 使用专用数据库用户，避免使用root
- [ ] 设置强密码
- [ ] 限制数据库访问IP
- [ ] 启用SSL连接

#### Redis安全
- [ ] 设置Redis密码
- [ ] 绑定特定IP地址
- [ ] 禁用危险命令

#### 应用安全
- [ ] 使用强Token密钥
- [ ] 关闭Swagger文档
- [ ] 配置XSS防护
- [ ] 设置合适的CORS策略

#### 系统安全
- [ ] 使用非root用户运行应用
- [ ] 配置防火墙规则
- [ ] 定期更新系统补丁
- [ ] 配置日志监控

### 📊 **性能优化配置**

#### JVM调优参数
```bash
# G1垃圾收集器配置
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:G1HeapRegionSize=16m

# 内存配置
-Xms2g -Xmx4g
-XX:MetaspaceSize=256m
-XX:MaxMetaspaceSize=512m

# GC日志
-XX:+PrintGC
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
-Xloggc:/home/<USER>/logs/gc.log
```

#### 数据库连接池优化
```yaml
spring:
  datasource:
    druid:
      initialSize: 20          # 初始连接数
      minIdle: 30             # 最小空闲连接
      maxActive: 200          # 最大活跃连接
      maxWait: 60000          # 获取连接等待超时时间
      timeBetweenEvictionRunsMillis: 60000    # 检测空闲连接间隔
      minEvictableIdleTimeMillis: 300000      # 最小空闲时间
      maxEvictableIdleTimeMillis: 900000      # 最大空闲时间
```

### 📈 **监控配置**

#### 启用Spring Boot Actuator
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
```

#### 配置Prometheus监控
```yaml
management:
  metrics:
    export:
      prometheus:
        enabled: true
```

### 🔄 **配置热更新**

#### 使用Spring Cloud Config
```yaml
spring:
  cloud:
    config:
      uri: http://config-server:8888
      profile: prod
      label: master
```

#### 配置刷新端点
```yaml
management:
  endpoints:
    web:
      exposure:
        include: refresh
```

### 📝 **部署验证**

#### 健康检查
```bash
# 应用健康检查
curl http://localhost:8080/actuator/health

# 数据库连接检查
curl http://localhost:8080/actuator/health/db

# Redis连接检查
curl http://localhost:8080/actuator/health/redis
```

#### 功能验证
```bash
# API接口测试
curl http://localhost:8080/crm/table-sharding/config

# 登录功能测试
curl -X POST http://localhost:8080/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

---

## 总结

### 🎯 **关键要点**

1. **生产环境使用两个配置文件**：
   - `application.yml` (基础配置)
   - `application-prod.yml` (生产环境覆盖配置)

2. **启动命令**：
   ```bash
   java -jar ruoyi-admin.jar --spring.profiles.active=prod
   ```

3. **配置优先级**：
   - 环境变量 > 命令行参数 > application-prod.yml > application.yml

4. **分表功能**：
   - 已在两个配置文件中都启用了 `sharding` profile
   - 生产和开发环境都支持分表功能

### ✅ **现在的配置状态**

- ✅ `application.yml` 包含基础配置和分表支持
- ✅ `application-prod.yml` 包含生产环境优化配置和分表支持
- ✅ 两个环境都会加载分表配置
- ✅ 生产环境配置会覆盖开发环境的相应配置项 