# BPMN 2.0 标准详解

## 一、BPMN 2.0 是什么？

BPMN（Business Process Model and Notation）2.0 是由对象管理组织（OMG）维护的行业标准，用于业务流程建模。它提供了一套统一的图形符号和规范，使业务分析师和技术人员能够用相同的"语言"描述业务流程。

**关键特性：**
- 标准化：被ISO/IEC 19510认可为国际标准
- 可视化：通过图形元素直观展示流程
- 可执行：支持从模型直接生成可执行代码
- 多角色适用：业务人员和技术人员都能理解

## 二、BPMN 2.0 文件格式

BPMN 2.0 流程定义以XML文件形式存储，扩展名通常为.bpmn或.bpmn20.xml。文件结构示例：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL">
  <process id="approvalProcess" name="审批流程">
    <startEvent id="start"/>
    <userTask id="managerApproval" name="经理审批"/>
    <endEvent id="end"/>
    <sequenceFlow sourceRef="start" targetRef="managerApproval"/>
    <sequenceFlow sourceRef="managerApproval" targetRef="end"/>
  </process>
</definitions>
```

## 三、核心元素分类

```mermaid
graph LR
    A[BPMN元素] --> B[流对象]
    A --> C[连接对象]
    A --> D[泳道]
    A --> E[工件]
    
    B --> B1[事件]
    B --> B2[活动]
    B --> B3[网关]
    
    C --> C1[顺序流]
    C --> C2[消息流]
    C --> C3[关联]
    
    style A fill:#f5f5f5,stroke:#333
    classDef category fill:#e3f2fd,stroke:#333;
    class B,C,D,E category;
```

### 1. 事件（Events）
- 开始事件：圆形，单细线边框
- 中间事件：双圆环
- 结束事件：圆形，粗线边框

### 2. 活动（Activities）
- 任务：圆角矩形
- 子流程：圆角矩形带"+"号

### 3. 网关（Gateways）
- 菱形，内部符号区分类型：
  - ×：互斥网关
  - +：并行网关
  - ○：包容网关

## 四、与Activiti的关系

Activiti 是BPMN 2.0标准的实现引擎，支持：
- 解析.bpmn文件
- 执行流程实例
- 提供API操作流程

```mermaid
graph TB
    A[BPMN设计器] -->|导出.bpmn文件| B[Activiti引擎]
    B --> C[执行流程]
    B --> D[管理任务]
    B --> E[记录历史]
    
    classDef system fill:#bbdefb,stroke:#333;
    class A,B system;
```

## 五、实际应用示例

**采购审批流程.bpmn**
```xml
<process id="purchaseApproval" name="采购审批">
  <startEvent id="start"/>
  <userTask id="deptReview" name="部门审核">
    <documentation>检查采购必要性</documentation>
  </userTask>
  <exclusiveGateway id="amountCheck"/>
  <userTask id="managerApprove" name="经理审批"/>
  <endEvent id="end"/>
  
  <sequenceFlow sourceRef="start" targetRef="deptReview"/>
  <sequenceFlow sourceRef="deptReview" targetRef="amountCheck"/>
  <sequenceFlow sourceRef="amountCheck" targetRef="managerApprove" 
                name="金额>5000">
    <conditionExpression xsi:type="tFormalExpression">
      ${amount > 5000}
    </conditionExpression>
  </sequenceFlow>
  <sequenceFlow sourceRef="amountCheck" targetRef="end" name="金额<=5000"/>
  <sequenceFlow sourceRef="managerApprove" targetRef="end"/>
</process>
```

## 六、BPMN设计工具详解

### 1. Camunda Modeler 深度解析

**基本特性：**
- 开源免费，基于Electron开发
- 支持BPMN 2.0标准所有元素
- 提供桌面版和Web嵌入版

**核心功能：**
```mermaid
graph LR
    A[Camunda Modeler] --> B[可视化设计]
    A --> C[属性配置]
    A --> D[XML导出]
    A --> E[扩展开发]
    
    B --> B1[拖拽节点]
    B --> B2[连线编辑]
    B --> B3[泳道管理]
    
    C --> C1[任务配置]
    C --> C2[网关条件]
    C --> C3[监听器设置]
    
    classDef func fill:#e8f5e9,stroke:#333;
    class B,C,D,E func;
```

**Vue集成方案：**

1. 使用bpmn-js库（Camunda官方维护）：
```javascript
// 安装依赖
npm install bpmn-js

// Vue组件示例
<template>
  <div ref="bpmnContainer"></div>
</template>

<script>
import BpmnModeler from 'bpmn-js/lib/Modeler';

export default {
  mounted() {
    const modeler = new BpmnModeler({
      container: this.$refs.bpmnContainer,
      // 配置自定义模块
      additionalModules: [
        require('bpmn-js-properties-panel'),
        require('bpmn-js-properties-panel/lib/provider/camunda')
      ]
    });
    
    // 加载空流程图
    modeler.createDiagram();
  }
}
</script>
```

**节点类型支持：**
- 事件：开始/中间/结束事件
- 任务：用户任务/服务任务/脚本任务
- 网关：互斥/并行/包容网关
- 子流程：嵌入/调用/事件子流程

### 2. 其他工具对比

| 特性        | Camunda Modeler | Bizagi Modeler | Signavio |
|------------|----------------|---------------|----------|
| 开源        | ✓              | ×             | ×        |
| Vue集成     | ✓              | ×             | ×        |
| 执行语义检查 | ✓              | ✓             | ✓        |
| 团队协作    | ×              | ✓             | ✓        |

### 3. 自定义扩展开发

可通过插件机制扩展：
- 自定义属性面板
- 添加新节点类型
- 集成业务规则引擎

## 七、验证工具
- BPMN.io 在线验证器
- Activiti 部署验证

## 七、学习资源
- OMG官方文档：www.omg.org/spec/BPMN
- BPMN 2.0手册（Bruce Silver著）
- Camunda BPMN教程
