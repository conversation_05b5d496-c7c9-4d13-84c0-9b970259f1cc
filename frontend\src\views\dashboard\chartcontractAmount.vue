<template>
    <div class="chart-container">
        <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="month" label="月份" width="180"></el-table-column>
            <el-table-column prop="amount" label="合同金额（元）" width="180"></el-table-column>
            <el-table-column prop="momGrowth" label="环比增长" width="180"></el-table-column>
            <el-table-column prop="yoyGrowth" label="同比增长" width="180"></el-table-column>
        </el-table>

        <div ref="chartcontractAmount" class="contract-chart"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    data() {
        return {
            chart: null,
            tableData: [
                { month: '2024-01', amount: 100000, momGrowth: 5, yoyGrowth: 10 },
                { month: '2024-02', amount: 120000, momGrowth: 20, yoyGrowth: 25 },
                { month: '2024-03', amount: 90000, momGrowth: -10, yoyGrowth: 5 },
                // 添加更多数据...
            ],
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart();
            window.addEventListener('resize', this.handleResize);
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.chart) {
            this.chart.dispose();
            this.chart = null;
        }
    },
    methods: {
        handleResize() {
            if (this.chart) {
                this.chart.resize();
            }
        },
        initChart() {
            if (this.chart) {
                this.chart.dispose();
            }

            const chartDom = this.$refs.chartcontractAmount;
            if (!chartDom) return;

            this.chart = echarts.init(chartDom);
            const option = {
                title: {
                    text: '合同金额趋势',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    data: this.tableData.map(item => item.month)
                },
                yAxis: {
                    type: 'value',
                    name: '金额（元）'
                },
                series: [
                    {
                        name: '合同金额',
                        type: 'line',
                        data: this.tableData.map(item => item.amount)
                    }
                ]
            };
            this.chart.setOption(option);
        }
    }
};
</script>

<style scoped>
.chart-container {
    width: 100%;
    height: 100%;
}
.contract-chart {
    width: 100%;
    height: 400px;
    margin-top: 20px;
}
</style>