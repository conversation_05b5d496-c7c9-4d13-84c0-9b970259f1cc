<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人团队成员管理功能详细实施方案</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
            margin-bottom: 20px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 25px;
            margin-bottom: 15px;
        }
        h4 {
            color: #7f8c8d;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .step {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }
        .file-tree {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 15px 0;
        }
        ol, ul {
            padding-left: 25px;
        }
        li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>联系人团队成员管理功能详细实施方案</h1>
        
        <div class="highlight">
            <strong>项目概述：</strong>基于现有CRM系统的用户层级管理基础设施，为联系人模块添加团队成员管理功能，实现超级管理员和团队管理员对联系人访问权限的精细化控制。
        </div>

        <h2>一、系统架构分析</h2>
        
        <h3>1.1 现有基础设施</h3>
        <p>系统已具备以下基础组件：</p>
        <ul>
            <li><strong>用户层级管理：</strong><code>crm_user_hierarchy</code>表和<code>v_user_hierarchy_relations</code>视图</li>
            <li><strong>权限控制：</strong>基于RBAC的<code>sys_role</code>、<code>sys_user_role</code>等表</li>
            <li><strong>数据权限：</strong><code>data_scope</code>字段支持1-全部数据，2-自定义，3-本部门，4-本部门及以下</li>
            <li><strong>团队服务：</strong><code>CrmUserHierarchyServiceImpl.getTeamMemberIds()</code>方法</li>
        </ul>

        <h3>1.2 技术栈确认</h3>
        <div class="file-tree">
后端技术栈：
├── Spring Boot + MyBatis
├── MySQL数据库
├── 若依框架权限体系
└── RESTful API设计

前端技术栈：
├── Vue 3 + TypeScript
├── Element Plus UI组件
├── Vite构建工具
└── 响应式设计
        </div>

        <h2>二、数据库设计与实施</h2>
        
        <h3>2.1 新增角色权限配置</h3>
        
        <div class="step">
            <h4>步骤1：创建新角色类型</h4>
            <p>在<code>sys_role</code>表中添加团队管理相关角色：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-sql">-- 插入超级管理员角色
INSERT INTO sys_role (
    role_id, role_name, role_key, role_sort, 
    data_scope, menu_check_strictly, dept_check_strictly, 
    status, del_flag, create_by, create_time, remark
) VALUES (
    100, '超级管理员', 'super_admin', 1, 
    '1', 1, 1, 
    '0', '0', 'admin', NOW(), '拥有系统全部数据访问权限'
);

-- 插入团队管理员角色
INSERT INTO sys_role (
    role_id, role_name, role_key, role_sort, 
    data_scope, menu_check_strictly, dept_check_strictly, 
    status, del_flag, create_by, create_time, remark
) VALUES (
    101, '团队管理员', 'team_admin', 2, 
    '4', 1, 1, 
    '0', '0', 'admin', NOW(), '管理本部门及下属部门的联系人团队'
);
</code></pre>
        </div>

        <h3>2.2 联系人团队成员关系表</h3>
        
        <div class="step">
            <h4>步骤2：创建团队成员关系表</h4>
            <p>设计专门的表来存储联系人与团队成员的关联关系：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-sql">CREATE TABLE crm_contact_team_members (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    contact_id BIGINT NOT NULL COMMENT '联系人ID，关联crm_contacts表',
    user_id BIGINT NOT NULL COMMENT '用户ID，关联sys_user表',
    role_type VARCHAR(20) NOT NULL DEFAULT 'member' COMMENT '角色类型：owner-负责人，admin-管理员，member-成员',
    permissions JSON COMMENT '权限列表：["view","edit","delete","assign"]',
    assigned_by BIGINT COMMENT '分配人ID',
    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
    status CHAR(1) DEFAULT '0' COMMENT '状态：0-正常，1-停用',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    
    INDEX idx_contact_id (contact_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_type (role_type),
    INDEX idx_status (status),
    UNIQUE KEY uk_contact_user (contact_id, user_id),
    
    FOREIGN KEY (contact_id) REFERENCES crm_contacts(contact_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES sys_user(user_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联系人团队成员关系表';
</code></pre>
        </div>

        <h3>2.3 权限验证视图</h3>
        
        <div class="step">
            <h4>步骤3：创建权限查询视图</h4>
            <p>为了提高查询效率，创建联合查询视图：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-sql">CREATE VIEW v_contact_team_permissions AS
SELECT 
    ctm.contact_id,
    ctm.user_id,
    u.user_name,
    u.nick_name,
    ctm.role_type,
    ctm.permissions,
    ctm.status,
    c.contact_name,
    c.company_name,
    sr.role_name as system_role,
    sr.data_scope,
    CASE 
        WHEN sr.data_scope = '1' THEN 1  -- 超级管理员
        WHEN sr.data_scope = '4' AND ctm.role_type = 'admin' THEN 1  -- 团队管理员
        WHEN ctm.role_type = 'owner' THEN 1  -- 负责人
        ELSE 0
    END as can_manage_team
FROM crm_contact_team_members ctm
LEFT JOIN sys_user u ON ctm.user_id = u.user_id
LEFT JOIN crm_contacts c ON ctm.contact_id = c.contact_id
LEFT JOIN sys_user_role sur ON u.user_id = sur.user_id
LEFT JOIN sys_role sr ON sur.role_id = sr.role_id
WHERE ctm.status = '0' AND u.status = '0';
</code></pre>
        </div>

        <h3>2.4 ER图：联系人团队成员关系</h3>
        <div class="mermaid" style="background:#fff;border:1px solid #eee;border-radius:6px;padding:16px;margin:16px 0;overflow-x:auto;">
erDiagram
    crm_contacts ||--o{ crm_contact_team_members : "1对多"
    sys_user ||--o{ crm_contact_team_members : "1对多"
    sys_user ||--o{ crm_contact_team_members : "分配人"
    crm_contact_team_members {
        BIGINT id PK "主键ID"
        BIGINT contact_id FK "联系人ID"
        BIGINT user_id FK "用户ID"
        VARCHAR role_type "角色类型"
        JSON permissions "权限列表"
        BIGINT assigned_by FK "分配人ID"
        DATETIME assigned_at "分配时间"
        CHAR status "状态"
        VARCHAR create_by "创建者"
        DATETIME create_time "创建时间"
        VARCHAR update_by "更新者"
        DATETIME update_time "更新时间"
        VARCHAR remark "备注"
    }
</div>
        <script type="module">
          import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
          mermaid.initialize({ startOnLoad: true });
        </script>

        <h2>三、后端API开发</h2>
        
        <h3>3.1 实体类设计</h3>
        
        <div class="step">
            <h4>步骤4：创建实体类</h4>
            <p>在<code>ruoyi-crm/src/main/java/com/ruoyi/crm/domain/</code>目录下创建：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-java">// CrmContactTeamMember.java
package com.ruoyi.crm.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 联系人团队成员对象 crm_contact_team_members
 */
public class CrmContactTeamMember extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 联系人ID */
    @Excel(name = "联系人ID")
    private Long contactId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 角色类型 */
    @Excel(name = "角色类型")
    private String roleType;

    /** 权限列表 */
    private List<String> permissions;

    /** 分配人ID */
    private Long assignedBy;

    /** 分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date assignedAt;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    // 关联查询字段
    private String userName;
    private String nickName;
    private String contactName;
    private String companyName;
    private String assignedByName;
    
    // getter和setter方法...
}
</code></pre>
        </div>

        <h3>3.2 Mapper接口设计</h3>
        
        <div class="step">
            <h4>步骤5：创建Mapper接口</h4>
            <p>在<code>ruoyi-crm/src/main/java/com/ruoyi/crm/mapper/</code>目录下创建：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-java">// CrmContactTeamMemberMapper.java
package com.ruoyi.crm.mapper;

import com.ruoyi.crm.domain.CrmContactTeamMember;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface CrmContactTeamMemberMapper {
    
    /**
     * 查询联系人团队成员列表
     */
    List<CrmContactTeamMember> selectContactTeamMemberList(CrmContactTeamMember member);
    
    /**
     * 根据联系人ID查询团队成员
     */
    List<CrmContactTeamMember> selectMembersByContactId(@Param("contactId") Long contactId);
    
    /**
     * 检查用户是否有联系人访问权限
     */
    CrmContactTeamMember checkUserPermission(@Param("contactId") Long contactId, 
                                           @Param("userId") Long userId);
    
    /**
     * 添加团队成员
     */
    int insertContactTeamMember(CrmContactTeamMember member);
    
    /**
     * 批量添加团队成员
     */
    int batchInsertMembers(@Param("members") List<CrmContactTeamMember> members);
    
    /**
     * 更新团队成员信息
     */
    int updateContactTeamMember(CrmContactTeamMember member);
    
    /**
     * 删除团队成员
     */
    int deleteContactTeamMember(@Param("id") Long id);
    
    /**
     * 批量删除团队成员
     */
    int deleteContactTeamMembers(@Param("ids") Long[] ids);
    
    /**
     * 移除联系人的所有团队成员
     */
    int removeAllMembersByContactId(@Param("contactId") Long contactId);
    
    /**
     * 获取用户可访问的联系人ID列表
     */
    List<Long> getAccessibleContactIds(@Param("userId") Long userId);
}
</code></pre>
        </div>

        <h3>3.3 Service层实现</h3>
        
        <div class="step">
            <h4>步骤6：实现Service层</h4>
            <p>创建服务接口和实现类：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-java">// ICrmContactTeamMemberService.java
package com.ruoyi.crm.service;

import com.ruoyi.crm.domain.CrmContactTeamMember;
import java.util.List;

public interface ICrmContactTeamMemberService {
    
    /**
     * 查询联系人团队成员列表
     */
    List<CrmContactTeamMember> selectContactTeamMemberList(CrmContactTeamMember member);
    
    /**
     * 根据联系人ID获取团队成员
     */
    List<CrmContactTeamMember> getTeamMembers(Long contactId);
    
    /**
     * 添加团队成员
     */
    int addTeamMember(CrmContactTeamMember member);
    
    /**
     * 批量添加团队成员
     */
    int batchAddMembers(Long contactId, List<Long> userIds, String roleType);
    
    /**
     * 更新成员角色
     */
    int updateMemberRole(Long id, String roleType, List<String> permissions);
    
    /**
     * 移除团队成员
     */
    int removeTeamMember(Long id);
    
    /**
     * 检查用户权限
     */
    boolean hasPermission(Long contactId, Long userId, String permission);
    
    /**
     * 检查是否可以管理团队
     */
    boolean canManageTeam(Long contactId, Long userId);
}
</code></pre>
        </div>

        <h3>3.4 Controller层实现</h3>
        
        <div class="step">
            <h4>步骤7：创建REST API控制器</h4>
            <p>在<code>ruoyi-crm/src/main/java/com/ruoyi/crm/controller/</code>目录下创建：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-java">// CrmContactTeamController.java
package com.ruoyi.crm.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.crm.domain.CrmContactTeamMember;
import com.ruoyi.crm.service.ICrmContactTeamMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 联系人团队管理Controller
 */
@RestController
@RequestMapping("/crm/contact/team")
public class CrmContactTeamController extends BaseController {
    
    @Autowired
    private ICrmContactTeamMemberService teamMemberService;
    
    /**
     * 获取联系人团队成员列表
     */
    @PreAuthorize("@ss.hasPermi('crm:contact:team:list')")
    @GetMapping("/list/{contactId}")
    public TableDataInfo list(@PathVariable Long contactId) {
        // 权限检查
        Long currentUserId = SecurityUtils.getUserId();
        if (!teamMemberService.hasPermission(contactId, currentUserId, "view")) {
            return getDataTable(null);
        }
        
        List<CrmContactTeamMember> list = teamMemberService.getTeamMembers(contactId);
        return getDataTable(list);
    }
    
    /**
     * 添加团队成员
     */
    @PreAuthorize("@ss.hasPermi('crm:contact:team:add')")
    @Log(title = "联系人团队", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmContactTeamMember member) {
        Long currentUserId = SecurityUtils.getUserId();
        
        // 检查管理权限
        if (!teamMemberService.canManageTeam(member.getContactId(), currentUserId)) {
            return AjaxResult.error("无权限管理此联系人团队");
        }
        
        member.setAssignedBy(currentUserId);
        member.setCreateBy(SecurityUtils.getUsername());
        
        return toAjax(teamMemberService.addTeamMember(member));
    }
    
    /**
     * 批量添加团队成员
     */
    @PreAuthorize("@ss.hasPermi('crm:contact:team:add')")
    @Log(title = "联系人团队", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public AjaxResult batchAdd(@RequestParam Long contactId, 
                              @RequestParam List<Long> userIds,
                              @RequestParam String roleType) {
        Long currentUserId = SecurityUtils.getUserId();
        
        if (!teamMemberService.canManageTeam(contactId, currentUserId)) {
            return AjaxResult.error("无权限管理此联系人团队");
        }
        
        return toAjax(teamMemberService.batchAddMembers(contactId, userIds, roleType));
    }
    
    /**
     * 更新成员角色
     */
    @PreAuthorize("@ss.hasPermi('crm:contact:team:edit')")
    @Log(title = "联系人团队", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}")
    public AjaxResult edit(@PathVariable Long id, @RequestBody CrmContactTeamMember member) {
        Long currentUserId = SecurityUtils.getUserId();
        
        if (!teamMemberService.canManageTeam(member.getContactId(), currentUserId)) {
            return AjaxResult.error("无权限管理此联系人团队");
        }
        
        member.setId(id);
        member.setUpdateBy(SecurityUtils.getUsername());
        
        return toAjax(teamMemberService.updateMemberRole(id, member.getRoleType(), member.getPermissions()));
    }
    
    /**
     * 移除团队成员
     */
    @PreAuthorize("@ss.hasPermi('crm:contact:team:remove')")
    @Log(title = "联系人团队", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        // 需要先查询成员信息以验证权限
        // 实现权限检查逻辑...
        
        return toAjax(teamMemberService.removeTeamMember(id));
    }
}
</code></pre>
        </div>

        <h2>四、前端组件开发</h2>
        
        <h3>4.1 TypeScript类型定义</h3>
        
        <div class="step">
            <h4>步骤8：定义TypeScript接口</h4>
            <p>在<code>frontend/src/types/</code>目录下创建类型定义：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-typescript">// contact-team.ts
export interface ContactTeamMember {
  id?: number;
  contactId: number;
  userId: number;
  userName?: string;
  nickName?: string;
  roleType: 'owner' | 'admin' | 'member';
  permissions: string[];
  assignedBy?: number;
  assignedByName?: string;
  assignedAt?: string;
  status: '0' | '1';
  remark?: string;
}

export interface TeamPermission {
  canView: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canAssign: boolean;
  canManageTeam: boolean;
}

export interface UserOption {
  userId: number;
  userName: string;
  nickName: string;
  deptName?: string;
  disabled?: boolean;
}

export interface TeamManagementProps {
  contactId: number;
  visible: boolean;
  readonly?: boolean;
}
</code></pre>
        </div>

        <h3>4.2 API服务层</h3>
        
        <div class="step">
            <h4>步骤9：创建API服务</h4>
            <p>在<code>frontend/src/api/</code>目录下创建API调用服务：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-typescript">// contact-team.ts
import request from '@/utils/request';
import { ContactTeamMember, UserOption } from '@/types/contact-team';

// 获取联系人团队成员列表
export function getTeamMembers(contactId: number) {
  return request({
    url: `/crm/contact/team/list/${contactId}`,
    method: 'get'
  });
}

// 添加团队成员
export function addTeamMember(data: ContactTeamMember) {
  return request({
    url: '/crm/contact/team',
    method: 'post',
    data
  });
}

// 批量添加团队成员
export function batchAddMembers(contactId: number, userIds: number[], roleType: string) {
  return request({
    url: '/crm/contact/team/batch',
    method: 'post',
    params: { contactId, userIds, roleType }
  });
}

// 更新成员角色
export function updateMemberRole(id: number, data: Partial<ContactTeamMember>) {
  return request({
    url: `/crm/contact/team/${id}`,
    method: 'put',
    data
  });
}

// 移除团队成员
export function removeTeamMember(id: number) {
  return request({
    url: `/crm/contact/team/${id}`,
    method: 'delete'
  });
}

// 获取可选用户列表
export function getAvailableUsers(deptId?: number) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: { deptId, status: '0' }
  });
}

// 检查用户权限
export function checkUserPermission(contactId: number, permission: string) {
  return request({
    url: `/crm/contact/team/permission/${contactId}`,
    method: 'get',
    params: { permission }
  });
}
</code></pre>
        </div>

        <h3>4.3 团队管理组件</h3>
        
        <div class="step">
            <h4>步骤10：创建Vue组件</h4>
            <p>在<code>frontend/src/components/</code>目录下创建团队管理组件：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-vue"><!-- ContactTeamManagement.vue -->
<template>
  <div class="team-management">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>团队成员管理</span>
          <el-button 
            v-if="canManageTeam" 
            type="primary" 
            size="small" 
            @click="showAddDialog = true"
          >
            添加成员
          </el-button>
        </div>
      </template>
      
      <!-- 团队成员列表 -->
      <el-table 
        :data="teamMembers" 
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column prop="nickName" label="姓名" width="120" />
        <el-table-column prop="userName" label="用户名" width="120" />
        <el-table-column prop="roleType" label="角色" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getRoleTagType(row.roleType)"
              size="small"
            >
              {{ getRoleLabel(row.roleType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="permissions" label="权限" min-width="200">
          <template #default="{ row }">
            <el-tag 
              v-for="perm in row.permissions" 
              :key="perm"
              size="small"
              style="margin-right: 5px;"
            >
              {{ getPermissionLabel(perm) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assignedAt" label="加入时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.assignedAt) }}
          </template>
        </el-table-column>
        <el-table-column 
          v-if="canManageTeam" 
          label="操作" 
          width="150" 
          fixed="right"
        >
          <template #default="{ row }">
            <el-button 
              type="text" 
              size="small" 
              @click="editMember(row)"
            >
              编辑
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              style="color: #f56c6c;"
              @click="removeMember(row)"
            >
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 添加成员对话框 -->
    <el-dialog 
      v-model="showAddDialog" 
      title="添加团队成员" 
      width="600px"
    >
      <el-form 
        ref="addFormRef" 
        :model="addForm" 
        :rules="addRules" 
        label-width="80px"
      >
        <el-form-item label="选择用户" prop="userIds">
          <el-select 
            v-model="addForm.userIds" 
            multiple 
            filterable 
            placeholder="请选择用户"
            style="width: 100%;"
          >
            <el-option 
              v-for="user in availableUsers" 
              :key="user.userId"
              :label="`${user.nickName}(${user.userName})`"
              :value="user.userId"
              :disabled="user.disabled"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="角色类型" prop="roleType">
          <el-radio-group v-model="addForm.roleType">
            <el-radio label="member">普通成员</el-radio>
            <el-radio label="admin">管理员</el-radio>
            <el-radio label="owner" :disabled="!isSuperAdmin">负责人</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="权限设置" prop="permissions">
          <el-checkbox-group v-model="addForm.permissions">
            <el-checkbox label="view">查看</el-checkbox>
            <el-checkbox label="edit">编辑</el-checkbox>
            <el-checkbox label="delete">删除</el-checkbox>
            <el-checkbox label="assign">分配</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="handleAddMembers">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { 
  getTeamMembers, 
  batchAddMembers, 
  removeTeamMember,
  getAvailableUsers,
  checkUserPermission
} from '@/api/contact-team';
import type { ContactTeamMember, UserOption } from '@/types/contact-team';
import { useUserStore } from '@/store/modules/user';

interface Props {
  contactId: number;
  visible: boolean;
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
});

const userStore = useUserStore();
const loading = ref(false);
const teamMembers = ref<ContactTeamMember[]>([]);
const availableUsers = ref<UserOption[]>([]);
const showAddDialog = ref(false);
const addFormRef = ref<FormInstance>();

// 表单数据
const addForm = reactive({
  userIds: [] as number[],
  roleType: 'member',
  permissions: ['view'] as string[]
});

// 表单验证规则
const addRules: FormRules = {
  userIds: [{ required: true, message: '请选择用户', trigger: 'change' }],
  roleType: [{ required: true, message: '请选择角色类型', trigger: 'change' }],
  permissions: [{ required: true, message: '请选择权限', trigger: 'change' }]
};

// 计算属性
const isSuperAdmin = computed(() => {
  return userStore.roles.includes('super_admin');
});

const canManageTeam = computed(() => {
  return !props.readonly && (isSuperAdmin.value || userStore.roles.includes('team_admin'));
});

// 方法
const loadTeamMembers = async () => {
  loading.value = true;
  try {
    const response = await getTeamMembers(props.contactId);
    teamMembers.value = response.rows || [];
  } catch (error) {
    console.error('加载团队成员失败:', error);
    ElMessage.error('加载团队成员失败');
  } finally {
    loading.value = false;
  }
};

const loadAvailableUsers = async () => {
  try {
    const response = await getAvailableUsers();
    availableUsers.value = response.rows?.map((user: any) => ({
      userId: user.userId,
      userName: user.userName,
      nickName: user.nickName,
      deptName: user.dept?.deptName,
      disabled: teamMembers.value.some(member => member.userId === user.userId)
    })) || [];
  } catch (error) {
    console.error('加载用户列表失败:', error);
  }
};

const handleAddMembers = async () => {
  if (!addFormRef.value) return;
  
  await addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await batchAddMembers(
          props.contactId, 
          addForm.userIds, 
          addForm.roleType
        );
        ElMessage.success('添加成员成功');
        showAddDialog.value = false;
        resetAddForm();
        await loadTeamMembers();
      } catch (error) {
        console.error('添加成员失败:', error);
        ElMessage.error('添加成员失败');
      }
    }
  });
};

const removeMember = async (member: ContactTeamMember) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除团队成员 "${member.nickName}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await removeTeamMember(member.id!);
    ElMessage.success('移除成员成功');
    await loadTeamMembers();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除成员失败:', error);
      ElMessage.error('移除成员失败');
    }
  }
};

const resetAddForm = () => {
  addForm.userIds = [];
  addForm.roleType = 'member';
  addForm.permissions = ['view'];
  addFormRef.value?.resetFields();
};

// 辅助方法
const getRoleTagType = (roleType: string) => {
  const typeMap: Record<string, string> = {
    owner: 'danger',
    admin: 'warning',
    member: 'info'
  };
  return typeMap[roleType] || 'info';
};

const getRoleLabel = (roleType: string) => {
  const labelMap: Record<string, string> = {
    owner: '负责人',
    admin: '管理员',
    member: '成员'
  };
  return labelMap[roleType] || '未知';
};

const getPermissionLabel = (permission: string) => {
  const labelMap: Record<string, string> = {
    view: '查看',
    edit: '编辑',
    delete: '删除',
    assign: '分配'
  };
  return labelMap[permission] || permission;
};

const formatDate = (dateStr: string) => {
  if (!dateStr) return '-';
  return new Date(dateStr).toLocaleDateString('zh-CN');
};

// 生命周期
onMounted(() => {
  if (props.visible && props.contactId) {
    loadTeamMembers();
    if (canManageTeam.value) {
      loadAvailableUsers();
    }
  }
});

// 监听props变化
watch(() => props.visible, (newVal) => {
  if (newVal && props.contactId) {
    loadTeamMembers();
    if (canManageTeam.value) {
      loadAvailableUsers();
    }
  }
});
</script>

<style scoped>
.team-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  text-align: right;
}
</style>
        </div>

        <h3>4.4 Tab集成配置</h3>
        
        <div class="step">
            <h4>步骤11：集成到联系人详情页Tab</h4>
            <p>团队成员管理功能作为联系人详情页的Tab组件，无需单独的菜单权限和路由配置。在<code>frontend/src/views/ContactManagement/config/index.ts</code>中添加Tab配置：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-typescript">// 在drawerConfig.menuItems中添加团队成员Tab
{
    key: 'team',
    label: '团队成员',
    icon: 'UserFilled',
    component: markRaw(ContactTeamManagement)
},</code></pre>
        </div>
        
        <div class="highlight">
            <strong>重要说明：</strong>
            <ul>
                <li>团队成员管理作为联系人详情页的Tab页签，与ContactActivityTab、ContactAttachmentTab等同级</li>
                <li>无需单独配置菜单权限和路由，仅作为抽屉Tab组件集成</li>
                <li>组件通过props接收contactId等参数，实现数据关联</li>
                <li>权限控制在组件内部处理，基于用户角色和团队成员权限</li>
            </ul>
        </div>

        <h2>五、权限控制实现</h2>
        
        <h3>5.1 权限检查工具类</h3>
        
        <div class="step">
            <h4>步骤11：创建权限检查工具</h4>
            <p>在<code>ruoyi-crm/src/main/java/com/ruoyi/crm/utils/</code>目录下创建：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-java">// ContactPermissionUtils.java
package com.ruoyi.crm.utils;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.crm.domain.CrmContactTeamMember;
import com.ruoyi.crm.service.ICrmContactTeamMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 联系人权限检查工具类
 */
@Component("contactPermissionChecker")
public class ContactPermissionUtils {
    
    @Autowired
    private ICrmContactTeamMemberService teamMemberService;
    
    /**
     * 检查当前用户是否有联系人的指定权限
     */
    public boolean hasPermission(Long contactId, String permission) {
        Long currentUserId = SecurityUtils.getUserId();
        return hasPermission(contactId, currentUserId, permission);
    }
    
    /**
     * 检查指定用户是否有联系人的指定权限
     */
    public boolean hasPermission(Long contactId, Long userId, String permission) {
        // 超级管理员拥有所有权限
        if (isSuperAdmin(userId)) {
            return true;
        }
        
        // 检查团队成员权限
        return teamMemberService.hasPermission(contactId, userId, permission);
    }
    
    /**
     * 检查当前用户是否可以管理团队
     */
    public boolean canManageTeam(Long contactId) {
        Long currentUserId = SecurityUtils.getUserId();
        return canManageTeam(contactId, currentUserId);
    }
    
    /**
     * 检查指定用户是否可以管理团队
     */
    public boolean canManageTeam(Long contactId, Long userId) {
        // 超级管理员可以管理所有团队
        if (isSuperAdmin(userId)) {
            return true;
        }
        
        // 团队管理员可以管理本部门及下属部门的联系人团队
        if (isTeamAdmin(userId)) {
            return teamMemberService.canManageTeam(contactId, userId);
        }
        
        // 联系人负责人可以管理团队
        CrmContactTeamMember member = teamMemberService.checkUserPermission(contactId, userId);
        return member != null && "owner".equals(member.getRoleType());
    }
    
    /**
     * 获取用户可访问的联系人ID列表
     */
    public List<Long> getAccessibleContactIds(Long userId) {
        if (isSuperAdmin(userId)) {
            // 超级管理员可以访问所有联系人，返回null表示无限制
            return null;
        }
        
        return teamMemberService.getAccessibleContactIds(userId);
    }
    
    /**
     * 检查是否为超级管理员
     */
    private boolean isSuperAdmin(Long userId) {
        List<String> roles = SecurityUtils.getLoginUser().getUser().getRoles()
            .stream()
            .map(role -> role.getRoleKey())
            .toList();
        return roles.contains("super_admin");
    }
    
    /**
     * 检查是否为团队管理员
     */
    private boolean isTeamAdmin(Long userId) {
        List<String> roles = SecurityUtils.getLoginUser().getUser().getRoles()
            .stream()
            .map(role -> role.getRoleKey())
            .toList();
        return roles.contains("team_admin");
    }
    
    /**
     * 获取默认权限列表
     */
    public static List<String> getDefaultPermissions(String roleType) {
        return switch (roleType) {
            case "owner" -> Arrays.asList("view", "edit", "delete", "assign");
            case "admin" -> Arrays.asList("view", "edit", "assign");
            case "member" -> Arrays.asList("view");
            default -> Arrays.asList("view");
        };
    }
}
</code></pre>
        </div>

        <h3>5.2 数据权限过滤</h3>
        
        <div class="step">
            <h4>步骤12：实现数据权限过滤</h4>
            <p>修改联系人查询逻辑，集成团队权限控制：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-java">// 在CrmContactsServiceImpl中添加权限过滤
@Override
public List<CrmContacts> selectCrmContactsList(CrmContacts crmContacts) {
    Long currentUserId = SecurityUtils.getUserId();
    
    // 获取用户可访问的联系人ID列表
    List<Long> accessibleContactIds = contactPermissionChecker.getAccessibleContactIds(currentUserId);
    
    // 如果返回null，表示超级管理员，无需过滤
    if (accessibleContactIds != null) {
        if (accessibleContactIds.isEmpty()) {
            // 用户没有任何联系人访问权限
            return new ArrayList<>();
        }
        // 设置查询条件，只查询有权限的联系人
        crmContacts.setAccessibleContactIds(accessibleContactIds);
    }
    
    return crmContactsMapper.selectCrmContactsList(crmContacts);
}
</code></pre>
        </div>

        <h2>六、测试与部署</h2>
        
        <h3>6.1 单元测试</h3>
        
        <div class="step">
            <h4>步骤13：编写单元测试</h4>
            <p>在<code>ruoyi-crm/src/test/java/</code>目录下创建测试类：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-java">// ContactTeamMemberServiceTest.java
@SpringBootTest
@Transactional
class ContactTeamMemberServiceTest {
    
    @Autowired
    private ICrmContactTeamMemberService teamMemberService;
    
    @Test
    void testAddTeamMember() {
        CrmContactTeamMember member = new CrmContactTeamMember();
        member.setContactId(1L);
        member.setUserId(2L);
        member.setRoleType("member");
        member.setPermissions(Arrays.asList("view"));
        
        int result = teamMemberService.addTeamMember(member);
        assertEquals(1, result);
    }
    
    @Test
    void testPermissionCheck() {
        boolean hasPermission = teamMemberService.hasPermission(1L, 2L, "view");
        assertTrue(hasPermission);
    }
    
    @Test
    void testBatchAddMembers() {
        List<Long> userIds = Arrays.asList(3L, 4L, 5L);
        int result = teamMemberService.batchAddMembers(1L, userIds, "member");
        assertEquals(3, result);
    }
}
</code></pre>
        </div>

        <h3>6.2 集成测试</h3>
        
        <div class="step">
            <h4>步骤14：API集成测试</h4>
            <p>创建API测试文件：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-http"># contact-team-api-test.http

### 获取团队成员列表
GET http://localhost:8080/crm/contact/team/list/1
Authorization: Bearer {{token}}

### 添加团队成员
POST http://localhost:8080/crm/contact/team
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "contactId": 1,
  "userId": 2,
  "roleType": "member",
  "permissions": ["view", "edit"]
}

### 批量添加团队成员
POST http://localhost:8080/crm/contact/team/batch?contactId=1&userIds=3,4,5&roleType=member
Authorization: Bearer {{token}}

### 更新成员角色
PUT http://localhost:8080/crm/contact/team/1
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "roleType": "admin",
  "permissions": ["view", "edit", "assign"]
}

### 移除团队成员
DELETE http://localhost:8080/crm/contact/team/1
Authorization: Bearer {{token}}
</code></pre>
        </div>

        <h3>6.3 部署配置</h3>
        
        <div class="step">
            <h4>步骤15：前端部署说明</h4>
            <p>团队成员管理功能的前端部署要点：</p>
        </div>
        
        <div class="success">
            <h4>前端部署清单：</h4>
            <ul>
                <li><strong>Vue组件部署：</strong>将ContactTeamManagement.vue组件部署到指定目录</li>
                <li><strong>Tab配置更新：</strong>在联系人详情页配置中添加团队成员Tab</li>
                <li><strong>API服务集成：</strong>部署相关的API调用服务文件</li>
                <li><strong>类型定义更新：</strong>添加TypeScript类型定义文件</li>
            </ul>
        </div>
        
        <div class="warning">
            <strong>重要提醒：</strong>
            <ul>
                <li>团队成员管理<strong>无需单独的菜单权限配置</strong></li>
                <li>团队成员管理<strong>无需单独的路由配置</strong></li>
                <li>该功能作为联系人详情页的内嵌Tab组件，与ContactActivityTab、ContactAttachmentTab等同级</li>
                <li>权限控制在组件内部实现，基于用户角色和团队成员权限</li>
            </ul>
        </div>
        
        <div class="step">
            <h4>步骤16：生产环境配置</h4>
            <p>配置生产环境的权限和性能优化：</p>
        </div>
        
        <div class="code-block">
<pre><code class="language-yaml"># application-prod.yml 配置示例
spring:
  datasource:
    # 数据库连接池配置
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # Redis缓存配置（用于权限缓存）
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

# 权限缓存配置
crm:
  permission:
    cache:
      enabled: true
      ttl: 300  # 5分钟缓存
      max-size: 10000
</code></pre>
        </div>

        <h2>七、性能优化建议</h2>
        
        <h3>7.1 数据库优化</h3>
        
        <div class="warning">
            <strong>重要提醒：</strong>在生产环境中，需要特别注意以下性能优化点：
        </div>
        
        <ul>
            <li><strong>索引优化：</strong>确保<code>crm_contact_team_members</code>表的关键字段都有适当的索引</li>
            <li><strong>查询优化：</strong>使用视图<code>v_contact_team_permissions</code>减少复杂关联查询</li>
            <li><strong>分页查询：</strong>对于大量数据的团队成员列表，实现分页查询</li>
            <li><strong>缓存策略：</strong>对频繁查询的权限信息进行Redis缓存</li>
        </ul>
        
        <h3>7.2 前端性能优化</h3>
        
        <ul>
            <li><strong>懒加载：</strong>团队管理组件采用懒加载方式，只在需要时加载</li>
            <li><strong>虚拟滚动：</strong>对于大量团队成员的列表，使用虚拟滚动技术</li>
            <li><strong>防抖处理：</strong>用户搜索和筛选操作添加防抖处理</li>
            <li><strong>状态管理：</strong>使用Pinia进行权限状态的全局管理</li>
        </ul>
        
        <h2>八、实施时间计划</h2>
        
        <table>
            <thead>
                <tr>
                    <th>阶段</th>
                    <th>任务内容</th>
                    <th>预估时间</th>
                    <th>负责人</th>
                    <th>交付物</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>第一阶段</td>
                    <td>数据库设计与创建</td>
                    <td>1-2天</td>
                    <td>后端开发</td>
                    <td>数据库表结构、索引、视图</td>
                </tr>
                <tr>
                    <td>第二阶段</td>
                    <td>后端API开发</td>
                    <td>3-4天</td>
                    <td>后端开发</td>
                    <td>实体类、Mapper、Service、Controller</td>
                </tr>
                <tr>
                    <td>第三阶段</td>
                    <td>权限控制实现</td>
                    <td>2-3天</td>
                    <td>后端开发</td>
                    <td>权限检查工具、数据过滤逻辑</td>
                </tr>
                <tr>
                    <td>第四阶段</td>
                    <td>前端组件开发</td>
                    <td>4-5天</td>
                    <td>前端开发</td>
                    <td>Vue组件、API服务、类型定义、Tab集成配置（无需菜单权限和路由）</td>
                </tr>
                <tr>
                    <td>第五阶段</td>
                    <td>集成测试</td>
                    <td>2-3天</td>
                    <td>全栈开发</td>
                    <td>单元测试、集成测试、API测试</td>
                </tr>
                <tr>
                    <td>第六阶段</td>
                    <td>部署上线</td>
                    <td>1-2天</td>
                    <td>运维开发</td>
                    <td>生产环境配置、性能监控</td>
                </tr>
            </tbody>
        </table>
        
        <div class="success">
            <strong>总计：</strong>预计13-19个工作日完成整个功能的开发和部署。
        </div>
        
        <h2>九、风险评估与应对</h2>
        
        <h3>9.1 技术风险</h3>
        
        <table>
            <thead>
                <tr>
                    <th>风险项</th>
                    <th>影响程度</th>
                    <th>发生概率</th>
                    <th>应对措施</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>权限逻辑复杂导致性能问题</td>
                    <td>高</td>
                    <td>中</td>
                    <td>提前进行性能测试，优化查询逻辑，使用缓存</td>
                </tr>
                <tr>
                    <td>数据库迁移失败</td>
                    <td>高</td>
                    <td>低</td>
                    <td>充分测试迁移脚本，准备回滚方案</td>
                </tr>
                <tr>
                    <td>前后端接口不匹配</td>
                    <td>中</td>
                    <td>中</td>
                    <td>使用API文档工具，进行接口测试</td>
                </tr>
                <tr>
                    <td>权限绕过安全漏洞</td>
                    <td>高</td>
                    <td>低</td>
                    <td>代码审查，安全测试，权限验证</td>
                </tr>
            </tbody>
        </table>
        
        <h3>9.2 业务风险</h3>
        
        <ul>
            <li><strong>用户接受度：</strong>新功能可能增加操作复杂度，需要提供详细的用户培训</li>
            <li><strong>数据一致性：</strong>权限变更可能影响现有数据访问，需要制定数据迁移策略</li>
            <li><strong>性能影响：</strong>权限检查可能影响系统性能，需要进行压力测试</li>
        </ul>
        
        <h2>十、后续扩展计划</h2>
        
        <div class="highlight">
            <strong>功能扩展方向：</strong>
        </div>
        
        <ol>
            <li><strong>批量操作：</strong>支持批量添加、移除、角色变更等操作</li>
            <li><strong>权限模板：</strong>预设常用的权限组合模板，简化配置</li>
            <li><strong>审批流程：</strong>重要权限变更需要审批流程</li>
            <li><strong>操作日志：</strong>详细记录所有团队成员变更操作</li>
            <li><strong>通知机制：</strong>权限变更时自动通知相关人员</li>
            <li><strong>移动端适配：</strong>开发移动端团队管理界面</li>
            <li><strong>API开放：</strong>提供第三方系统集成的API接口</li>
        </ol>
        
        <h2>十一、总结</h2>
        
        <div class="success">
            <p>本方案基于现有CRM系统的用户层级管理基础设施，通过新增团队成员关系表和权限控制逻辑，实现了联系人的精细化团队管理功能。方案具有以下特点：</p>
            
            <ul>
                <li><strong>架构合理：</strong>充分利用现有基础设施，减少开发成本</li>
                <li><strong>权限完善：</strong>支持多层级权限控制，满足不同业务需求</li>
                <li><strong>扩展性强：</strong>预留了后续功能扩展的接口和架构</li>
                <li><strong>性能优化：</strong>通过索引、缓存等手段保证系统性能</li>
                <li><strong>安全可靠：</strong>完善的权限验证和数据保护机制</li>
            </ul>
        </div>
        
        <div class="highlight">
            <strong>实施建议：</strong>建议按照本方案的阶段划分，循序渐进地实施各个功能模块，确保每个阶段都有明确的交付物和验收标准。同时，在开发过程中要注重代码质量和文档完善，为后续的维护和扩展打下良好基础。
        </div>
        
        <hr style="margin: 30px 0; border: none; border-top: 2px solid #eee;">
        
        <div style="text-align: center; color: #7f8c8d; font-size: 14px; margin-top: 30px;">
            <p>文档版本：v1.0 | 创建时间：2024年12月 | 最后更新：2024年12月</p>
            <p>CRM系统联系人团队成员管理功能详细实施方案</p>
        </div>
    </div>
</body>
</html>