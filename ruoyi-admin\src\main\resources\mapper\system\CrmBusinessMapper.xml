<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CrmBusinessMapper">
    
    <resultMap type="CrmBusiness" id="CrmBusinessResult">
        <result property="id"    column="id"    />
        <result property="businessName"    column="business_name"    />
        <result property="businessType"    column="business_type"    />
        <result property="relatedTable"    column="related_table"    />
        <result property="primaryField1"    column="primary_field1"    />
        <result property="primaryField2"    column="primary_field2"    />
        <result property="primaryField3"    column="primary_field3"    />
        <result property="functionDescription"    column="function_description"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectCrmBusinessVo">
        select id, business_name, business_type, related_table, primary_field1, primary_field2, primary_field3, function_description, created_at, updated_at from crm_business
    </sql>

    <select id="selectCrmBusinessList" parameterType="CrmBusiness" resultMap="CrmBusinessResult">
        <include refid="selectCrmBusinessVo"/>
        <where>  
            <if test="businessName != null  and businessName != ''"> and business_name like concat('%', #{businessName}, '%')</if>
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="relatedTable != null  and relatedTable != ''"> and related_table = #{relatedTable}</if>
            <if test="primaryField1 != null  and primaryField1 != ''"> and primary_field1 = #{primaryField1}</if>
            <if test="primaryField2 != null  and primaryField2 != ''"> and primary_field2 = #{primaryField2}</if>
            <if test="primaryField3 != null  and primaryField3 != ''"> and primary_field3 = #{primaryField3}</if>
            <if test="functionDescription != null  and functionDescription != ''"> and function_description = #{functionDescription}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectCrmBusinessById" parameterType="Long" resultMap="CrmBusinessResult">
        <include refid="selectCrmBusinessVo"/>
        where id = #{id}
    </select>

    <insert id="insertCrmBusiness" parameterType="CrmBusiness" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessName != null and businessName != ''">business_name,</if>
            <if test="businessType != null and businessType != ''">business_type,</if>
            <if test="relatedTable != null">related_table,</if>
            <if test="primaryField1 != null">primary_field1,</if>
            <if test="primaryField2 != null">primary_field2,</if>
            <if test="primaryField3 != null">primary_field3,</if>
            <if test="functionDescription != null">function_description,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessName != null and businessName != ''">#{businessName},</if>
            <if test="businessType != null and businessType != ''">#{businessType},</if>
            <if test="relatedTable != null">#{relatedTable},</if>
            <if test="primaryField1 != null">#{primaryField1},</if>
            <if test="primaryField2 != null">#{primaryField2},</if>
            <if test="primaryField3 != null">#{primaryField3},</if>
            <if test="functionDescription != null">#{functionDescription},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateCrmBusiness" parameterType="CrmBusiness">
        update crm_business
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessName != null and businessName != ''">business_name = #{businessName},</if>
            <if test="businessType != null and businessType != ''">business_type = #{businessType},</if>
            <if test="relatedTable != null">related_table = #{relatedTable},</if>
            <if test="primaryField1 != null">primary_field1 = #{primaryField1},</if>
            <if test="primaryField2 != null">primary_field2 = #{primaryField2},</if>
            <if test="primaryField3 != null">primary_field3 = #{primaryField3},</if>
            <if test="functionDescription != null">function_description = #{functionDescription},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmBusinessById" parameterType="Long">
        delete from crm_business where id = #{id}
    </delete>

    <delete id="deleteCrmBusinessByIds" parameterType="String">
        delete from crm_business where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>