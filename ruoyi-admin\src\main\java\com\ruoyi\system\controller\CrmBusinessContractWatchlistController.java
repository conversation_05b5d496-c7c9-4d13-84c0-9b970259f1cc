package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.domain.entity.CrmBusinessContractWatchlist;
import com.ruoyi.system.service.ICrmBusinessContractWatchlistService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 合同关注Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Api(tags = "合同关注管理")
@RestController
@RequestMapping("/system/watchlist")
public class CrmBusinessContractWatchlistController extends BaseController {
    @Autowired
    private ICrmBusinessContractWatchlistService crmBusinessContractWatchlistService;

    /**
     * 查询合同关注列表
     */
    @ApiOperation("获取合同关注列表")
    @PreAuthorize("@ss.hasPermi('system:watchlist:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusinessContractWatchlist crmBusinessContractWatchlist) {
        startPage();
        List<CrmBusinessContractWatchlist> list = crmBusinessContractWatchlistService
                .selectCrmBusinessContractWatchlistList(crmBusinessContractWatchlist);
        return getDataTable(list);
    }

    /**
     * 导出合同关注列表
     */
    @ApiOperation("导出合同关注列表")
    @PreAuthorize("@ss.hasPermi('system:watchlist:export')")
    @Log(title = "合同关注", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmBusinessContractWatchlist crmBusinessContractWatchlist) {
        List<CrmBusinessContractWatchlist> list = crmBusinessContractWatchlistService
                .selectCrmBusinessContractWatchlistList(crmBusinessContractWatchlist);
        ExcelUtil<CrmBusinessContractWatchlist> util = new ExcelUtil<CrmBusinessContractWatchlist>(
                CrmBusinessContractWatchlist.class);
        util.exportExcel(response, list, "合同关注数据");
    }

    /**
     * 获取合同关注详细信息
     */
    @ApiOperation("获取合同关注详细信息")
    @PreAuthorize("@ss.hasPermi('system:watchlist:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(value = "合同关注ID", required = true) @PathVariable("id") Long id) {
        return success(crmBusinessContractWatchlistService.selectCrmBusinessContractWatchlistById(id));
    }

    /**
     * 新增合同关注
     */
    @ApiOperation("新增合同关注")
    @PreAuthorize("@ss.hasPermi('system:watchlist:add')")
    @Log(title = "合同关注", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(
            @ApiParam(value = "合同关注实体", required = true) @RequestBody CrmBusinessContractWatchlist crmBusinessContractWatchlist) {
        return toAjax(
                crmBusinessContractWatchlistService.insertCrmBusinessContractWatchlist(crmBusinessContractWatchlist));
    }

    /**
     * 修改合同关注
     */
    @ApiOperation("修改合同关注")
    @PreAuthorize("@ss.hasPermi('system:watchlist:edit')")
    @Log(title = "合同关注", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(
            @ApiParam(value = "合同关注实体", required = true) @RequestBody CrmBusinessContractWatchlist crmBusinessContractWatchlist) {
        return toAjax(
                crmBusinessContractWatchlistService.updateCrmBusinessContractWatchlist(crmBusinessContractWatchlist));
    }

    /**
     * 删除合同关注
     */
    @ApiOperation("删除合同关注")
    @PreAuthorize("@ss.hasPermi('system:watchlist:remove')")
    @Log(title = "合同关注", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam(value = "要删除的合同关注ID数组", required = true) @PathVariable Long[] ids) {
        return toAjax(crmBusinessContractWatchlistService.deleteCrmBusinessContractWatchlistByIds(ids));
    }
}
