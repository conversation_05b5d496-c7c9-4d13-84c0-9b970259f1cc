// 筛选选项接口
export interface FilterOption {
  label: string;
  value: string | number;
  disabled?: boolean;
}

// 操作按钮接口
export interface ActionButton {
  key: string;
  label: string;
  icon?: string;
  props?: Record<string, any>;
  handler?: () => void;
}

// 搜索配置接口
export interface SearchConfig {
  placeholder: string;
  width?: string;
  icon?: string;
  debounceTime?: number;
}

// 筛选配置接口
export interface FilterConfig {
  label: string;
  options: FilterOption[];
  buttonStyle?: boolean;
  size?: 'default' | 'small' | 'large';
}

// 组件配置接口
export interface CommonFilterConfig {
  search?: SearchConfig;
  filter?: FilterConfig;
  actions?: ActionButton[];
} 