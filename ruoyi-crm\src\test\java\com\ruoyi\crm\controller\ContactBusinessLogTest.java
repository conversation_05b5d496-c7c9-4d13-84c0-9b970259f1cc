package com.ruoyi.crm.controller;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.crm.BaseTestCase;

/**
 * 测试联系人业务日志功能
 */
public class ContactBusinessLogTest extends BaseTestCase {

    private static final Logger logger = LoggerFactory.getLogger(ContactBusinessLogTest.class);

    @Autowired
    private CrmContactsController crmContactsController;

    @Test
    @DisplayName("创建联系人时AOP应该正确记录日志")
    public void testContactCreationBusinessLog() {
        logger.info("=== 开始测试联系人创建业务日志 ===");
        
        // 创建测试联系人数据
        CrmContacts contact = new CrmContacts();
        contact.setName("测试联系人AOP");
        contact.setPosition("测试职位");
        contact.setPhone("***********");
        contact.setEmail("<EMAIL>");
        contact.setResponsiblePersonId("1");

        logger.info("创建联系人请求数据: {}", contact);

        // 执行创建操作
        AjaxResult result = crmContactsController.addWithRecord(contact);

        logger.info("创建联系人响应: {}", result);

        // 验证操作成功
        assertEquals(200, result.get("code"));
        assertNotNull(result.get("data"));
        
        // 获取创建的联系人ID
        CrmContacts createdContact = (CrmContacts) result.get("data");
        Long contactId = createdContact.getId();
        assertNotNull(contactId);
        
        logger.info("创建的联系人ID: {}", contactId);
        logger.info("=== 联系人创建测试完成，请检查BusinessLogAspect日志输出 ===");
    }
}