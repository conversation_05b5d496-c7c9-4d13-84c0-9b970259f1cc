package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CrmBusinessApApprovalHistoryMapper;
import com.ruoyi.common.domain.entity.CrmBusinessApApprovalHistory;
import com.ruoyi.system.service.ICrmBusinessApApprovalHistoryService;

/**
 * 审批历史Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class CrmBusinessApApprovalHistoryServiceImpl implements ICrmBusinessApApprovalHistoryService 
{
    @Autowired
    private CrmBusinessApApprovalHistoryMapper crmBusinessApApprovalHistoryMapper;

    /**
     * 查询审批历史
     * 
     * @param historyId 审批历史主键
     * @return 审批历史
     */
    @Override
    public CrmBusinessApApprovalHistory selectCrmBusinessApApprovalHistoryByHistoryId(Long historyId)
    {
        return crmBusinessApApprovalHistoryMapper.selectCrmBusinessApApprovalHistoryByHistoryId(historyId);
    }

    /**
     * 查询审批历史列表
     * 
     * @param crmBusinessApApprovalHistory 审批历史
     * @return 审批历史
     */
    @Override
    public List<CrmBusinessApApprovalHistory> selectCrmBusinessApApprovalHistoryList(CrmBusinessApApprovalHistory crmBusinessApApprovalHistory)
    {
        return crmBusinessApApprovalHistoryMapper.selectCrmBusinessApApprovalHistoryList(crmBusinessApApprovalHistory);
    }

    /**
     * 新增审批历史
     * 
     * @param crmBusinessApApprovalHistory 审批历史
     * @return 结果
     */
    @Override
    public int insertCrmBusinessApApprovalHistory(CrmBusinessApApprovalHistory crmBusinessApApprovalHistory)
    {
        return crmBusinessApApprovalHistoryMapper.insertCrmBusinessApApprovalHistory(crmBusinessApApprovalHistory);
    }

    /**
     * 修改审批历史
     * 
     * @param crmBusinessApApprovalHistory 审批历史
     * @return 结果
     */
    @Override
    public int updateCrmBusinessApApprovalHistory(CrmBusinessApApprovalHistory crmBusinessApApprovalHistory)
    {
        return crmBusinessApApprovalHistoryMapper.updateCrmBusinessApApprovalHistory(crmBusinessApApprovalHistory);
    }

    /**
     * 批量删除审批历史
     * 
     * @param historyIds 需要删除的审批历史主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessApApprovalHistoryByHistoryIds(Long[] historyIds)
    {
        return crmBusinessApApprovalHistoryMapper.deleteCrmBusinessApApprovalHistoryByHistoryIds(historyIds);
    }

    /**
     * 删除审批历史信息
     * 
     * @param historyId 审批历史主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessApApprovalHistoryByHistoryId(Long historyId)
    {
        return crmBusinessApApprovalHistoryMapper.deleteCrmBusinessApApprovalHistoryByHistoryId(historyId);
    }
}
