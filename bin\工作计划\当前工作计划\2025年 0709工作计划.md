# 2025年 第28周工作计划 - CRM团队管理API开发

**目标:** 完成CRM团队管理模块的后端API开发，为前端提供数据接口支持。

| 任务ID | 核心任务 | 详细步骤 | 状态 |
| :--- | :--- | :--- | :--- |
| **API-01** | **创建控制器** | 创建 `CrmTeamController.java` 文件。 | 待处理 |
| **API-02** | **实现团队CRUD接口** | - `POST /team`: 创建新团队<br>- `GET /team/list`: 获取团队列表（支持分页和搜索）<br>- `GET /team/{id}`: 获取单个团队详情<br>- `PUT /team`: 更新团队信息<br>- `DELETE /team/{id}`: 删除团队 | 待处理 |
| **API-03** | **实现团队成员管理接口** | - `POST /team/member`: 向团队中添加成员<br>- `DELETE /team/member/{teamId}/{userId}`: 从团队中移除成员<br>- `GET /team/{id}/members`: 获取指定团队的成员列表 | 待处理 |
| **API-04** | **完善权限控制** | - 为所有接口添加 `@PreAuthorize` 注解，确保只有授权用户才能操作。 | 待处理 |
| **API-05** | **编写API文档** | - 使用Swagger或Apifox为所有接口生成清晰的API文档。 | 待处理 |
| **API-06** | **单元测试** | - (可选) 为关键业务逻辑编写单元测试。 | 待处理 |

---
*这是根据 `CRM统一团队成员管理方案0709.html` 中的第二阶段任务制定的计划。*
