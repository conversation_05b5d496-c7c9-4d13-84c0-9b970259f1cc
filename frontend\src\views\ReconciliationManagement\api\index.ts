import request from '@/utils/request';
import { Reconciliation, ReconciliationQuery, ReconciliationExportQuery } from '../types';

// 查询对账单列表
export function getReconciliationList(query: ReconciliationQuery) {
  return request({
    url: '/front/crm/reconciliation/list',
    method: 'get',
    params: query
  });
}

// 查询对账单详细
export function getReconciliation(id: number) {
  return request({
    url: '/front/crm/reconciliation/' + id,
    method: 'get'
  });
}

// 新增对账单
export function addReconciliation(data: Partial<Reconciliation>) {
  return request({
    url: '/front/crm/reconciliation',
    method: 'post',
    data: data
  });
}

// 修改对账单
export function updateReconciliation(data: Reconciliation) {
  return request({
    url: '/front/crm/reconciliation',
    method: 'put',
    data: data
  });
}

// 删除对账单
export function delReconciliation(id: number | number[]) {
  return request({
    url: '/front/crm/reconciliation/' + id,
    method: 'delete'
  });
}

// 提交对账单
export function submitReconciliation(id: number) {
    return request({
        url: '/front/crm/reconciliation/submit/' + id,
        method: 'post'
    });
}

// 导出对账单
export function exportReconciliation(query: ReconciliationExportQuery) {
    return request({
        url: '/front/crm/reconciliation/export',
        method: 'get',
        params: query,
        responseType: 'blob'
    });
}

// 获取未对账订单列表
export function getUnreconciledOrders(customerId: number, query: { pageNum: number; pageSize: number; orderNo?: string }) {
    return request({
        url: `/front/crm/reconciliation/unreconciled-orders/${customerId}`,
        method: 'get',
        params: query
    });
}
