---
description: 
globs: 
alwaysApply: true
---
# CRM模块开发规范

# 禁止使用BaseMapper

## 1. 包结构规范
### 1.1 通用代码
- 实体类：`com.ruoyi.common.domain.entity`
- Mapper接口：`com.ruoyi.common.mapper`
- DTO：`com.ruoyi.common.domain.dto`
- 枚举类：`com.ruoyi.common.enums`
- 异常类：`com.ruoyi.common.exception`

### 1.2 业务代码
- Controller：以`Crm`开头
- Service接口：以`ICrm`开头
- Service实现类：以`Crm`开头，`Impl`结尾
- 实体类：以`Crm`开头

## 2. 命名规范
### 2.1 类
- 大驼峰命名，接口以`I`开头，实现类以`Impl`结尾，实体类与表名对应（去前缀驼峰命名）

### 2.2 方法
- 小驼峰命名，查询以`get`、`list`、`query`开头，新增以`add`、`insert`开头，修改以`update`、`modify`开头，删除以`delete`、`remove`开头

### 2.3 变量
- 小驼峰命名，常量大写加下划线，布尔变量以`is`、`has`、`can`等开头

## 3. 代码规范
### 3.1 注释
- 类注释：作者、创建时间、描述
- 方法注释：参数、返回值、异常说明
- 关键代码：行内注释

### 3.2 异常处理
- 业务异常继承`BusinessException`
- 统一异常处理机制，清晰异常信息

### 3.3 事务处理
- 多库操作加事务注解并指定回滚异常，只读操作无事务注解

## 4. 接口规范
### 4.1 URL
- 以`/crm`开头，管理后台`/crm/admin`，前台`/crm/api`，小写字母加连字符

### 4.2 请求
- GET查询，POST新增，PUT修改，DELETE删除，参数校验，敏感数据加密

### 4.3 响应
- 统一格式，符合RESTful规范，数据脱敏，错误响应含错误码和信息
