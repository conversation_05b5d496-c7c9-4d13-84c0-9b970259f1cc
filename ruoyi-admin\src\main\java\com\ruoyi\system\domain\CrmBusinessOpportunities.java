package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 商机对象 crm_business_opportunities
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public class CrmBusinessOpportunities extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键，自增字段 */
    private Long id;

    /** 负责人ID */
    @Excel(name = "负责人ID")
    private Long managerId;

    /** 商机名称 */
    @Excel(name = "商机名称")
    private String opportunityName;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 商机金额 */
    @Excel(name = "商机金额")
    private BigDecimal opportunityAmount;

    /** 商机阶段 */
    @Excel(name = "商机阶段")
    private String opportunityStage;

    /** 赢单率 */
    @Excel(name = "赢单率")
    private BigDecimal winRate;

    /** 预计关闭日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预计关闭日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expectedCloseDate;

    /** 商机来源 */
    @Excel(name = "商机来源")
    private String opportunitySource;

    /** 商机类型 */
    @Excel(name = "商机类型")
    private String opportunityType;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 记录创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记录创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 记录更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记录更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setManagerId(Long managerId) 
    {
        this.managerId = managerId;
    }

    public Long getManagerId() 
    {
        return managerId;
    }
    public void setOpportunityName(String opportunityName) 
    {
        this.opportunityName = opportunityName;
    }

    public String getOpportunityName() 
    {
        return opportunityName;
    }
    public void setCustomerName(String customerName) 
    {
        this.customerName = customerName;
    }

    public String getCustomerName() 
    {
        return customerName;
    }
    public void setOpportunityAmount(BigDecimal opportunityAmount) 
    {
        this.opportunityAmount = opportunityAmount;
    }

    public BigDecimal getOpportunityAmount() 
    {
        return opportunityAmount;
    }
    public void setOpportunityStage(String opportunityStage) 
    {
        this.opportunityStage = opportunityStage;
    }

    public String getOpportunityStage() 
    {
        return opportunityStage;
    }
    public void setWinRate(BigDecimal winRate) 
    {
        this.winRate = winRate;
    }

    public BigDecimal getWinRate() 
    {
        return winRate;
    }
    public void setExpectedCloseDate(Date expectedCloseDate) 
    {
        this.expectedCloseDate = expectedCloseDate;
    }

    public Date getExpectedCloseDate() 
    {
        return expectedCloseDate;
    }
    public void setOpportunitySource(String opportunitySource) 
    {
        this.opportunitySource = opportunitySource;
    }

    public String getOpportunitySource() 
    {
        return opportunitySource;
    }
    public void setOpportunityType(String opportunityType) 
    {
        this.opportunityType = opportunityType;
    }

    public String getOpportunityType() 
    {
        return opportunityType;
    }
    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("managerId", getManagerId())
            .append("opportunityName", getOpportunityName())
            .append("customerName", getCustomerName())
            .append("opportunityAmount", getOpportunityAmount())
            .append("opportunityStage", getOpportunityStage())
            .append("winRate", getWinRate())
            .append("expectedCloseDate", getExpectedCloseDate())
            .append("opportunitySource", getOpportunitySource())
            .append("opportunityType", getOpportunityType())
            .append("remarks", getRemarks())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}
