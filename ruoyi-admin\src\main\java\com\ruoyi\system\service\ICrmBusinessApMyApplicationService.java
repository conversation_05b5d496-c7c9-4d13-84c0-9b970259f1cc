package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmBusinessApMyApplication;

/**
 * 我的申请Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface ICrmBusinessApMyApplicationService 
{
    /**
     * 查询我的申请
     * 
     * @param applicationId 我的申请主键
     * @return 我的申请
     */
    public CrmBusinessApMyApplication selectCrmBusinessApMyApplicationByApplicationId(Long applicationId);

    /**
     * 查询我的申请列表
     * 
     * @param crmBusinessApMyApplication 我的申请
     * @return 我的申请集合
     */
    public List<CrmBusinessApMyApplication> selectCrmBusinessApMyApplicationList(CrmBusinessApMyApplication crmBusinessApMyApplication);

    /**
     * 新增我的申请
     * 
     * @param crmBusinessApMyApplication 我的申请
     * @return 结果
     */
    public int insertCrmBusinessApMyApplication(CrmBusinessApMyApplication crmBusinessApMyApplication);

    /**
     * 修改我的申请
     * 
     * @param crmBusinessApMyApplication 我的申请
     * @return 结果
     */
    public int updateCrmBusinessApMyApplication(CrmBusinessApMyApplication crmBusinessApMyApplication);

    /**
     * 批量删除我的申请
     * 
     * @param applicationIds 需要删除的我的申请主键集合
     * @return 结果
     */
    public int deleteCrmBusinessApMyApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 删除我的申请信息
     * 
     * @param applicationId 我的申请主键
     * @return 结果
     */
    public int deleteCrmBusinessApMyApplicationByApplicationId(Long applicationId);
}
