# CRM412 项目开发指导原则

## 🚨 核心架构规则 - 必须遵守

## 计划
当我让你生成任务计划时，你需要遵循以下原则：
- **你要确保你最先生成的就是任务计划！而且是一定要生成的！**
- **不要直接开始写代码！** 你需要在开始前生成一个任务计划表！
- **你修改代码的时机只有一种，那就是我明确要求你修改！**

## 测试编译
- **一定注意** 不要动不动就整个项目test测试。直接指定测试类进行测试, 一整个测试就是5分钟，我有几个5分钟给你浪费。 
- 测试代码编译时，确保使用 `mvn clean test-compile` 命令，这样可以清理之前的编译结果并重新编译测试代码。
- 测试代码编译时，确保使用 `mvn clean test` 命令，这样可以清理之前的编译结果并重新编译测试代码。


## 文件 
- 计划
  - bin/task/ 下面就是我创建的工作计划
  - 比如 bin/task/对账单  下面就是对战单的工作计划
- xml代码位置
  - 所有的crm系统业务xml代码都在ruoyi-crm 项目下的resources/mapper目录下
- 代码位置
  - 所有的crm系统业务代码都在ruoyi-crm 项目下的src/main/java/com/ruoyi/crm目录下
- 业务公共代码的位置
  - 所有的crm系统业务公共代码都在ruoyi-crm 项目下的src/main/java/com/ruoyi/common目录下
- 格式
  - 如果我需要你给我创建文档 你最好给我创建html格式的
- 文件路径common 
  - 不要搞混了common文件夹和ruoyi-common项目，common文件夹是在ruoyi-crm下面的一个关于业务的公共文件夹，里面存放了一些公共的业务代码。ruoyi-common项目是一个独立的项目，主要是一些公共的工具类和配置。


## 准则
- 工作计划文件夹
  - **内容:** 所有每周工作计划都存储在 `bin/task/周工作计划/` 目录下。文件命名规则为 `YYYY年 第WW周工作计划.md`，例如 `2025年 第27周工作计划.md`。这是查找当前工作计划的唯一位置。

- 称呼
  - **内容:** The user prefers to be addressed as '兄弟' instead of '老板'.

- 替换规则
  - **内容:** 在使用 `edit_file` 工具生成 `code_edit` 内容时，`// ... existing code ...` 标记是用于指示保留文件中的原有代码。代理在生成 `code_edit` 时，必须确保精确地使用此标记来表示未修改的代码块，并且绝不能让 `// ... existing code ...` 标记本身替换掉文件中任何实际的代码内容。

- 流程引擎
  - **内容:** 当遇到Activiti历史记录为空的问题时，常见原因包括：1) ProcessDefinitionKey不匹配 - 查询历史记录时使用的key必须与BPMN文件中定义的process id完全一致 2) 历史记录配置问题 - 确保processEngineConfiguration.setHistory("full")和application.yml中activiti.history-level: full都正确配置 3) 数据库事务问题 - 历史记录的写入依赖于事务提交，测试环境中需要确保事务正确提交 4) 测试数据清理 - 避免在测试方法中过度清理历史数据，可能影响后续的历史查询。

- 在Mermaid流程图注意问题
  - **内容:** 在Mermaid流程图（flowchart）中，当节点内的文本包含换行符（<br/>）时，应避免使用半角括号 `()` 和双引号 `""`，因为它们有很大概率导致解析错误。最稳妥的解决方法是将这些符号替换为全角字符，例如 `（）` 和 `" "`。

- element ui 问题
  - **内容:** 在配置Element Plus时，不能简单使用app.use(ElementPlus)，需要提供配置选项：app.use(ElementPlus, { size: 'default', zIndex: 3000 })。简单的配置方式会导致问题，特别是MessageBox等弹窗组件可能无法正常显示。 

## 0 配置相关
数据库默认配置， 如果想要连接数据库用下面的配置
master:
    url: ${SPRING_DATASOURCE_DRUID_URL:**********************************************************************************************************************************************}
    username: mycrm41
    password: mycrm41

## 🚫 重要警告
当你会修改好多文件时，请务必遵循以下原则：
- ** 你修改代码的时机只有一种，那就是我明确要求你修改！ **
- ** 如果是多任务的话，我会告诉你，按照我们的任务计划表来执行！这个时候你才能修改和生产代码！ **

### ⛔ 绝对禁止的操作
- **绝对不要随意删除 common 目录下的文件！**
- **不要认为 common 和 crm 包重复就删除！**
- **不要破坏用户自定义的分层设计！**

## 📁 目录结构与职责划分

### 1. ruoyi-common 工程
```
ruoyi-common/
├── src/main/java/com/ruoyi/common/
```
- **用途**: 全局通用组件和工具
- **范围**: 整个项目的所有模块都可以使用
- **示例**: 通用工具类、全局配置、基础框架组件
- **原则**: 这里的代码是跨模块共享的

### 2. ruoyi-crm/src/main/java/com/ruoyi/common/
```
ruoyi-crm/
├── src/main/java/com/ruoyi/common/
│   ├── mapper/          # CRM模块通用Mapper
│   ├── service/         # CRM模块通用Service
│   ├── domain/          # CRM模块通用实体
│   └── utils/           # CRM模块通用工具
```
- **用途**: CRM模块内的通用组件（模块级通用）
- **范围**: 仅限CRM模块内使用的通用代码
- **示例**: CrmContactTeamMemberMapper、CrmBaseService等
- **重要**: 这是用户自定义的分层设计，符合模块化开发原则
- **✅ 明确规则**：
  - **Mapper接口**：直接放在 `com.ruoyi.common.mapper` 包下
  - **实体类**：直接放在 `com.ruoyi.common.domain` 包下
  - **通用工具类**：直接放在 `com.ruoyi.common.utils` 包下

### 3. ruoyi-crm/src/main/java/com/ruoyi/crm/
```
ruoyi-crm/
├── src/main/java/com/ruoyi/crm/
│   ├── controller/      # 业务控制器
│   ├── service/         # 业务服务实现
│   └── common/          # CRM业务相关通用组件
```
- **用途**: CRM模块的具体业务逻辑
- **范围**: 主要放置控制器和服务层实现
- **示例**: CrmContactController、CrmContactServiceImpl
- **✅ 明确规则**：
  - **Controller层**：放在 `com.ruoyi.crm.controller` 包下
  - **Service层**：放在 `com.ruoyi.crm.service` 包下
  - **业务相关组件**：其他业务相关的特定实现

## 🏗️ 架构设计优势
1. **模块化**: 每个模块可以有自己的通用组件层
2. **解耦**: 模块间依赖清晰，便于维护
3. **复用**: 模块内通用代码可在模块内复用
4. **扩展**: 新增模块时可以遵循相同的分层原则

## 📋 开发规范与最佳实践

### 代码放置规则
- 全局通用 → `ruoyi-common`
- CRM模块通用 → `ruoyi-crm/src/main/java/com/ruoyi/common/`
- CRM业务逻辑 → `ruoyi-crm/src/main/java/com/ruoyi/crm/`
- CRM业务通用 → `ruoyi-crm/src/main/java/com/ruoyi/crm/common/`

### 🎯 代码放置明细规则

#### ruoyi-crm/src/main/java/com/ruoyi/common/ 目录
```
com.ruoyi.common/
├── mapper/           # ✅ Mapper接口（如：CrmContactTeamMemberMapper）
├── domain/           # ✅ 实体类（如：CrmContactTeamMember）
├── service/          # ✅ 通用服务接口
└── utils/            # ✅ CRM模块通用工具类
```

#### ruoyi-crm/src/main/java/com/ruoyi/crm/ 目录
```
com.ruoyi.crm/
├── controller/       # ✅ 控制器层（如：CrmContactController）
├── service/          # ✅ 服务层实现（如：CrmContactServiceImpl）
└── common/           # ✅ 业务相关通用组件
```

#### 代码生成规则
- **生成的Mapper接口** → `ruoyi-crm/src/main/java/com/ruoyi/common/mapper/`
- **生成的实体类** → `ruoyi-crm/src/main/java/com/ruoyi/common/domain/`
- **手写的Controller** → `ruoyi-crm/src/main/java/com/ruoyi/crm/controller/`
- **手写的Service实现** → `ruoyi-crm/src/main/java/com/ruoyi/crm/service/`

### TypeHandler 处理规则
```java
// ✅ 正确：CRM模块专用的TypeHandler
com.ruoyi.crm.common.handler.ListStringTypeHandler

// ❌ 错误：不要放在全局common中
com.ruoyi.common.core.handler.ListStringTypeHandler
```

### MyBatis 配置规则
```xml
<!-- ✅ 正确：使用CRM模块的handler -->
<result property="permissions" column="permissions" 
        typeHandler="com.ruoyi.crm.common.handler.ListStringTypeHandler"/>

<!-- ✅ 正确：namespace指向CRM模块的common包 -->
<mapper namespace="com.ruoyi.common.mapper.CrmContactTeamMemberMapper">
```

## ⚠️ 常见错误与避免方法

### 错误示例
1. ❌ 删除 `ruoyi-crm/src/main/java/com/ruoyi/common/` 下的文件
2. ❌ 认为 common 和 crm 包名重复就要合并
3. ❌ 随意移动模块级通用组件到业务包中
4. ❌ 将CRM专用组件放到全局common中

### 正确做法
1. ✅ 保持用户设计的分层结构不变
2. ✅ 理解每个目录的职责和用途
3. ✅ 新增功能时遵循现有的分层结构
4. ✅ 模块级通用组件放在对应模块的 common 包下

## 🔧 技术实现要点

### Bean 冲突解决
- 保持不同包路径下的同名类，它们有不同的职责
- 通过包路径区分全局通用和模块通用

### 依赖注入规则
```java
// ✅ 正确：注入CRM模块的通用Mapper
@Autowired
private com.ruoyi.common.mapper.CrmContactTeamMemberMapper teamMemberMapper;
```

### 测试编写规则
- 测试类应该测试对应层级的功能
- 不要跨层级测试

## 📝 历史教训记录
- 曾经错误地删除或建议删除 common 目录下的文件
- 需要深刻理解用户的架构设计意图
- 不能简单地认为包名相似就是重复代码
- 模块化设计允许不同模块有相似的结构

## 🎯 开发检查清单

### 添加新功能时
- [ ] 确定功能属于哪个层级（全局/模块通用/业务）
- [ ] 选择正确的包路径
- [ ] 遵循现有的命名规范
- [ ] 不破坏现有的分层结构

### 修复问题时
- [ ] 理解问题的根本原因
- [ ] 不随意删除或移动文件
- [ ] 保持架构设计的一致性
- [ ] 验证修复不会破坏其他功能

---
**重要提醒：在进行任何架构性修改前，必须参考此文档！**