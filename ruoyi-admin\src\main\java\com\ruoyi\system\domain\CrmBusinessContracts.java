package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 合同对象 crm_business_contracts
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public class CrmBusinessContracts extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键，自增字段 */
    private Long id;

    /** 负责人ID */
    @Excel(name = "负责人ID")
    private Long managerId;

    /** 合同编号 */
    @Excel(name = "合同编号")
    private String contractNumber;

    /** 合同名称 */
    @Excel(name = "合同名称")
    private String contractName;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 报价单编号 */
    @Excel(name = "报价单编号")
    private String quotationNumber;

    /** 商机名称 */
    @Excel(name = "商机名称")
    private String opportunityName;

    /** 合同金额 */
    @Excel(name = "合同金额")
    private BigDecimal contractAmount;

    /** 下单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date orderDate;

    /** 合同开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /** 合同结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /** 客户签约人 */
    @Excel(name = "客户签约人")
    private String customerSignatory;

    /** 公司签约人 */
    @Excel(name = "公司签约人")
    private String companySignatory;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 合同类型 */
    @Excel(name = "合同类型")
    private String contractType;

    /** 合同影像 */
    @Excel(name = "合同影像")
    private String contractImage;

    /** 附件 */
    @Excel(name = "附件")
    private String attachments;

    /** 利润 */
    @Excel(name = "利润")
    private BigDecimal profit;

    /** 产品成本总金额 */
    @Excel(name = "产品成本总金额")
    private BigDecimal totalProductCost;

    /** 记录创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记录创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 记录更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记录更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setManagerId(Long managerId) 
    {
        this.managerId = managerId;
    }

    public Long getManagerId() 
    {
        return managerId;
    }
    public void setContractNumber(String contractNumber) 
    {
        this.contractNumber = contractNumber;
    }

    public String getContractNumber() 
    {
        return contractNumber;
    }
    public void setContractName(String contractName) 
    {
        this.contractName = contractName;
    }

    public String getContractName() 
    {
        return contractName;
    }
    public void setCustomerName(String customerName) 
    {
        this.customerName = customerName;
    }

    public String getCustomerName() 
    {
        return customerName;
    }
    public void setQuotationNumber(String quotationNumber) 
    {
        this.quotationNumber = quotationNumber;
    }

    public String getQuotationNumber() 
    {
        return quotationNumber;
    }
    public void setOpportunityName(String opportunityName) 
    {
        this.opportunityName = opportunityName;
    }

    public String getOpportunityName() 
    {
        return opportunityName;
    }
    public void setContractAmount(BigDecimal contractAmount) 
    {
        this.contractAmount = contractAmount;
    }

    public BigDecimal getContractAmount() 
    {
        return contractAmount;
    }
    public void setOrderDate(Date orderDate) 
    {
        this.orderDate = orderDate;
    }

    public Date getOrderDate() 
    {
        return orderDate;
    }
    public void setStartDate(Date startDate) 
    {
        this.startDate = startDate;
    }

    public Date getStartDate() 
    {
        return startDate;
    }
    public void setEndDate(Date endDate) 
    {
        this.endDate = endDate;
    }

    public Date getEndDate() 
    {
        return endDate;
    }
    public void setCustomerSignatory(String customerSignatory) 
    {
        this.customerSignatory = customerSignatory;
    }

    public String getCustomerSignatory() 
    {
        return customerSignatory;
    }
    public void setCompanySignatory(String companySignatory) 
    {
        this.companySignatory = companySignatory;
    }

    public String getCompanySignatory() 
    {
        return companySignatory;
    }
    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }
    public void setContractType(String contractType) 
    {
        this.contractType = contractType;
    }

    public String getContractType() 
    {
        return contractType;
    }
    public void setContractImage(String contractImage) 
    {
        this.contractImage = contractImage;
    }

    public String getContractImage() 
    {
        return contractImage;
    }
    public void setAttachments(String attachments) 
    {
        this.attachments = attachments;
    }

    public String getAttachments() 
    {
        return attachments;
    }
    public void setProfit(BigDecimal profit) 
    {
        this.profit = profit;
    }

    public BigDecimal getProfit() 
    {
        return profit;
    }
    public void setTotalProductCost(BigDecimal totalProductCost) 
    {
        this.totalProductCost = totalProductCost;
    }

    public BigDecimal getTotalProductCost() 
    {
        return totalProductCost;
    }
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("managerId", getManagerId())
            .append("contractNumber", getContractNumber())
            .append("contractName", getContractName())
            .append("customerName", getCustomerName())
            .append("quotationNumber", getQuotationNumber())
            .append("opportunityName", getOpportunityName())
            .append("contractAmount", getContractAmount())
            .append("orderDate", getOrderDate())
            .append("startDate", getStartDate())
            .append("endDate", getEndDate())
            .append("customerSignatory", getCustomerSignatory())
            .append("companySignatory", getCompanySignatory())
            .append("remarks", getRemarks())
            .append("contractType", getContractType())
            .append("contractImage", getContractImage())
            .append("attachments", getAttachments())
            .append("profit", getProfit())
            .append("totalProductCost", getTotalProductCost())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}
