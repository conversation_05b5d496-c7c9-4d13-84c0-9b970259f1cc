package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmBusinessApApprovalProcess;

/**
 * 审批处理Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface ICrmBusinessApApprovalProcessService 
{
    /**
     * 查询审批处理
     * 
     * @param approvalId 审批处理主键
     * @return 审批处理
     */
    public CrmBusinessApApprovalProcess selectCrmBusinessApApprovalProcessByApprovalId(Long approvalId);

    /**
     * 查询审批处理列表
     * 
     * @param crmBusinessApApprovalProcess 审批处理
     * @return 审批处理集合
     */
    public List<CrmBusinessApApprovalProcess> selectCrmBusinessApApprovalProcessList(CrmBusinessApApprovalProcess crmBusinessApApprovalProcess);

    /**
     * 新增审批处理
     * 
     * @param crmBusinessApApprovalProcess 审批处理
     * @return 结果
     */
    public int insertCrmBusinessApApprovalProcess(CrmBusinessApApprovalProcess crmBusinessApApprovalProcess);

    /**
     * 修改审批处理
     * 
     * @param crmBusinessApApprovalProcess 审批处理
     * @return 结果
     */
    public int updateCrmBusinessApApprovalProcess(CrmBusinessApApprovalProcess crmBusinessApApprovalProcess);

    /**
     * 批量删除审批处理
     * 
     * @param approvalIds 需要删除的审批处理主键集合
     * @return 结果
     */
    public int deleteCrmBusinessApApprovalProcessByApprovalIds(Long[] approvalIds);

    /**
     * 删除审批处理信息
     * 
     * @param approvalId 审批处理主键
     * @return 结果
     */
    public int deleteCrmBusinessApApprovalProcessByApprovalId(Long approvalId);
}
