<template>
  <div class="employee-analysis">
    <div class="coming-soon">
      <el-icon class="icon"><User /></el-icon>
      <h3>员工客户分析</h3>
      <p>此功能正在开发中，敬请期待...</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { User } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.employee-analysis {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  
  .coming-soon {
    text-align: center;
    color: #86909c;
    
    .icon {
      font-size: 64px;
      margin-bottom: 20px;
      opacity: 0.5;
    }
    
    h3 {
      font-size: 20px;
      margin-bottom: 12px;
      color: #1f2329;
    }
    
    p {
      font-size: 14px;
    }
  }
}
</style> 