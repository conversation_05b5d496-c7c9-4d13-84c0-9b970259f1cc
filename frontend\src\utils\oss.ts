interface OSSPolicy {
  accessId: string
  policy: string
  signature: string
  dir: string
  host: string
  expire: number
}

/**
 * 生成OSS文件路径
 */
function getOSSFilePath(filename: string, dir: string): string {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const timestamp = Date.now()
  const extension = filename.split('.').pop()
  
  return `${dir}/${year}${month}${day}/${timestamp}.${extension}`
}

/**
 * 上传文件到OSS
 * @param file 文件对象
 * @param policy OSS策略
 * @returns 返回文件访问URL
 */
export async function uploadToOSS(file: File, policy: OSSPolicy): Promise<string> {
  const formData = new FormData()
  const filePath = getOSSFilePath(file.name, policy.dir)
  
  // 组装OSS需要的参数
  formData.append('key', filePath)
  formData.append('policy', policy.policy)
  formData.append('OSSAccessKeyId', policy.accessId)
  formData.append('signature', policy.signature)
  formData.append('success_action_status', '200')
  formData.append('file', file)

  // 上传文件
  const response = await fetch(policy.host, {
    method: 'POST',
    body: formData
  })

  if (!response.ok) {
    throw new Error(' uploadToOSS oss.ts 上传失败')
  }

  // 返回文件访问路径
  return `${policy.host}/${filePath}`
}
