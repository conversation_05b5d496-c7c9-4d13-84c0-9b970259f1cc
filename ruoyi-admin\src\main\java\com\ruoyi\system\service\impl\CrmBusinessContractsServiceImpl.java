package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CrmBusinessContractsMapper;
import com.ruoyi.system.domain.CrmBusinessContracts;
import com.ruoyi.system.service.ICrmBusinessContractsService;

/**
 * 合同Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class CrmBusinessContractsServiceImpl implements ICrmBusinessContractsService 
{
    @Autowired
    private CrmBusinessContractsMapper crmBusinessContractsMapper;

    /**
     * 查询合同
     * 
     * @param id 合同主键
     * @return 合同
     */
    @Override
    public CrmBusinessContracts selectCrmBusinessContractsById(Long id)
    {
        return crmBusinessContractsMapper.selectCrmBusinessContractsById(id);
    }

    /**
     * 查询合同列表
     * 
     * @param crmBusinessContracts 合同
     * @return 合同
     */
    @Override
    public List<CrmBusinessContracts> selectCrmBusinessContractsList(CrmBusinessContracts crmBusinessContracts)
    {
        return crmBusinessContractsMapper.selectCrmBusinessContractsList(crmBusinessContracts);
    }

    /**
     * 新增合同
     * 
     * @param crmBusinessContracts 合同
     * @return 结果
     */
    @Override
    public int insertCrmBusinessContracts(CrmBusinessContracts crmBusinessContracts)
    {
        return crmBusinessContractsMapper.insertCrmBusinessContracts(crmBusinessContracts);
    }

    /**
     * 修改合同
     * 
     * @param crmBusinessContracts 合同
     * @return 结果
     */
    @Override
    public int updateCrmBusinessContracts(CrmBusinessContracts crmBusinessContracts)
    {
        return crmBusinessContractsMapper.updateCrmBusinessContracts(crmBusinessContracts);
    }

    /**
     * 批量删除合同
     * 
     * @param ids 需要删除的合同主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessContractsByIds(Long[] ids)
    {
        return crmBusinessContractsMapper.deleteCrmBusinessContractsByIds(ids);
    }

    /**
     * 删除合同信息
     * 
     * @param id 合同主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessContractsById(Long id)
    {
        return crmBusinessContractsMapper.deleteCrmBusinessContractsById(id);
    }
}
