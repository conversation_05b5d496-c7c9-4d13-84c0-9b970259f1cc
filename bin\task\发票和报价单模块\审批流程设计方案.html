<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报价单审批流程设计方案</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #9b59b6;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #9b59b6;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #8e44ad;
            margin-top: 25px;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #9b59b6;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        .xml-block {
            background-color: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 15px 0;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin: 8px 0;
        }
        .flow-step {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .flow-step h3 {
            color: white;
            margin-top: 0;
        }
        .mermaid-container {
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 报价单和发票审批流程设计方案</h1>

        <div class="highlight">
            <strong>📋 设计目标：</strong>基于现有Activiti工作流引擎，为报价单和发票模块设计完整的审批流程，支持多级审批、条件分支和状态同步。
        </div>

        <h2>🎯 一、流程需求分析</h2>
        
        <h3>1.1 业务需求</h3>
        <div class="success">
            <ul>
                <li><strong>多级审批：</strong>支持部门经理 → 总经理的多级审批</li>
                <li><strong>条件分支：</strong>根据报价金额决定审批层级</li>
                <li><strong>状态同步：</strong>流程状态与业务状态实时同步</li>
                <li><strong>权限控制：</strong>不同角色具有不同的操作权限</li>
                <li><strong>历史记录：</strong>完整的审批历史和意见记录</li>
            </ul>
        </div>

        <h3>1.2 审批规则</h3>
        <table>
            <tr>
                <th>报价金额范围</th>
                <th>审批流程</th>
                <th>审批人</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>≤ 10万元</td>
                <td>一级审批</td>
                <td>部门经理</td>
                <td>部门经理直接审批</td>
            </tr>
            <tr>
                <td>10万 < 金额 ≤ 50万</td>
                <td>二级审批</td>
                <td>部门经理 → 总经理</td>
                <td>需要总经理最终审批</td>
            </tr>
            <tr>
                <td>> 50万元</td>
                <td>三级审批</td>
                <td>部门经理 → 总经理 → 董事长</td>
                <td>重大项目需董事长审批</td>
            </tr>
        </table>

        <h2>🔄 二、流程设计</h2>
        
        <h3>2.1 流程图</h3>
        <div class="mermaid-container">
            <strong>报价单审批流程图</strong>
            <div class="code">
开始 → 提交审批 → 部门经理审批 → 金额判断网关
                                        ↓
                    ≤10万 → 审批完成 → 状态同步 → 结束
                                        ↓
                    10万-50万 → 总经理审批 → 状态同步 → 结束
                                        ↓
                    >50万 → 总经理审批 → 董事长审批 → 状态同步 → 结束
            </div>
        </div>

        <h3>2.2 流程节点详细设计</h3>
        
        <div class="flow-step">
            <h3>节点1：开始事件 (startEvent)</h3>
            <ul>
                <li><strong>触发条件：</strong>业务员提交报价单审批</li>
                <li><strong>流程变量：</strong>quotationId, totalAmount, applicant</li>
                <li><strong>自动执行：</strong>更新报价单状态为"submitted"</li>
            </ul>
        </div>

        <div class="flow-step">
            <h3>节点2：部门经理审批 (deptManagerApproval)</h3>
            <ul>
                <li><strong>任务类型：</strong>用户任务 (UserTask)</li>
                <li><strong>分配规则：</strong>根据申请人所属部门自动分配</li>
                <li><strong>审批选项：</strong>同意、驳回、需要修改</li>
                <li><strong>超时设置：</strong>3个工作日自动提醒</li>
            </ul>
        </div>

        <div class="flow-step">
            <h3>节点3：金额判断网关 (amountGateway)</h3>
            <ul>
                <li><strong>网关类型：</strong>排他网关 (ExclusiveGateway)</li>
                <li><strong>判断条件：</strong>基于totalAmount变量</li>
                <li><strong>分支路径：</strong>小额直接完成、中额总经理审批、大额多级审批</li>
            </ul>
        </div>

        <div class="flow-step">
            <h3>节点4：总经理审批 (ceoApproval)</h3>
            <ul>
                <li><strong>任务类型：</strong>用户任务 (UserTask)</li>
                <li><strong>分配规则：</strong>固定分配给总经理角色</li>
                <li><strong>审批选项：</strong>同意、驳回、退回部门经理</li>
                <li><strong>超时设置：</strong>5个工作日自动提醒</li>
            </ul>
        </div>

        <h2>📄 三、BPMN流程定义</h2>
        
        <h3>3.1 流程配置文件</h3>
        <div class="xml-block">
&lt;?xml version="1.0" encoding="UTF-8"?&gt;
&lt;definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:activiti="http://activiti.org/bpmn"
             targetNamespace="http://www.ruoyi.com/quotation"&gt;

  &lt;process id="quotation-approval" name="报价单审批流程" isExecutable="true"&gt;
    
    &lt;!-- 开始事件 --&gt;
    &lt;startEvent id="startEvent" name="提交审批"&gt;
      &lt;extensionElements&gt;
        &lt;activiti:executionListener event="start" 
                                   delegateExpression="${quotationStatusListener}"&gt;
        &lt;/activiti:executionListener&gt;
      &lt;/extensionElements&gt;
    &lt;/startEvent&gt;
    
    &lt;!-- 部门经理审批 --&gt;
    &lt;userTask id="deptManagerApproval" name="部门经理审批"
              activiti:candidateGroups="dept_manager"&gt;
      &lt;documentation&gt;部门经理审核报价单内容和价格&lt;/documentation&gt;
      &lt;extensionElements&gt;
        &lt;activiti:formProperty id="approvalResult" name="审批结果" 
                               type="enum" required="true"&gt;
          &lt;activiti:value id="approved" name="同意"/&gt;
          &lt;activiti:value id="rejected" name="驳回"/&gt;
          &lt;activiti:value id="needModify" name="需要修改"/&gt;
        &lt;/activiti:formProperty&gt;
        &lt;activiti:formProperty id="approvalComments" name="审批意见" 
                               type="string"/&gt;
      &lt;/extensionElements&gt;
    &lt;/userTask&gt;
    
    &lt;!-- 金额判断网关 --&gt;
    &lt;exclusiveGateway id="amountGateway" name="金额判断"/&gt;
    
    &lt;!-- 总经理审批 --&gt;
    &lt;userTask id="ceoApproval" name="总经理审批"
              activiti:candidateGroups="ceo"&gt;
      &lt;documentation&gt;总经理审批大额报价单&lt;/documentation&gt;
    &lt;/userTask&gt;
    
    &lt;!-- 状态同步服务任务 --&gt;
    &lt;serviceTask id="syncStatus" name="状态同步"
                 activiti:delegateExpression="${quotationStatusListener}"/&gt;
    
    &lt;!-- 结束事件 --&gt;
    &lt;endEvent id="endEvent" name="审批完成"/&gt;
    
    &lt;!-- 流程连线 --&gt;
    &lt;sequenceFlow id="flow1" sourceRef="startEvent" targetRef="deptManagerApproval"/&gt;
    &lt;sequenceFlow id="flow2" sourceRef="deptManagerApproval" targetRef="amountGateway"/&gt;
    
    &lt;!-- 条件分支 --&gt;
    &lt;sequenceFlow id="smallAmount" sourceRef="amountGateway" targetRef="syncStatus"&gt;
      &lt;conditionExpression&gt;${totalAmount &lt;= 100000}&lt;/conditionExpression&gt;
    &lt;/sequenceFlow&gt;
    
    &lt;sequenceFlow id="largeAmount" sourceRef="amountGateway" targetRef="ceoApproval"&gt;
      &lt;conditionExpression&gt;${totalAmount &gt; 100000}&lt;/conditionExpression&gt;
    &lt;/sequenceFlow&gt;
    
    &lt;sequenceFlow id="flow5" sourceRef="ceoApproval" targetRef="syncStatus"/&gt;
    &lt;sequenceFlow id="flow6" sourceRef="syncStatus" targetRef="endEvent"/&gt;
    
  &lt;/process&gt;
&lt;/definitions&gt;
        </div>

        <h2>🔧 四、监听器和服务委托</h2>
        
        <h3>4.1 状态同步监听器</h3>
        <div class="code">
@Component("quotationStatusListener")
public class QuotationStatusListener implements JavaDelegate {
    
    @Autowired
    private ICrmQuotationService quotationService;
    
    @Override
    public void execute(DelegateExecution execution) {
        try {
            Long quotationId = Long.valueOf(execution.getProcessInstanceBusinessKey());
            String approvalResult = (String) execution.getVariable("approvalResult");
            String currentTaskName = execution.getCurrentActivityName();
            
            CrmQuotation quotation = quotationService.selectById(quotationId);
            if (quotation != null) {
                // 更新流程状态
                quotation.setProcessInstanceId(execution.getProcessInstanceId());
                quotation.setCurrentTaskName(currentTaskName);
                
                // 根据审批结果更新业务状态
                if ("approved".equals(approvalResult)) {
                    quotation.setStatus("approved");
                    quotation.setApprovalStatus("approved");
                } else if ("rejected".equals(approvalResult)) {
                    quotation.setStatus("rejected");
                    quotation.setApprovalStatus("rejected");
                }
                
                quotationService.updateQuotation(quotation);
            }
        } catch (Exception e) {
            throw new RuntimeException("状态同步失败", e);
        }
    }
}
        </div>

        <h3>4.2 任务分配监听器</h3>
        <div class="code">
@Component("quotationTaskAssignmentListener")
public class QuotationTaskAssignmentListener implements TaskListener {
    
    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefinitionKey = delegateTask.getTaskDefinitionKey();
        
        if ("deptManagerApproval".equals(taskDefinitionKey)) {
            // 根据申请人部门自动分配部门经理
            String applicant = (String) delegateTask.getVariable("applicant");
            String deptManager = getDeptManagerByUser(applicant);
            delegateTask.setAssignee(deptManager);
        } else if ("ceoApproval".equals(taskDefinitionKey)) {
            // 分配给总经理
            delegateTask.setAssignee("ceo");
        }
    }
    
    private String getDeptManagerByUser(String userId) {
        // 实现根据用户ID获取部门经理的逻辑
        return "dept_manager_001";
    }
}
        </div>

        <h2>⚙️ 五、配置和部署</h2>
        
        <h3>5.1 流程部署配置</h3>
        <div class="info">
            <strong>部署步骤：</strong>
            <ol>
                <li>将BPMN文件放置到 <code>src/main/resources/processes/</code> 目录</li>
                <li>配置监听器和服务委托类</li>
                <li>更新application.yml中的Activiti配置</li>
                <li>重启应用自动部署流程</li>
            </ol>
        </div>

        <h3>5.2 权限配置</h3>
        <table>
            <tr>
                <th>角色</th>
                <th>权限</th>
                <th>操作范围</th>
            </tr>
            <tr>
                <td>业务员</td>
                <td>提交、查看、撤回</td>
                <td>自己创建的报价单</td>
            </tr>
            <tr>
                <td>部门经理</td>
                <td>审批、驳回、查看</td>
                <td>本部门的报价单</td>
            </tr>
            <tr>
                <td>总经理</td>
                <td>审批、驳回、查看</td>
                <td>所有报价单</td>
            </tr>
            <tr>
                <td>系统管理员</td>
                <td>查看、管理、监控</td>
                <td>所有流程实例</td>
            </tr>
        </table>

        <h3>5.3 API接口设计</h3>
        <div class="code">
// 启动审批流程
POST /api/quotations/{id}/submit
{
    "comments": "请审批此报价单"
}

// 处理审批任务
POST /api/quotations/tasks/{taskId}/complete
{
    "approvalResult": "approved",
    "approvalComments": "同意此报价"
}

// 查询待办任务
GET /api/quotations/tasks/pending?userId={userId}

// 查询流程历史
GET /api/quotations/{id}/process-history
        </div>

        <div class="success">
            <strong>✅ 设计优势：</strong>
            <ul>
                <li>基于成熟的Activiti引擎，稳定可靠</li>
                <li>支持复杂的业务规则和条件分支</li>
                <li>完整的状态同步机制</li>
                <li>灵活的任务分配策略</li>
                <li>详细的审批历史记录</li>
            </ul>
        </div>

        <h2>📄 六、发票审批流程设计</h2>

        <h3>6.1 发票审批需求</h3>
        <div class="success">
            <ul>
                <li><strong>审批触发：</strong>发票创建后需要审批才能开票</li>
                <li><strong>合同关联：</strong>发票必须关联合同编号</li>
                <li><strong>附件管理：</strong>支持上传发票图片和相关附件</li>
                <li><strong>审批层级：</strong>财务经理 → 总经理（根据金额）</li>
                <li><strong>状态同步：</strong>审批状态与开票状态分离管理</li>
            </ul>
        </div>

        <h3>6.2 发票审批流程图</h3>
        <div class="mermaid-container">
            <strong>发票审批流程图</strong>
            <div class="code">
开始 → 提交审批 → 财务经理审批 → 金额判断网关
                                        ↓
                    ≤20万 → 审批完成 → 状态同步 → 结束
                                        ↓
                    >20万 → 总经理审批 → 状态同步 → 结束
            </div>
        </div>

        <h3>6.3 发票审批规则</h3>
        <table>
            <tr>
                <th>发票金额范围</th>
                <th>审批流程</th>
                <th>审批人</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>≤ 20万元</td>
                <td>一级审批</td>
                <td>财务经理</td>
                <td>财务经理直接审批</td>
            </tr>
            <tr>
                <td>> 20万元</td>
                <td>二级审批</td>
                <td>财务经理 → 总经理</td>
                <td>需要总经理最终审批</td>
            </tr>
        </table>

        <h3>6.4 发票BPMN流程定义</h3>
        <div class="xml-block">
&lt;?xml version="1.0" encoding="UTF-8"?&gt;
&lt;definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:activiti="http://activiti.org/bpmn"
             targetNamespace="http://www.ruoyi.com/invoice"&gt;

  &lt;process id="invoice-approval" name="发票审批流程" isExecutable="true"&gt;

    &lt;!-- 开始事件 --&gt;
    &lt;startEvent id="startEvent" name="提交审批"&gt;
      &lt;extensionElements&gt;
        &lt;activiti:executionListener event="start"
                                   delegateExpression="${invoiceStatusListener}"&gt;
        &lt;/activiti:executionListener&gt;
      &lt;/extensionElements&gt;
    &lt;/startEvent&gt;

    &lt;!-- 财务经理审批 --&gt;
    &lt;userTask id="financeManagerApproval" name="财务经理审批"
              activiti:candidateGroups="finance_manager"&gt;
      &lt;documentation&gt;财务经理审核发票信息和金额&lt;/documentation&gt;
      &lt;extensionElements&gt;
        &lt;activiti:formProperty id="approvalResult" name="审批结果"
                               type="enum" required="true"&gt;
          &lt;activiti:value id="approved" name="同意"/&gt;
          &lt;activiti:value id="rejected" name="驳回"/&gt;
          &lt;activiti:value id="needModify" name="需要修改"/&gt;
        &lt;/activiti:formProperty&gt;
        &lt;activiti:formProperty id="approvalComments" name="审批意见"
                               type="string"/&gt;
      &lt;/extensionElements&gt;
    &lt;/userTask&gt;

    &lt;!-- 金额判断网关 --&gt;
    &lt;exclusiveGateway id="amountGateway" name="金额判断"/&gt;

    &lt;!-- 总经理审批 --&gt;
    &lt;userTask id="ceoApproval" name="总经理审批"
              activiti:candidateGroups="ceo"&gt;
      &lt;documentation&gt;总经理审批大额发票&lt;/documentation&gt;
    &lt;/userTask&gt;

    &lt;!-- 状态同步服务任务 --&gt;
    &lt;serviceTask id="syncStatus" name="状态同步"
                 activiti:delegateExpression="${invoiceStatusListener}"/&gt;

    &lt;!-- 结束事件 --&gt;
    &lt;endEvent id="endEvent" name="审批完成"/&gt;

    &lt;!-- 流程连线 --&gt;
    &lt;sequenceFlow id="flow1" sourceRef="startEvent" targetRef="financeManagerApproval"/&gt;
    &lt;sequenceFlow id="flow2" sourceRef="financeManagerApproval" targetRef="amountGateway"/&gt;

    &lt;!-- 条件分支 --&gt;
    &lt;sequenceFlow id="smallAmount" sourceRef="amountGateway" targetRef="syncStatus"&gt;
      &lt;conditionExpression&gt;${totalAmount &lt;= 200000}&lt;/conditionExpression&gt;
    &lt;/sequenceFlow&gt;

    &lt;sequenceFlow id="largeAmount" sourceRef="amountGateway" targetRef="ceoApproval"&gt;
      &lt;conditionExpression&gt;${totalAmount &gt; 200000}&lt;/conditionExpression&gt;
    &lt;/sequenceFlow&gt;

    &lt;sequenceFlow id="flow5" sourceRef="ceoApproval" targetRef="syncStatus"/&gt;
    &lt;sequenceFlow id="flow6" sourceRef="syncStatus" targetRef="endEvent"/&gt;

  &lt;/process&gt;
&lt;/definitions&gt;
        </div>

        <h3>6.5 发票状态同步监听器</h3>
        <div class="code">
@Component("invoiceStatusListener")
public class InvoiceStatusListener implements JavaDelegate {

    @Autowired
    private ICrmInvoiceService invoiceService;

    @Override
    public void execute(DelegateExecution execution) {
        try {
            Long invoiceId = Long.valueOf(execution.getProcessInstanceBusinessKey());
            String approvalResult = (String) execution.getVariable("approvalResult");
            String currentTaskName = execution.getCurrentActivityName();

            CrmInvoice invoice = invoiceService.selectById(invoiceId);
            if (invoice != null) {
                // 更新流程状态
                invoice.setProcessInstanceId(execution.getProcessInstanceId());
                invoice.setCurrentTaskName(currentTaskName);

                // 根据审批结果更新业务状态
                if ("approved".equals(approvalResult)) {
                    invoice.setStatus("approved");
                    invoice.setApprovalStatus("approved");
                } else if ("rejected".equals(approvalResult)) {
                    invoice.setStatus("rejected");
                    invoice.setApprovalStatus("rejected");
                }

                invoiceService.updateInvoice(invoice);
            }
        } catch (Exception e) {
            throw new RuntimeException("发票状态同步失败", e);
        }
    }
}
        </div>

        <h2>📎 七、发票附件管理设计</h2>

        <h3>7.1 附件类型</h3>
        <table>
            <tr>
                <th>附件类型</th>
                <th>说明</th>
                <th>必填</th>
                <th>格式要求</th>
            </tr>
            <tr>
                <td>发票图片</td>
                <td>发票的扫描件或照片</td>
                <td>是</td>
                <td>JPG, PNG, PDF</td>
            </tr>
            <tr>
                <td>合同扫描件</td>
                <td>相关合同的扫描件</td>
                <td>否</td>
                <td>PDF, JPG, PNG</td>
            </tr>
            <tr>
                <td>其他附件</td>
                <td>其他相关文件</td>
                <td>否</td>
                <td>PDF, DOC, XLS等</td>
            </tr>
        </table>

        <h3>7.2 附件管理API</h3>
        <div class="code">
// 上传发票附件
POST /api/invoices/{id}/attachments
Content-Type: multipart/form-data
{
    "file": [文件],
    "attachmentType": "invoice_image",
    "description": "发票正本扫描件"
}

// 获取发票附件列表
GET /api/invoices/{id}/attachments

// 删除附件
DELETE /api/invoices/attachments/{attachmentId}

// 下载附件
GET /api/invoices/attachments/{attachmentId}/download
        </div>

        <div class="success">
            <strong>✅ 完整设计优势：</strong>
            <ul>
                <li>报价单和发票都支持完整的审批流程</li>
                <li>发票支持合同关联，业务流程更完整</li>
                <li>完善的附件管理，支持多种文件类型</li>
                <li>灵活的审批规则，可根据金额自动分配</li>
                <li>状态管理清晰，审批状态与业务状态分离</li>
            </ul>
        </div>

        <div class="warning">
            <strong>⚠️ 注意事项：</strong>
            <ul>
                <li>确保流程定义Key与代码中的常量一致</li>
                <li>监听器类必须注册为Spring Bean</li>
                <li>流程变量的数据类型要与业务对象匹配</li>
                <li>考虑异常情况的处理和回滚机制</li>
                <li>附件上传需要配置文件存储路径和大小限制</li>
                <li>合同关联需要验证合同的有效性</li>
            </ul>
        </div>
    </div>
</body>
</html>
