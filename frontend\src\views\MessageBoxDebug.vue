<template>
  <div class="debug-container">
    <h2>MessageBox & Message 测试</h2>
    
    <div class="test-buttons">
      <el-button 
        type="primary" 
        @click="testMessageBox" 
        size="large" 
        :loading="isLoading"
      >
        {{ isLoading ? '测试中...' : '测试 MessageBox' }}
      </el-button>
      
      <el-button 
        type="success" 
        @click="testMessage" 
        size="large"
      >
        测试 Message
      </el-button>
      
      <el-button 
        type="warning" 
        @click="testConfirm" 
        size="large"
      >
        测试 Confirm
      </el-button>

      <el-button 
        type="info" 
        @click="testSimpleAlert" 
        size="large"
      >
        测试简单弹窗
      </el-button>

      <el-button 
        type="danger" 
        @click="checkDOMElements" 
        size="large"
      >
        检查DOM元素
      </el-button>

      <el-button 
        type="primary" 
        @click="testDirectCall" 
        size="large"
      >
        直接调用测试
      </el-button>

      <el-button 
        type="success" 
        @click="checkZIndex" 
        size="large"
      >
        检查Z-Index
      </el-button>
    </div>
    
    <div class="status-info">
      <p class="status">状态: {{ testStatus }}</p>
      <div v-if="errorInfo" class="error-info">
        <p>错误信息: {{ errorInfo }}</p>
      </div>
    </div>

    <div class="debug-info">
      <h3>调试信息</h3>
      <p>Element Plus 版本: {{ elementPlusVersion }}</p>
      <p>Vue 版本: {{ vueVersion }}</p>
      <p>浏览器: {{ userAgent }}</p>
      <div v-if="domInfo" class="dom-info">
        <h4>DOM检查结果:</h4>
        <pre>{{ domInfo }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus';
import { nextTick, onMounted, ref } from 'vue';

const isLoading = ref(false);
const testStatus = ref('等待测试');
const errorInfo = ref('');
const elementPlusVersion = ref('');
const vueVersion = ref('');
const userAgent = ref('');
const domInfo = ref('');

onMounted(() => {
  // 获取版本信息
  try {
    elementPlusVersion.value = '2.10.0'; // 从package.json获取
    vueVersion.value = '3.4.21'; // 从package.json获取
    userAgent.value = navigator.userAgent;
    
    // 检查Element Plus是否正确加载
    console.log('ElMessage:', ElMessage);
    console.log('ElMessageBox:', ElMessageBox);
    
    // 检查DOM中是否有Element Plus的样式
    const elementStyles = document.querySelector('link[href*="element-plus"]') || 
                         document.querySelector('style[data-vite-dev-id*="element-plus"]');
    console.log('Element Plus 样式已加载:', !!elementStyles);
    
  } catch (error: any) {
    console.error('初始化错误:', error);
    errorInfo.value = `初始化错误: ${error}`;
  }
});

// 直接调用测试
const testDirectCall = async () => {
  console.log('=== 直接调用测试 ===');
  
  try {
    // 直接调用，不使用await
    ElMessageBox.alert('直接调用测试', '提示', {
      confirmButtonText: '确定',
      type: 'info',
      callback: (action: string) => {
        console.log('直接调用回调:', action);
        testStatus.value = '直接调用测试完成';
      }
    });
    
    console.log('直接调用已执行');
    testStatus.value = '直接调用已执行，等待用户操作';
    
  } catch (error: any) {
    console.error('直接调用失败:', error);
    errorInfo.value = `直接调用错误: ${error.message || error}`;
    testStatus.value = '直接调用失败';
  }
};

// 检查DOM元素
const checkDOMElements = () => {
  console.log('=== 检查DOM元素 ===');
  
  try {
    const info: string[] = [];
    
    // 检查body
    info.push(`Body overflow: ${getComputedStyle(document.body).overflow}`);
    info.push(`Body position: ${getComputedStyle(document.body).position}`);
    info.push(`Body z-index: ${getComputedStyle(document.body).zIndex}`);
    
    // 检查html
    info.push(`HTML overflow: ${getComputedStyle(document.documentElement).overflow}`);
    
    // 检查app容器
    const app = document.getElementById('app');
    if (app) {
      info.push(`App overflow: ${getComputedStyle(app).overflow}`);
      info.push(`App position: ${getComputedStyle(app).position}`);
      info.push(`App z-index: ${getComputedStyle(app).zIndex}`);
    }
    
    // 检查main-container
    const mainContainer = document.querySelector('.main-container');
    if (mainContainer) {
      const styles = getComputedStyle(mainContainer);
      info.push(`Main-container overflow: ${styles.overflow}`);
      info.push(`Main-container position: ${styles.position}`);
      info.push(`Main-container z-index: ${styles.zIndex}`);
    }
    
    // 检查是否有现有的弹窗元素
    const existingModals = document.querySelectorAll('.el-overlay, .el-message-box, .el-dialog');
    info.push(`现有弹窗元素数量: ${existingModals.length}`);
    
    // 检查Element Plus相关的CSS
    const allStyles = document.querySelectorAll('style, link[rel="stylesheet"]');
    let elementPlusStyleCount = 0;
    allStyles.forEach(style => {
      if (style.textContent?.includes('el-message') || 
          (style as HTMLLinkElement).href?.includes('element-plus')) {
        elementPlusStyleCount++;
      }
    });
    info.push(`Element Plus 样式数量: ${elementPlusStyleCount}`);
    
    domInfo.value = info.join('\n');
    testStatus.value = 'DOM检查完成';
    
  } catch (error: any) {
    console.error('DOM检查失败:', error);
    errorInfo.value = `DOM检查错误: ${error.message || error}`;
    testStatus.value = 'DOM检查失败';
  }
};

// 测试最简单的弹窗
const testSimpleAlert = () => {
  console.log('=== 测试最简单的弹窗 ===');
  
  try {
    // 使用最基本的alert
    alert('这是浏览器原生弹窗，如果这个能显示，说明不是浏览器问题');
    
    // 然后测试Element Plus，使用最简单的调用方式
    ElMessageBox.alert('最简单的Element Plus弹窗');
    testStatus.value = '简单弹窗测试完成';
  } catch (error: any) {
    console.error('简单弹窗测试失败:', error);
    errorInfo.value = `简单弹窗错误: ${error}`;
    testStatus.value = '简单弹窗测试失败';
  }
};

// 测试 MessageBox
const testMessageBox = async () => {
  console.log('=== 测试 MessageBox ===');
  
  isLoading.value = true;
  testStatus.value = '测试中...';
  errorInfo.value = '';
  
  try {
    console.log('准备调用 ElMessageBox.alert');
    
    // 检查ElMessageBox是否存在
    if (!ElMessageBox) {
      throw new Error('ElMessageBox 未定义');
    }
    
    if (typeof ElMessageBox.alert !== 'function') {
      throw new Error('ElMessageBox.alert 不是函数');
    }
    
    console.log('ElMessageBox.alert 类型:', typeof ElMessageBox.alert);
    
    // 等待下一个tick确保DOM已更新
    await nextTick();
    
    // 使用await等待用户操作
    await ElMessageBox.alert('这是一个测试消息', '测试标题', {
      confirmButtonText: '确定',
      type: 'info',
      appendTo: 'body', // 确保挂载到body
      callback: (action: string) => {
        console.log('MessageBox callback:', action);
      }
    });

    console.log('MessageBox 调用成功');
    testStatus.value = 'MessageBox 测试成功';
    ElMessage.success('MessageBox 测试成功！');
  } catch (error: any) {
    console.error('MessageBox 错误:', error);
    errorInfo.value = `MessageBox错误: ${error.message || error}`;
    testStatus.value = 'MessageBox 测试失败';
    
    // 尝试使用Message显示错误
    try {
      ElMessage.error(`MessageBox 测试失败: ${error.message || error}`);
    } catch (msgError: any) {
      console.error('连Message都失败了:', msgError);
      errorInfo.value += ` | Message也失败: ${msgError}`;
    }
  } finally {
    isLoading.value = false;
  }
};

// 测试 Message
const testMessage = () => {
  console.log('=== 测试 Message ===');
  errorInfo.value = '';
  
  try {
    // 检查ElMessage是否存在
    if (!ElMessage) {
      throw new Error('ElMessage 未定义');
    }
    
    if (typeof ElMessage.success !== 'function') {
      throw new Error('ElMessage.success 不是函数');
    }
    
    console.log('ElMessage.success 类型:', typeof ElMessage.success);
    
    ElMessage.success('Message 组件测试成功！');
    testStatus.value = 'Message 测试成功';
  } catch (error: any) {
    console.error('Message 测试失败:', error);
    errorInfo.value = `Message错误: ${error.message || error}`;
    testStatus.value = 'Message 测试失败';
  }
};

// 测试 Confirm
const testConfirm = async () => {
  console.log('=== 测试 Confirm ===');
  errorInfo.value = '';
  
  try {
    // 检查ElMessageBox是否存在
    if (!ElMessageBox || typeof ElMessageBox.confirm !== 'function') {
      throw new Error('ElMessageBox.confirm 不可用');
    }
    
    const result = await ElMessageBox.confirm('确认要执行这个操作吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      appendTo: 'body' // 确保挂载到body
    });
    
    console.log('Confirm result:', result);
    testStatus.value = 'Confirm 测试 - 用户确认';
    ElMessage.success('用户确认了操作');
  } catch (error: any) {
    console.log('Confirm error:', error);
    if (error === 'cancel') {
      testStatus.value = 'Confirm 测试 - 用户取消';
      ElMessage.info('用户取消了操作');
    } else {
      console.error('Confirm 测试失败:', error);
      errorInfo.value = `Confirm错误: ${error.message || error}`;
      testStatus.value = 'Confirm 测试失败';
      ElMessage.error('Confirm 测试失败');
    }
  }
};

// 检查Z-Index
const checkZIndex = () => {
  console.log('=== 检查Z-Index ===');
  
  try {
    const info: string[] = [];
    
    // 检查各种元素的z-index
    const bodyZIndex = getComputedStyle(document.body).zIndex;
    const htmlZIndex = getComputedStyle(document.documentElement).zIndex;
    
    info.push(`Body z-index: ${bodyZIndex}`);
    info.push(`HTML z-index: ${htmlZIndex}`);
    
    // 检查app容器
    const app = document.getElementById('app');
    if (app) {
      const appZIndex = getComputedStyle(app).zIndex;
      info.push(`App z-index: ${appZIndex}`);
    }
    
    // 检查main-container
    const mainContainer = document.querySelector('.main-container');
    if (mainContainer) {
      const containerZIndex = getComputedStyle(mainContainer).zIndex;
      info.push(`Main-container z-index: ${containerZIndex}`);
    }
    
    // 查找页面中最高的z-index
    const allElements = document.querySelectorAll('*');
    let maxZIndex = 0;
    let elementWithMaxZIndex: Element | null = null;
    
    allElements.forEach(el => {
      const zIndex = parseInt(getComputedStyle(el).zIndex);
      if (!isNaN(zIndex) && zIndex > maxZIndex) {
        maxZIndex = zIndex;
        elementWithMaxZIndex = el;
      }
    });
    
    info.push(`页面最高z-index: ${maxZIndex}`);
    if (elementWithMaxZIndex) {
      info.push(`最高z-index元素: ${elementWithMaxZIndex.tagName}.${elementWithMaxZIndex.className}`);
    }
    
    // Element Plus弹窗的期望z-index范围
    info.push(`Element Plus默认z-index基础值: 2000`);
    info.push(`当前设置的z-index基础值: 3000`);
    
    domInfo.value = info.join('\n');
    testStatus.value = 'Z-Index检查完成';
    
  } catch (error: any) {
    console.error('Z-Index检查失败:', error);
    errorInfo.value = `Z-Index检查错误: ${error.message || error}`;
    testStatus.value = 'Z-Index检查失败';
  }
};
</script>

<style scoped>
.debug-container {
  padding: 50px;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

h2 {
  color: #333;
  margin-bottom: 40px;
  font-size: 24px;
}

.test-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.el-button {
  padding: 15px 30px;
  font-size: 16px;
}

.status-info {
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  margin-bottom: 20px;
}

.status {
  margin: 0;
  font-size: 16px;
  color: #606266;
  font-weight: 500;
}

.error-info {
  margin-top: 10px;
  padding: 10px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
}

.debug-info {
  text-align: left;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.debug-info h3 {
  margin-top: 0;
  color: #333;
}

.debug-info p {
  margin: 5px 0;
  font-family: monospace;
  font-size: 14px;
}

.dom-info {
  margin-top: 15px;
}

.dom-info h4 {
  margin: 10px 0 5px 0;
  color: #666;
}

.dom-info pre {
  background: #f0f0f0;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>

<!-- 全局样式，确保弹窗可见 -->
<style>
/* 确保弹窗有足够高的z-index */
.el-overlay {
  z-index: 9999 !important;
}

.el-message-box {
  z-index: 10000 !important;
}

.el-message {
  z-index: 10001 !important;
}

/* 确保body可以显示弹窗 */
body {
  overflow: visible !important;
}

/* 确保弹窗容器可见 */
.el-message-box__wrapper {
  z-index: 10000 !important;
}
</style> 