package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CrmBusinessPaymentDetailsMapper;
import com.ruoyi.system.domain.CrmBusinessPaymentDetails;
import com.ruoyi.system.service.ICrmBusinessPaymentDetailsService;

/**
 * 回款明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Service
public class CrmBusinessPaymentDetailsServiceImpl implements ICrmBusinessPaymentDetailsService 
{
    @Autowired
    private CrmBusinessPaymentDetailsMapper crmBusinessPaymentDetailsMapper;

    /**
     * 查询回款明细
     * 
     * @param id 回款明细主键
     * @return 回款明细
     */
    @Override
    public CrmBusinessPaymentDetails selectCrmBusinessPaymentDetailsById(Long id)
    {
        return crmBusinessPaymentDetailsMapper.selectCrmBusinessPaymentDetailsById(id);
    }

    /**
     * 查询回款明细列表
     * 
     * @param crmBusinessPaymentDetails 回款明细
     * @return 回款明细
     */
    @Override
    public List<CrmBusinessPaymentDetails> selectCrmBusinessPaymentDetailsList(CrmBusinessPaymentDetails crmBusinessPaymentDetails)
    {
        return crmBusinessPaymentDetailsMapper.selectCrmBusinessPaymentDetailsList(crmBusinessPaymentDetails);
    }

    /**
     * 新增回款明细
     * 
     * @param crmBusinessPaymentDetails 回款明细
     * @return 结果
     */
    @Override
    public int insertCrmBusinessPaymentDetails(CrmBusinessPaymentDetails crmBusinessPaymentDetails)
    {
        return crmBusinessPaymentDetailsMapper.insertCrmBusinessPaymentDetails(crmBusinessPaymentDetails);
    }

    /**
     * 修改回款明细
     * 
     * @param crmBusinessPaymentDetails 回款明细
     * @return 结果
     */
    @Override
    public int updateCrmBusinessPaymentDetails(CrmBusinessPaymentDetails crmBusinessPaymentDetails)
    {
        return crmBusinessPaymentDetailsMapper.updateCrmBusinessPaymentDetails(crmBusinessPaymentDetails);
    }

    /**
     * 批量删除回款明细
     * 
     * @param ids 需要删除的回款明细主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessPaymentDetailsByIds(Long[] ids)
    {
        return crmBusinessPaymentDetailsMapper.deleteCrmBusinessPaymentDetailsByIds(ids);
    }

    /**
     * 删除回款明细信息
     * 
     * @param id 回款明细主键
     * @return 结果
     */
    @Override
    public int deleteCrmBusinessPaymentDetailsById(Long id)
    {
        return crmBusinessPaymentDetailsMapper.deleteCrmBusinessPaymentDetailsById(id);
    }
}
