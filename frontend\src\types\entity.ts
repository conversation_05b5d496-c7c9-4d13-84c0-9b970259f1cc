class BaseEntity {
    id: number;
    name: string;
    responsiblePersonId?: string;    // 负责人ID
    createTime?: string;
    createBy?: string;
    updateTime?: string;
    updateBy?: string;
    delFlag: string;

    constructor(
        source?: any
    ) {
        this.id = source?.id || 0;
        this.name = source?.name || '';
        this.responsiblePersonId = source?.responsiblePersonId || '';
        this.createTime = source?.createTime || '';
        this.createBy = source?.createBy || '';
        this.updateTime = source?.updateTime || '';
        this.updateBy = source?.updateBy || '';
        this.delFlag = source?.delFlag || '0';
    }
}

export class LeadEntity extends BaseEntity {

    leadName: string;
    customerName: string;
    leadSource: string;
    mobile: string;
    phone: string;
    email: string;
    address: string;
    detailedAddress: string;
    customerIndustry: string;
    customerLevel: string;
    nextContactTime: string;
    selectedDate: string;
    remarks: string;
    status: string;

    constructor(source?: any) {
        super(source);
        this.responsiblePersonId = source?.responsiblePersonId || '';
        this.leadName = source?.leadName || '';
        this.customerName = source?.customerName || '';
        this.leadSource = source?.leadSource || '';
        this.mobile = source?.mobile || '';
        this.phone = source?.phone || '';
        this.email = source?.email || '';
        this.address = source?.address || '';
        this.detailedAddress = source?.detailedAddress || '';
        this.customerIndustry = source?.customerIndustry || '';
        this.customerLevel = source?.customerLevel || '';
        this.nextContactTime = source?.nextContactTime || new Date();
        this.selectedDate = source?.selectedDate || new Date();
        this.remarks = source?.remarks || '';
        this.status = source?.status || '';
        this.delFlag = source?.delFlag || '0';
    }
}

export class LeadUserAssociationEntity extends BaseEntity {
    leadId: number;
    userId: number;
    status: string;
    currentOwnerId: number;

    constructor(source?: any) {
        super(source);
        this.leadId = source?.leadId || 0;
        this.userId = source?.userId || 0;
        this.status = source?.status || '';
        this.currentOwnerId = source?.currentOwnerId || 0;
    }
}

export class LeadFollowerEntity extends BaseEntity {
    leadId: number;
    followerId: number;
    status: string;

    constructor(source?: any) {
        super(source);
        this.leadId = source?.leadId || 0;
        this.followerId = source?.followerId || 0;
        this.status = source?.status || '';
    }
}

export class CustomerEntity extends BaseEntity {
    contactName?: string;
    phone?: string;
    email?: string;
    industry?: string;
    address?: string;
    level?: string;
    attachmentsCount?: number;

    constructor(source?: any) {
        super(source);
        this.contactName = source?.contactName || '';
        this.phone = source?.phone || '';
        this.email = source?.email || '';
    }
}

export class ContactEntity extends BaseEntity {
    customerId?: number;
    position?: string;
    phone?: string;
    email?: string;
    gender?: string;
    attachmentsCount?: number;

    constructor(source?: any) {
        super(source);
        this.customerId = source?.customerId || 0;
        this.position = source?.position || '';
        this.phone = source?.phone || '';
        this.email = source?.email || '';
        this.gender = source?.gender || '';
        this.attachmentsCount = source?.attachmentsCount || 0;
    }
}

export type EntityType = 'lead' | 'customer' | 'contact' | 'leadUserAssociation' | 'leadFollower';
export type EntityData = LeadEntity | CustomerEntity | ContactEntity | LeadUserAssociationEntity | LeadFollowerEntity; 