package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.domain.entity.CrmBusinessApProcessInstance;
import com.ruoyi.system.service.ICrmBusinessApProcessInstanceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 流程实例Controller
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@Api(tags = "流程实例管理")
@RestController
@RequestMapping("/system/instance")
public class CrmBusinessApProcessInstanceController extends BaseController {
    @Autowired
    private ICrmBusinessApProcessInstanceService crmBusinessApProcessInstanceService;

    /**
     * 查询流程实例列表
     */
    @PreAuthorize("@ss.hasPermi('system:instance:list')")
    @ApiOperation("查询流程实例列表")
    @GetMapping("/list")
    public TableDataInfo list(CrmBusinessApProcessInstance crmBusinessApProcessInstance) {
        startPage();
        List<CrmBusinessApProcessInstance> list = crmBusinessApProcessInstanceService
                .selectCrmBusinessApProcessInstanceList(crmBusinessApProcessInstance);
        return getDataTable(list);
    }

    /**
     * 导出流程实例列表
     */
    @PreAuthorize("@ss.hasPermi('system:instance:export')")
    @Log(title = "流程实例", businessType = BusinessType.EXPORT)
    @ApiOperation("导出流程实例列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmBusinessApProcessInstance crmBusinessApProcessInstance) {
        List<CrmBusinessApProcessInstance> list = crmBusinessApProcessInstanceService
                .selectCrmBusinessApProcessInstanceList(crmBusinessApProcessInstance);
        ExcelUtil<CrmBusinessApProcessInstance> util = new ExcelUtil<CrmBusinessApProcessInstance>(
                CrmBusinessApProcessInstance.class);
        util.exportExcel(response, list, "流程实例数据");
    }

    /**
     * 获取流程实例详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:instance:query')")
    @ApiOperation("获取流程实例详细信息")
    @GetMapping(value = "/{instanceId}")
    public AjaxResult getInfo(@ApiParam("流程实例ID") @PathVariable("instanceId") Long instanceId) {
        return success(crmBusinessApProcessInstanceService.selectCrmBusinessApProcessInstanceByInstanceId(instanceId));
    }

    /**
     * 新增流程实例
     */
    @PreAuthorize("@ss.hasPermi('system:instance:add')")
    @Log(title = "流程实例", businessType = BusinessType.INSERT)
    @ApiOperation("新增流程实例")
    @PostMapping
    public AjaxResult add(@RequestBody CrmBusinessApProcessInstance crmBusinessApProcessInstance) {
        return toAjax(
                crmBusinessApProcessInstanceService.insertCrmBusinessApProcessInstance(crmBusinessApProcessInstance));
    }

    /**
     * 修改流程实例
     */
    @PreAuthorize("@ss.hasPermi('system:instance:edit')")
    @Log(title = "流程实例", businessType = BusinessType.UPDATE)
    @ApiOperation("修改流程实例")
    @PutMapping
    public AjaxResult edit(@RequestBody CrmBusinessApProcessInstance crmBusinessApProcessInstance) {
        return toAjax(
                crmBusinessApProcessInstanceService.updateCrmBusinessApProcessInstance(crmBusinessApProcessInstance));
    }

    /**
     * 删除流程实例
     */
    @PreAuthorize("@ss.hasPermi('system:instance:remove')")
    @Log(title = "流程实例", businessType = BusinessType.DELETE)
    @ApiOperation("删除流程实例")
    @DeleteMapping("/{instanceIds}")
    public AjaxResult remove(@ApiParam("流程实例ID列表") @PathVariable Long[] instanceIds) {
        return toAjax(crmBusinessApProcessInstanceService.deleteCrmBusinessApProcessInstanceByInstanceIds(instanceIds));
    }
}
