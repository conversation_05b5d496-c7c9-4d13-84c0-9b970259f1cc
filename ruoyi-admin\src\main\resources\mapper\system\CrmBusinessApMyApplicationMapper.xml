<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CrmBusinessApMyApplicationMapper">
    
    <resultMap type="CrmBusinessApMyApplication" id="CrmBusinessApMyApplicationResult">
        <result property="applicationId"    column="application_id"    />
        <result property="contentId"    column="content_id"    />
        <result property="instanceId"    column="instance_id"    />
        <result property="currentNodeId"    column="current_node_id"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectCrmBusinessApMyApplicationVo">
        select application_id, content_id, instance_id, current_node_id, submit_time, status from crm_business_ap_my_application
    </sql>

    <select id="selectCrmBusinessApMyApplicationList" parameterType="CrmBusinessApMyApplication" resultMap="CrmBusinessApMyApplicationResult">
        <include refid="selectCrmBusinessApMyApplicationVo"/>
        <where>  
            <if test="contentId != null "> and content_id = #{contentId}</if>
            <if test="instanceId != null "> and instance_id = #{instanceId}</if>
            <if test="currentNodeId != null "> and current_node_id = #{currentNodeId}</if>
            <if test="submitTime != null "> and submit_time = #{submitTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectCrmBusinessApMyApplicationByApplicationId" parameterType="Long" resultMap="CrmBusinessApMyApplicationResult">
        <include refid="selectCrmBusinessApMyApplicationVo"/>
        where application_id = #{applicationId}
    </select>

    <insert id="insertCrmBusinessApMyApplication" parameterType="CrmBusinessApMyApplication" useGeneratedKeys="true" keyProperty="applicationId">
        insert into crm_business_ap_my_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contentId != null">content_id,</if>
            <if test="instanceId != null">instance_id,</if>
            <if test="currentNodeId != null">current_node_id,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contentId != null">#{contentId},</if>
            <if test="instanceId != null">#{instanceId},</if>
            <if test="currentNodeId != null">#{currentNodeId},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateCrmBusinessApMyApplication" parameterType="CrmBusinessApMyApplication">
        update crm_business_ap_my_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="contentId != null">content_id = #{contentId},</if>
            <if test="instanceId != null">instance_id = #{instanceId},</if>
            <if test="currentNodeId != null">current_node_id = #{currentNodeId},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where application_id = #{applicationId}
    </update>

    <delete id="deleteCrmBusinessApMyApplicationByApplicationId" parameterType="Long">
        delete from crm_business_ap_my_application where application_id = #{applicationId}
    </delete>

    <delete id="deleteCrmBusinessApMyApplicationByApplicationIds" parameterType="String">
        delete from crm_business_ap_my_application where application_id in 
        <foreach item="applicationId" collection="array" open="(" separator="," close=")">
            #{applicationId}
        </foreach>
    </delete>
</mapper>