<template>
  <div class="opportunity-team-tab">
    <!-- 团队信息卡片 -->
    <div class="team-info-section">
      <el-card shadow="never" class="team-card">
        <template #header>
          <div class="card-header">
            <span>团队信息</span>
            <el-button 
              v-if="!readonly && !currentTeam" 
              type="primary" 
              size="small" 
              @click="openAssignDialog"
            >
              分配团队
            </el-button>
          </div>
        </template>
        
        <div v-if="currentTeam" class="current-team">
          <div class="team-info">
            <el-avatar :size="40" :src="currentTeam.avatar">
              {{ currentTeam.teamName?.charAt(0) }}
            </el-avatar>
            <div class="team-details">
              <h4>{{ currentTeam.teamName }}</h4>
              <p class="team-leader">负责人：{{ currentTeam.leaderName || '未设置' }}</p>
              <p class="assign-time">分配时间：{{ formatDate(currentTeam.createTime) }}</p>
            </div>
          </div>
          <div class="team-actions">
            <el-button 
              v-if="!readonly" 
              type="danger" 
              size="small" 
              plain 
              @click="handleUnassign"
            >
              取消分配
            </el-button>
          </div>
        </div>
        
        <div v-else class="no-team">
          <el-empty description="暂未分配团队">
            <el-button 
              v-if="!readonly" 
              type="primary" 
              @click="openAssignDialog"
            >
              分配团队
            </el-button>
          </el-empty>
        </div>
      </el-card>
    </div>

    <!-- 团队成员列表 -->
    <div v-if="currentTeam" class="team-members-section">
      <el-card shadow="never" class="members-card">
        <template #header>
          <div class="card-header">
            <span>团队成员 ({{ teamMembers.length }})</span>
            <div class="header-actions">
              <el-radio-group v-model="viewMode" size="small">
                <el-radio-button label="list">列表</el-radio-button>
                <el-radio-button label="grid">网格</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>

        <el-skeleton v-if="loading" :rows="3" animated />

        <!-- 列表视图 -->
        <div v-else-if="viewMode === 'list'" class="members-list">
          <el-table :data="teamMembers" style="width: 100%">
            <el-table-column label="成员" min-width="200">
              <template #default="{ row }">
                <div class="member-info">
                  <el-avatar :size="32" :src="row.avatar">
                    {{ row.nickName?.charAt(0) }}
                  </el-avatar>
                  <div class="member-details">
                    <div class="member-name">{{ row.nickName || row.userName }}</div>
                    <div class="member-username">@{{ row.userName }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="角色" width="120">
              <template #default="{ row }">
                <el-tag :type="getRoleTagType(row.roleType)" size="small">
                  {{ getRoleLabel(row.roleType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === '0' ? 'success' : 'danger'" size="small">
                  {{ row.status === '0' ? '正常' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="加入时间" width="160">
              <template #default="{ row }">
                {{ formatDate(row.joinTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button 
                  type="primary" 
                  link 
                  size="small" 
                  @click="viewMemberDetail(row)"
                >
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 网格视图 -->
        <div v-else class="members-grid">
          <el-row :gutter="16">
            <el-col 
              :xs="24" :sm="12" :md="8" :lg="6" 
              v-for="member in teamMembers" 
              :key="member.id"
            >
              <div class="member-card" @click="viewMemberDetail(member)">
                <div class="member-avatar">
                  <el-avatar :size="60" :src="member.avatar">
                    {{ member.nickName?.charAt(0) }}
                  </el-avatar>
                  <div class="status-indicator" :class="member.status === '0' ? 'online' : 'offline'"></div>
                </div>
                <div class="member-info">
                  <h5>{{ member.nickName || member.userName }}</h5>
                  <p class="username">@{{ member.userName }}</p>
                  <el-tag :type="getRoleTagType(member.roleType)" size="small">
                    {{ getRoleLabel(member.roleType) }}
                  </el-tag>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 空状态 -->
        <div v-if="teamMembers.length === 0 && !loading" class="empty-members">
          <el-empty description="团队暂无成员" />
        </div>
      </el-card>
    </div>

    <!-- 团队分配对话框 -->
    <TeamAssignDialog
      v-model:visible="assignDialogVisible"
      :biz-id="entityData?.id"
      biz-type="OPPORTUNITY"
      :biz-name="entityData?.name"
      title="分配商机团队"
      @success="handleAssignSuccess"
    />

    <!-- 成员详情抽屉 -->
    <el-drawer v-model="memberDetailVisible" title="成员详情" size="400px">
      <div v-if="selectedMember" class="member-detail">
        <div class="detail-header">
          <el-avatar :size="80" :src="selectedMember.avatar">
            {{ selectedMember.nickName?.charAt(0) }}
          </el-avatar>
          <h3>{{ selectedMember.nickName || selectedMember.userName }}</h3>
          <p>@{{ selectedMember.userName }}</p>
        </div>
        <div class="detail-info">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="角色">
              <el-tag :type="getRoleTagType(selectedMember.roleType)">
                {{ getRoleLabel(selectedMember.roleType) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="selectedMember.status === '0' ? 'success' : 'danger'">
                {{ selectedMember.status === '0' ? '正常' : '停用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="加入时间">
              {{ formatDate(selectedMember.joinTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="所属团队">
              {{ selectedMember.teamName || currentTeam?.teamName }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import {
    getTeamByBiz,
    getTeamMembersByBiz,
    unassignTeamFromBiz
} from '@/api/team-relation'
import TeamAssignDialog from '@/components/TeamAssignDialog.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, ref, watch } from 'vue'

// 定义接口
interface TeamInfo {
  teamId: number
  teamName: string
  leaderName?: string
  createTime: string
  avatar?: string
}

interface TeamMember {
  id: number
  userId: number
  userName: string
  nickName?: string
  roleType: string
  status: string
  joinTime: string
  avatar?: string
  teamName?: string
}

interface Props {
  entityData?: {
    id: number
    name: string
  }
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

// 响应式数据
const loading = ref(false)
const viewMode = ref<'list' | 'grid'>('list')
const currentTeam = ref<TeamInfo | null>(null)
const teamMembers = ref<TeamMember[]>([])
const assignDialogVisible = ref(false)
const memberDetailVisible = ref(false)
const selectedMember = ref<TeamMember | null>(null)

// 方法
const loadTeamInfo = async () => {
  if (!props.entityData?.id) return

  loading.value = true
  try {
    const response = await getTeamByBiz(props.entityData.id, 'OPPORTUNITY')
    currentTeam.value = response.data || null
  } catch (error) {
    console.error('加载团队信息失败:', error)
  } finally {
    loading.value = false
  }
}

const loadTeamMembers = async () => {
  if (!props.entityData?.id) return

  loading.value = true
  try {
    const response = await getTeamMembersByBiz(props.entityData.id, 'OPPORTUNITY')
    teamMembers.value = response.data?.rows || response.data || []
  } catch (error) {
    console.error('加载团队成员失败:', error)
    teamMembers.value = []
  } finally {
    loading.value = false
  }
}

const openAssignDialog = () => {
  assignDialogVisible.value = true
}

const handleUnassign = async () => {
  if (!props.entityData?.id) return

  try {
    await ElMessageBox.confirm(
      '确定要取消当前的团队分配吗？',
      '确认取消分配',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await unassignTeamFromBiz(props.entityData.id, 'OPPORTUNITY')
    ElMessage.success('取消分配成功')
    
    // 重新加载数据
    currentTeam.value = null
    teamMembers.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消分配失败:', error)
      ElMessage.error('取消分配失败')
    }
  }
}

const handleAssignSuccess = () => {
  ElMessage.success('团队分配成功')
  loadTeamInfo()
  loadTeamMembers()
}

const viewMemberDetail = (member: TeamMember) => {
  selectedMember.value = member
  memberDetailVisible.value = true
}

const getRoleTagType = (roleType: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    owner: 'danger',
    admin: 'warning',
    member: 'info'
  };
  return typeMap[roleType] || 'info';
}

const getRoleLabel = (roleType: string) => {
  const labelMap: Record<string, string> = {
    owner: '负责人',
    admin: '管理员',
    member: '成员'
  }
  return labelMap[roleType] || '成员'
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 监听props变化
watch(() => props.entityData?.id, (newId) => {
  if (newId) {
    loadTeamInfo()
    loadTeamMembers()
  }
}, { immediate: true })

onMounted(() => {
  if (props.entityData?.id) {
    loadTeamInfo()
    loadTeamMembers()
  }
})
</script>

<style scoped>
.opportunity-team-tab {
  padding: 16px;
}

.team-info-section {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-team {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.team-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.team-details h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
}

.team-details p {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.members-list .member-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.member-details .member-name {
  font-weight: 500;
}

.member-details .member-username {
  font-size: 12px;
  color: #666;
}

.members-grid .member-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
}

.members-grid .member-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.member-avatar {
  position: relative;
  display: inline-block;
  margin-bottom: 8px;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #fff;
}

.status-indicator.online {
  background-color: #67c23a;
}

.status-indicator.offline {
  background-color: #f56c6c;
}

.member-info h5 {
  margin: 0 0 4px 0;
  font-size: 14px;
}

.member-info .username {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #666;
}

.member-detail .detail-header {
  text-align: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 20px;
}

.member-detail h3 {
  margin: 12px 0 4px 0;
}

.member-detail p {
  margin: 0;
  color: #666;
}
</style>
