/**
 * 缓存工具类
 */
interface Cache {
  set(key: string, value: any): void;
  get(key: string): string | null;
  setJSON(key: string, value: any): void;
  getJSON(key: string): any;
  remove(key: string): void;
}

class StorageCache implements Cache {
  private storage: Storage;

  constructor(storage: Storage) {
    this.storage = storage;
  }

  set(key: string, value: any): void {
    this.storage.setItem(key, value);
  }

  get(key: string): string | null {
    return this.storage.getItem(key);
  }

  setJSON(key: string, value: any): void {
    this.storage.setItem(key, JSON.stringify(value));
  }

  getJSON(key: string): any {
    const value = this.storage.getItem(key);
    if (value) {
      try {
        return JSON.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  remove(key: string): void {
    this.storage.removeItem(key);
  }
}

export default {
  session: new StorageCache(sessionStorage),
  local: new StorageCache(localStorage)
};