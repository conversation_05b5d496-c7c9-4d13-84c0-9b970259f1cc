package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.CrmBusinessContracts;

/**
 * 合同Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface ICrmBusinessContractsService 
{
    /**
     * 查询合同
     * 
     * @param id 合同主键
     * @return 合同
     */
    public CrmBusinessContracts selectCrmBusinessContractsById(Long id);

    /**
     * 查询合同列表
     * 
     * @param crmBusinessContracts 合同
     * @return 合同集合
     */
    public List<CrmBusinessContracts> selectCrmBusinessContractsList(CrmBusinessContracts crmBusinessContracts);

    /**
     * 新增合同
     * 
     * @param crmBusinessContracts 合同
     * @return 结果
     */
    public int insertCrmBusinessContracts(CrmBusinessContracts crmBusinessContracts);

    /**
     * 修改合同
     * 
     * @param crmBusinessContracts 合同
     * @return 结果
     */
    public int updateCrmBusinessContracts(CrmBusinessContracts crmBusinessContracts);

    /**
     * 批量删除合同
     * 
     * @param ids 需要删除的合同主键集合
     * @return 结果
     */
    public int deleteCrmBusinessContractsByIds(Long[] ids);

    /**
     * 删除合同信息
     * 
     * @param id 合同主键
     * @return 结果
     */
    public int deleteCrmBusinessContractsById(Long id);
}
