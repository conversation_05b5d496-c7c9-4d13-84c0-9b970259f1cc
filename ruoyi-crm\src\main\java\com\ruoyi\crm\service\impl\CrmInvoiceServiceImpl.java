package com.ruoyi.crm.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.domain.CrmInvoice;
import com.ruoyi.common.domain.CrmInvoiceItem;
import com.ruoyi.common.domain.CrmInvoiceAttachment;
import com.ruoyi.common.domain.CrmQuotation;
import com.ruoyi.common.domain.CrmQuotationItem;
import com.ruoyi.common.mapper.CrmInvoiceMapper;
import com.ruoyi.common.mapper.CrmQuotationMapper;
import com.ruoyi.crm.service.ICrmInvoiceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 发票Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Service
public class CrmInvoiceServiceImpl implements ICrmInvoiceService {
    private static final Logger logger = LoggerFactory.getLogger(CrmInvoiceServiceImpl.class);

    @Autowired
    private CrmInvoiceMapper crmInvoiceMapper;
    
    @Autowired
    private CrmQuotationMapper crmQuotationMapper;

    /**
     * 查询发票
     * 
     * @param id 发票主键
     * @return 发票
     */
    @Override
    public CrmInvoice selectCrmInvoiceById(Long id) {
        return crmInvoiceMapper.selectCrmInvoiceById(id);
    }

    /**
     * 查询发票列表
     * 
     * @param crmInvoice 发票
     * @return 发票
     */
    @Override
    public List<CrmInvoice> selectCrmInvoiceList(CrmInvoice crmInvoice) {
        return crmInvoiceMapper.selectCrmInvoiceList(crmInvoice);
    }

    /**
     * 新增发票
     * 
     * @param crmInvoice 发票
     * @return 结果
     */
    @Transactional
    @Override
    public int insertCrmInvoice(CrmInvoice crmInvoice) {
        // 生成发票编号
        if (StringUtils.isEmpty(crmInvoice.getInvoiceNo())) {
            crmInvoice.setInvoiceNo(generateInvoiceNo());
        }
        
        // 计算发票金额
        calculateInvoiceAmounts(crmInvoice);
        
        // 设置默认状态
        if (StringUtils.isEmpty(crmInvoice.getStatus())) {
            crmInvoice.setStatus("draft");
        }
        if (StringUtils.isEmpty(crmInvoice.getApprovalStatus())) {
            crmInvoice.setApprovalStatus("pending");
        }
        if (StringUtils.isEmpty(crmInvoice.getCurrency())) {
            crmInvoice.setCurrency("CNY");
        }
        if (crmInvoice.getExchangeRate() == null) {
            crmInvoice.setExchangeRate(BigDecimal.ONE);
        }
        if (StringUtils.isEmpty(crmInvoice.getInvoiceType())) {
            crmInvoice.setInvoiceType("special");
        }
        
        crmInvoice.setCreateTime(DateUtils.getNowDate());
        crmInvoice.setCreateBy(SecurityUtils.getUsername());
        
        int rows = crmInvoiceMapper.insertCrmInvoice(crmInvoice);
        insertCrmInvoiceItem(crmInvoice);
        insertCrmInvoiceAttachment(crmInvoice);
        
        logger.info("新增发票: {}, ID: {}", crmInvoice.getInvoiceTitle(), crmInvoice.getId());
        return rows;
    }

    /**
     * 修改发票
     * 
     * @param crmInvoice 发票
     * @return 结果
     */
    @Transactional
    @Override
    public int updateCrmInvoice(CrmInvoice crmInvoice) {
        // 计算发票金额
        calculateInvoiceAmounts(crmInvoice);
        
        crmInvoice.setUpdateTime(DateUtils.getNowDate());
        crmInvoice.setUpdateBy(SecurityUtils.getUsername());
        
        crmInvoiceMapper.deleteCrmInvoiceItemByInvoiceId(crmInvoice.getId());
        insertCrmInvoiceItem(crmInvoice);
        
        crmInvoiceMapper.deleteCrmInvoiceAttachmentByInvoiceId(crmInvoice.getId());
        insertCrmInvoiceAttachment(crmInvoice);
        
        int rows = crmInvoiceMapper.updateCrmInvoice(crmInvoice);
        
        logger.info("修改发票: {}, ID: {}", crmInvoice.getInvoiceTitle(), crmInvoice.getId());
        return rows;
    }

    /**
     * 批量删除发票
     * 
     * @param ids 需要删除的发票主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteCrmInvoiceByIds(Long[] ids) {
        crmInvoiceMapper.deleteCrmInvoiceItemByInvoiceIds(ids);
        crmInvoiceMapper.deleteCrmInvoiceAttachmentByInvoiceIds(ids);
        int rows = crmInvoiceMapper.deleteCrmInvoiceByIds(ids);
        
        logger.info("批量删除发票数量: {}", ids.length);
        return rows;
    }

    /**
     * 删除发票信息
     * 
     * @param id 发票主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteCrmInvoiceById(Long id) {
        crmInvoiceMapper.deleteCrmInvoiceItemByInvoiceId(id);
        crmInvoiceMapper.deleteCrmInvoiceAttachmentByInvoiceId(id);
        int rows = crmInvoiceMapper.deleteCrmInvoiceById(id);
        
        logger.info("删除发票 ID: {}", id);
        return rows;
    }

    /**
     * 新增发票明细信息
     * 
     * @param crmInvoice 发票对象
     */
    public void insertCrmInvoiceItem(CrmInvoice crmInvoice) {
        List<CrmInvoiceItem> crmInvoiceItemList = crmInvoice.getInvoiceItems();
        Long invoiceId = crmInvoice.getId();
        if (!CollectionUtils.isEmpty(crmInvoiceItemList)) {
            int sortOrder = 1;
            for (CrmInvoiceItem crmInvoiceItem : crmInvoiceItemList) {
                crmInvoiceItem.setInvoiceId(invoiceId);
                if (crmInvoiceItem.getSortOrder() == null) {
                    crmInvoiceItem.setSortOrder(sortOrder++);
                }
                // 计算明细金额和税额
                if (crmInvoiceItem.getQuantity() != null && crmInvoiceItem.getUnitPrice() != null) {
                    BigDecimal amount = crmInvoiceItem.getQuantity().multiply(crmInvoiceItem.getUnitPrice());
                    crmInvoiceItem.setAmount(amount);
                    
                    // 计算税额
                    BigDecimal taxRate = crmInvoiceItem.getTaxRate();
                    if (taxRate == null) {
                        taxRate = crmInvoice.getTaxRate() != null ? crmInvoice.getTaxRate() : new BigDecimal("0.13");
                        crmInvoiceItem.setTaxRate(taxRate);
                    }
                    BigDecimal taxAmount = amount.multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
                    crmInvoiceItem.setTaxAmount(taxAmount);
                }
            }
            crmInvoiceMapper.batchCrmInvoiceItem(crmInvoiceItemList);
        }
    }

    /**
     * 新增发票附件信息
     * 
     * @param crmInvoice 发票对象
     */
    public void insertCrmInvoiceAttachment(CrmInvoice crmInvoice) {
        List<CrmInvoiceAttachment> crmInvoiceAttachmentList = crmInvoice.getInvoiceAttachments();
        Long invoiceId = crmInvoice.getId();
        if (!CollectionUtils.isEmpty(crmInvoiceAttachmentList)) {
            int sortOrder = 1;
            for (CrmInvoiceAttachment crmInvoiceAttachment : crmInvoiceAttachmentList) {
                crmInvoiceAttachment.setInvoiceId(invoiceId);
                if (crmInvoiceAttachment.getSortOrder() == null) {
                    crmInvoiceAttachment.setSortOrder(sortOrder++);
                }
                if (StringUtils.isEmpty(crmInvoiceAttachment.getDelFlag())) {
                    crmInvoiceAttachment.setDelFlag("0");
                }
                if (StringUtils.isEmpty(crmInvoiceAttachment.getIsMain())) {
                    crmInvoiceAttachment.setIsMain("0");
                }
                crmInvoiceAttachment.setCreateTime(DateUtils.getNowDate());
                crmInvoiceAttachment.setCreateBy(SecurityUtils.getUsername());
            }
            crmInvoiceMapper.batchCrmInvoiceAttachment(crmInvoiceAttachmentList);
        }
    }

    @Override
    public CrmInvoice selectCrmInvoiceByInvoiceNo(String invoiceNo) {
        return crmInvoiceMapper.selectCrmInvoiceByInvoiceNo(invoiceNo);
    }

    @Override
    public List<CrmInvoice> selectCrmInvoiceByQuotationId(Long quotationId) {
        return crmInvoiceMapper.selectCrmInvoiceByQuotationId(quotationId);
    }

    @Override
    @Transactional
    public CrmInvoice createInvoiceFromQuotation(Long quotationId) {
        CrmQuotation quotation = crmQuotationMapper.selectCrmQuotationById(quotationId);
        if (quotation == null) {
            throw new RuntimeException("报价单不存在");
        }
        
        if (!"approved".equals(quotation.getApprovalStatus())) {
            throw new RuntimeException("只有审批通过的报价单才能创建发票");
        }
        
        CrmInvoice invoice = new CrmInvoice();
        
        // 复制基本信息
        invoice.setInvoiceTitle(quotation.getCustomerName());
        invoice.setCustomerId(quotation.getCustomerId());
        invoice.setCustomerName(quotation.getCustomerName());
        invoice.setContactId(quotation.getContactId());
        invoice.setContactName(quotation.getContactName());
        invoice.setQuotationId(quotation.getId());
        invoice.setResponsiblePersonId(quotation.getResponsiblePersonId());
        invoice.setCurrency(quotation.getCurrency());
        invoice.setRemarks("基于报价单 " + quotation.getQuotationNo() + " 创建");
        
        // 设置默认税率
        invoice.setTaxRate(new BigDecimal("0.13"));
        
        // 转换报价单明细为发票明细
        List<CrmInvoiceItem> invoiceItems = new ArrayList<>();
        if (!CollectionUtils.isEmpty(quotation.getQuotationItems())) {
            for (CrmQuotationItem quotationItem : quotation.getQuotationItems()) {
                CrmInvoiceItem invoiceItem = new CrmInvoiceItem();
                invoiceItem.setItemName(quotationItem.getProductName());
                invoiceItem.setItemCode(quotationItem.getProductCode());
                invoiceItem.setSpecification(quotationItem.getSpecification());
                invoiceItem.setUnit(quotationItem.getUnit());
                invoiceItem.setQuantity(quotationItem.getQuantity());
                invoiceItem.setUnitPrice(quotationItem.getUnitPrice());
                invoiceItem.setSortOrder(quotationItem.getSortOrder());
                invoiceItem.setRemarks(quotationItem.getRemarks());
                
                invoiceItems.add(invoiceItem);
            }
        }
        invoice.setInvoiceItems(invoiceItems);
        
        int result = insertCrmInvoice(invoice);
        if (result > 0) {
            logger.info("从报价单创建发票成功: 报价单ID={}, 发票ID={}", quotationId, invoice.getId());
            return selectCrmInvoiceById(invoice.getId());
        }
        
        throw new RuntimeException("创建发票失败");
    }

    @Override
    public int submitInvoiceApproval(Long invoiceId, String processDefinitionKey) {
        // TODO: 集成Activiti工作流
        CrmInvoice invoice = crmInvoiceMapper.selectCrmInvoiceById(invoiceId);
        if (invoice == null) {
            throw new RuntimeException("发票不存在");
        }
        
        if (!"draft".equals(invoice.getStatus())) {
            throw new RuntimeException("只有草稿状态的发票才能提交审批");
        }
        
        // 更新状态
        invoice.setStatus("submitted");
        invoice.setApprovalStatus("pending");
        invoice.setUpdateTime(DateUtils.getNowDate());
        invoice.setUpdateBy(SecurityUtils.getUsername());
        
        int rows = crmInvoiceMapper.updateCrmInvoice(invoice);
        
        logger.info("提交发票审批: {}, ID: {}", invoice.getInvoiceTitle(), invoiceId);
        return rows;
    }

    @Override
    public int approveInvoice(String taskId, boolean approved, String comment) {
        // TODO: 集成Activiti工作流处理审批
        logger.info("审批发票任务: {}, 审批结果: {}, 意见: {}", taskId, approved, comment);
        return 1;
    }

    @Override
    public int issueInvoice(Long invoiceId) {
        CrmInvoice invoice = crmInvoiceMapper.selectCrmInvoiceById(invoiceId);
        if (invoice == null) {
            throw new RuntimeException("发票不存在");
        }
        
        if (!"approved".equals(invoice.getApprovalStatus())) {
            throw new RuntimeException("只有审批通过的发票才能开票");
        }
        
        // 更新状态和开票日期
        invoice.setStatus("issued");
        invoice.setIssueDate(new Date());
        invoice.setUpdateTime(DateUtils.getNowDate());
        invoice.setUpdateBy(SecurityUtils.getUsername());
        
        int rows = crmInvoiceMapper.updateCrmInvoice(invoice);
        
        logger.info("开票: {}, ID: {}", invoice.getInvoiceTitle(), invoiceId);
        return rows;
    }

    @Override
    public int cancelInvoice(Long invoiceId, String reason) {
        CrmInvoice invoice = crmInvoiceMapper.selectCrmInvoiceById(invoiceId);
        if (invoice == null) {
            throw new RuntimeException("发票不存在");
        }
        
        if (!"issued".equals(invoice.getStatus())) {
            throw new RuntimeException("只有已开票状态的发票才能作废");
        }
        
        // 更新状态
        invoice.setStatus("cancelled");
        invoice.setCancelDate(new Date());
        invoice.setCancelReason(reason);
        invoice.setUpdateTime(DateUtils.getNowDate());
        invoice.setUpdateBy(SecurityUtils.getUsername());
        
        int rows = crmInvoiceMapper.updateCrmInvoice(invoice);
        
        logger.info("作废发票: {}, ID: {}, 原因: {}", invoice.getInvoiceTitle(), invoiceId, reason);
        return rows;
    }

    @Override
    public String generateInvoiceNo() {
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String prefix = "INV" + dateStr;
        
        // TODO: 实现自增序号逻辑，确保编号唯一性
        long currentTimeMillis = System.currentTimeMillis();
        String suffix = String.format("%04d", currentTimeMillis % 10000);
        
        return prefix + suffix;
    }

    @Override
    public void calculateInvoiceAmounts(CrmInvoice crmInvoice) {
        if (CollectionUtils.isEmpty(crmInvoice.getInvoiceItems())) {
            crmInvoice.setAmountExcludingTax(BigDecimal.ZERO);
            crmInvoice.setTaxAmount(BigDecimal.ZERO);
            crmInvoice.setAmountIncludingTax(BigDecimal.ZERO);
            return;
        }
        
        BigDecimal totalAmountExcludingTax = BigDecimal.ZERO;
        BigDecimal totalTaxAmount = BigDecimal.ZERO;
        
        for (CrmInvoiceItem item : crmInvoice.getInvoiceItems()) {
            if (item.getAmount() != null) {
                totalAmountExcludingTax = totalAmountExcludingTax.add(item.getAmount());
            }
            if (item.getTaxAmount() != null) {
                totalTaxAmount = totalTaxAmount.add(item.getTaxAmount());
            }
        }
        
        crmInvoice.setAmountExcludingTax(totalAmountExcludingTax);
        crmInvoice.setTaxAmount(totalTaxAmount);
        crmInvoice.setAmountIncludingTax(totalAmountExcludingTax.add(totalTaxAmount));
    }
}