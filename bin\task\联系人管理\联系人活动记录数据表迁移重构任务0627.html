<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人活动记录数据表迁移重构任务</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #e74c3c;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
            text-align: center;
        }
        h2 {
            color: #3498db;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #27ae60;
            margin-top: 25px;
        }
        h4 {
            color: #8e44ad;
            margin-top: 20px;
        }
        .urgent {
            background-color: #fff5f5;
            border: 2px solid #e74c3c;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .important {
            background-color: #f0f8ff;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }
        .task-list {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .code-block {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Consolas', 'Monaco', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .file-path {
            background-color: #34495e;
            color: #ecf0f1;
            padding: 5px 10px;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9em;
        }
        .status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status.todo {
            background-color: #ffeaa7;
            color: #fdcb6e;
        }
        .status.progress {
            background-color: #74b9ff;
            color: white;
        }
        .status.done {
            background-color: #00b894;
            color: white;
        }
        .priority {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }
        .priority.high {
            background-color: #e74c3c;
            color: white;
        }
        .priority.medium {
            background-color: #f39c12;
            color: white;
        }
        .priority.low {
            background-color: #95a5a6;
            color: white;
        }
        ol, ul {
            margin-left: 20px;
        }
        li {
            margin: 8px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .timeline {
            border-left: 3px solid #3498db;
            padding-left: 20px;
            margin: 20px 0;
        }
        .timeline-item {
            margin: 20px 0;
            position: relative;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -26px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #3498db;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 联系人活动记录数据表迁移重构任务</h1>
        
        <div class="urgent">
            <h3>🚨 紧急问题描述</h3>
            <p>发现重要架构错误：当前联系人控制器中的活动记录功能使用了过时的 <code>crm_business_follow_up_records</code> 表，该表已被标记为【obsolete】。需要迁移到正确的 <code>crm_contact_followup_records</code> 表。</p>
        </div>

        <h2>📋 任务概览</h2>
        <div class="important">
            <p><strong>项目目标：</strong> 将联系人活动记录功能从过时的数据表迁移到新的专用数据表，完善整个技术栈的实现。</p>
            <p><strong>预计工期：</strong> 3-5个工作日</p>
            <p><strong>影响范围：</strong> 后端Java层、前端Vue层、数据库结构</p>
        </div>

        <h2>🎯 具体任务分解</h2>

        <h3>阶段一：后端Java层重构 <span class="priority high">高优先级</span></h3>
        
        <h4>1.1 创建新的实体类 <span class="status todo">待完成</span></h4>
        <div class="task-list">
            <p><strong>文件路径：</strong> <span class="file-path">ruoyi-crm/src/main/java/com/ruoyi/crm/domain/CrmContactFollowupRecords.java</span></p>
            <p><strong>任务内容：</strong></p>
            <ul>
                <li>根据 <code>crm_contact_followup_records</code> 表结构创建实体类</li>
                <li>包含字段：id, contactId, followUpContent, nextContactMethod, followUpMethod, nextContactTime, communicationResult, meetingSummary, contactQuality, relatedFiles, creatorId, createdAt, updatedAt</li>
                <li>继承 BaseEntity，添加适当的注解(@Excel, @JsonFormat等)</li>
                <li>实现序列化接口</li>
            </ul>
        </div>

        <h4>1.2 创建Mapper接口 <span class="status todo">待完成</span></h4>
        <div class="task-list">
            <p><strong>文件路径：</strong> <span class="file-path">ruoyi-crm/src/main/java/com/ruoyi/crm/mapper/CrmContactFollowupRecordsMapper.java</span></p>
            <p><strong>任务内容：</strong></p>
            <ul>
                <li>定义CRUD基础方法</li>
                <li>添加专门的查询方法：<code>selectByContactId(Long contactId)</code></li>
                <li>添加统计方法：<code>countByContactId(Long contactId)</code></li>
                <li>添加最新记录查询：<code>selectLatestByContactId(Long contactId)</code></li>
            </ul>
        </div>

        <h4>1.3 创建XML映射文件 <span class="status todo">待完成</span></h4>
        <div class="task-list">
            <p><strong>文件路径：</strong> <span class="file-path">ruoyi-crm/src/main/resources/mapper/crm/CrmContactFollowupRecordsMapper.xml</span></p>
            <p><strong>任务内容：</strong></p>
            <ul>
                <li>实现所有Mapper接口方法的SQL语句</li>
                <li>优化查询性能，使用合适的索引</li>
                <li>实现分页查询支持</li>
            </ul>
        </div>

        <h4>1.4 创建Service接口和实现类 <span class="status todo">待完成</span></h4>
        <div class="task-list">
            <p><strong>接口文件：</strong> <span class="file-path">ruoyi-crm/src/main/java/com/ruoyi/crm/service/ICrmContactFollowupRecordsService.java</span></p>
            <p><strong>实现文件：</strong> <span class="file-path">ruoyi-crm/src/main/java/com/ruoyi/crm/service/impl/CrmContactFollowupRecordsServiceImpl.java</span></p>
            <p><strong>任务内容：</strong></p>
            <ul>
                <li>实现完整的业务逻辑</li>
                <li>添加数据验证</li>
                <li>实现事务管理</li>
                <li>添加缓存支持（如果需要）</li>
            </ul>
        </div>

        <h4>1.5 重构联系人控制器 <span class="status todo">待完成</span> <span class="priority high">高优先级</span></h4>
        <div class="task-list">
            <p><strong>文件路径：</strong> <span class="file-path">ruoyi-crm/src/main/java/com/ruoyi/crm/controller/CrmContactsController.java</span></p>
            <p><strong>需要修改的方法：</strong></p>
            <ul>
                <li><code>getContactActivities()</code> - 获取联系人活动记录</li>
                <li><code>createContactActivity()</code> - 创建联系人活动记录</li>
                <li><code>updateContactActivity()</code> - 编辑联系人活动记录</li>
                <li><code>deleteContactActivity()</code> - 删除联系人活动记录</li>
                <li><code>getContactActivityStats()</code> - 获取联系人活动记录统计</li>
            </ul>
            <p><strong>具体修改：</strong></p>
            <ul>
                <li>将 <code>ICrmBusinessFollowUpRecordsService</code> 替换为 <code>ICrmContactFollowupRecordsService</code></li>
                <li>将 <code>CrmBusinessFollowUpRecords</code> 实体替换为 <code>CrmContactFollowupRecords</code></li>
                <li>调整字段映射和业务逻辑</li>
            </ul>
        </div>

        <h3>阶段二：前端Vue层重构 <span class="priority medium">中优先级</span></h3>

        <h4>2.1 更新API接口文件 <span class="status todo">待完成</span></h4>
        <div class="task-list">
            <p><strong>文件路径：</strong> <span class="file-path">frontend/src/api/crm/contacts.js</span></p>
            <p><strong>需要添加的API方法：</strong></p>
            <ul>
                <li><code>getContactActivities(contactId)</code> - 获取联系人活动记录</li>
                <li><code>addContactActivity(data)</code> - 创建活动记录</li>
                <li><code>updateContactActivity(contactId, id, data)</code> - 更新活动记录</li>
                <li><code>deleteContactActivity(contactId, id)</code> - 删除活动记录</li>
                <li><code>getContactActivityStats(contactId)</code> - 获取活动统计</li>
            </ul>
        </div>

        <h4>2.2 创建活动记录管理组件 <span class="status todo">待完成</span></h4>
        <div class="task-list">
            <p><strong>文件路径：</strong> <span class="file-path">frontend/src/views/crm/contacts/components/ActivityRecords.vue</span></p>
            <p><strong>组件功能：</strong></p>
            <ul>
                <li>活动记录列表展示</li>
                <li>活动记录的增删改查</li>
                <li>活动记录的筛选和搜索</li>
                <li>活动统计信息展示</li>
            </ul>
        </div>

        <h4>2.3 创建活动记录表单组件 <span class="status todo">待完成</span></h4>
        <div class="task-list">
            <p><strong>文件路径：</strong> <span class="file-path">frontend/src/views/crm/contacts/components/ActivityForm.vue</span></p>
            <p><strong>表单字段：</strong></p>
            <ul>
                <li>跟进内容（富文本编辑器）</li>
                <li>跟进方式（下拉选择）</li>
                <li>下次联系方式</li>
                <li>下次联系时间（日期时间选择器）</li>
                <li>沟通结果</li>
                <li>会议纪要</li>
                <li>联系质量评级</li>
                <li>相关文件上传</li>
            </ul>
        </div>

        <h4>2.4 更新联系人详情页面 <span class="status todo">待完成</span></h4>
        <div class="task-list">
            <p><strong>文件路径：</strong> <span class="file-path">frontend/src/views/crm/contacts/detail.vue</span></p>
            <p><strong>任务内容：</strong></p>
            <ul>
                <li>集成活动记录组件</li>
                <li>添加活动记录选项卡</li>
                <li>优化界面布局</li>
            </ul>
        </div>

        <h3>阶段三：数据库和配置 <span class="priority medium">中优先级</span></h3>

        <h4>3.1 数据库结构验证 <span class="status todo">待完成</span></h4>
        <div class="task-list">
            <p><strong>任务内容：</strong></p>
            <ul>
                <li>确认 <code>crm_contact_followup_records</code> 表已正确创建</li>
                <li>验证索引是否合理</li>
                <li>检查字段类型和约束</li>
            </ul>
        </div>

        <h4>3.2 数据迁移脚本 <span class="status todo">待完成</span></h4>
        <div class="task-list">
            <p><strong>文件路径：</strong> <span class="file-path">ruoyi-crm/sql/migration_contact_activities.sql</span></p>
            <p><strong>任务内容：</strong></p>
            <ul>
                <li>编写数据迁移脚本，将现有的联系人相关记录从旧表迁移到新表</li>
                <li>字段映射处理</li>
                <li>数据完整性验证</li>
            </ul>
        </div>

        <h3>阶段四：测试和验证 <span class="priority high">高优先级</span></h3>
        <h4>4.2 集成测试 <span class="status todo">待完成</span></h4>
        <div class="task-list">
            <p><strong>测试内容：</strong></p>
            <ul>
                <li>端到端业务流程测试</li>
                <li>前后端接口联调测试</li>
                <li>数据一致性测试</li>
            </ul>
        </div>

        <h2>📊 受影响的文件清单</h2>
        <table>
            <thead>
                <tr>
                    <th>文件类型</th>
                    <th>文件路径</th>
                    <th>操作类型</th>
                    <th>优先级</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Java实体类</td>
                    <td>ruoyi-crm/src/main/java/com/ruoyi/crm/domain/CrmContactFollowupRecords.java</td>
                    <td>新建</td>
                    <td><span class="priority high">高</span></td>
                </tr>
                <tr>
                    <td>Java Mapper</td>
                    <td>ruoyi-crm/src/main/java/com/ruoyi/crm/mapper/CrmContactFollowupRecordsMapper.java</td>
                    <td>新建</td>
                    <td><span class="priority high">高</span></td>
                </tr>
                <tr>
                    <td>XML映射</td>
                    <td>ruoyi-crm/src/main/resources/mapper/crm/CrmContactFollowupRecordsMapper.xml</td>
                    <td>新建</td>
                    <td><span class="priority high">高</span></td>
                </tr>
                <tr>
                    <td>Service接口</td>
                    <td>ruoyi-crm/src/main/java/com/ruoyi/crm/service/ICrmContactFollowupRecordsService.java</td>
                    <td>新建</td>
                    <td><span class="priority high">高</span></td>
                </tr>
                <tr>
                    <td>Service实现</td>
                    <td>ruoyi-crm/src/main/java/com/ruoyi/crm/service/impl/CrmContactFollowupRecordsServiceImpl.java</td>
                    <td>新建</td>
                    <td><span class="priority high">高</span></td>
                </tr>
                <tr>
                    <td>Controller</td>
                    <td>ruoyi-crm/src/main/java/com/ruoyi/crm/controller/CrmContactsController.java</td>
                    <td>修改</td>
                    <td><span class="priority high">高</span></td>
                </tr>
                <tr>
                    <td>前端API</td>
                    <td>frontend/src/api/crm/contacts.js</td>
                    <td>修改</td>
                    <td><span class="priority medium">中</span></td>
                </tr>
                <tr>
                    <td>Vue组件</td>
                    <td>frontend/src/views/crm/contacts/components/ActivityRecords.vue</td>
                    <td>新建</td>
                    <td><span class="priority medium">中</span></td>
                </tr>
                <tr>
                    <td>Vue组件</td>
                    <td>frontend/src/views/crm/contacts/components/ActivityForm.vue</td>
                    <td>新建</td>
                    <td><span class="priority medium">中</span></td>
                </tr>
                <tr>
                    <td>Vue页面</td>
                    <td>frontend/src/views/crm/contacts/detail.vue</td>
                    <td>修改</td>
                    <td><span class="priority medium">中</span></td>
                </tr>
            </tbody>
        </table>

        <h2>⚠️ 风险和注意事项</h2>
        <div class="warning">
            <h4>数据完整性风险</h4>
            <ul>
                <li>迁移过程中需要确保现有数据不丢失</li>
                <li>建议在迁移前做完整的数据备份</li>
                <li>新旧表结构字段映射需要仔细验证</li>
            </ul>
        </div>

        <div class="warning">
            <h4>向后兼容性</h4>
            <ul>
                <li>如果有其他模块还在使用旧的 <code>crm_business_follow_up_records</code> 表，需要评估影响</li>
                <li>可能需要保留旧接口一段时间，做渐进式迁移</li>
            </ul>
        </div>

        <div class="warning">
            <h4>性能考虑</h4>
            <ul>
                <li>新表的索引策略需要根据实际查询场景优化</li>
                <li>大量历史数据迁移可能影响系统性能</li>
            </ul>
        </div>

        <h2>🚀 实施时间表</h2>
        <div class="timeline">
            <div class="timeline-item">
                <h4>第1天：后端核心层创建</h4>
                <p>完成实体类、Mapper、XML映射文件的创建</p>
            </div>
            <div class="timeline-item">
                <h4>第2天：Service层和Controller重构</h4>
                <p>实现业务逻辑，重构联系人控制器</p>
            </div>
            <div class="timeline-item">
                <h4>第3天：前端组件开发</h4>
                <p>创建活动记录相关的Vue组件</p>
            </div>
            <div class="timeline-item">
                <h4>第4天：集成和测试</h4>
                <p>前后端联调，功能测试</p>
            </div>
            <div class="timeline-item">
                <h4>第5天：数据迁移和上线</h4>
                <p>执行数据迁移，生产环境部署</p>
            </div>
        </div>

        <h2>✅ 验收标准</h2>
        <div class="important">
            <ol>
                <li>所有联系人活动记录功能正常工作</li>
                <li>数据完整性得到保证，无数据丢失</li>
                <li>前端界面功能完整，用户体验良好</li>
                <li>性能满足要求，响应时间在可接受范围内</li>
                <li>通过所有单元测试和集成测试</li>
                <li>代码质量符合团队规范</li>
            </ol>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>📅 文档创建时间：2025年6月27日</p>
            <p>🔄 最后更新：2025年6月27日</p>
        </div>
    </div>
</body>
</html>
