<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CrmBusinessPaymentDetailsMapper">
    
    <resultMap type="CrmBusinessPaymentDetails" id="CrmBusinessPaymentDetailsResult">
        <result property="id"    column="id"    />
        <result property="paymentId"    column="payment_id"    />
        <result property="paymentPlanId"    column="payment_plan_id"    />
        <result property="paymentDate"    column="payment_date"    />
        <result property="paymentAmount"    column="payment_amount"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="remarks"    column="remarks"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectCrmBusinessPaymentDetailsVo">
        select id, payment_id, payment_plan_id, payment_date, payment_amount, payment_method, remarks, created_at, updated_at from crm_business_payment_details
    </sql>

    <select id="selectCrmBusinessPaymentDetailsList" parameterType="CrmBusinessPaymentDetails" resultMap="CrmBusinessPaymentDetailsResult">
        <include refid="selectCrmBusinessPaymentDetailsVo"/>
        <where>  
            <if test="paymentId != null "> and payment_id = #{paymentId}</if>
            <if test="paymentPlanId != null "> and payment_plan_id = #{paymentPlanId}</if>
            <if test="paymentDate != null "> and payment_date = #{paymentDate}</if>
            <if test="paymentAmount != null "> and payment_amount = #{paymentAmount}</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and payment_method = #{paymentMethod}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectCrmBusinessPaymentDetailsById" parameterType="Long" resultMap="CrmBusinessPaymentDetailsResult">
        <include refid="selectCrmBusinessPaymentDetailsVo"/>
        where id = #{id}
    </select>

    <insert id="insertCrmBusinessPaymentDetails" parameterType="CrmBusinessPaymentDetails" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_payment_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="paymentId != null">payment_id,</if>
            <if test="paymentPlanId != null">payment_plan_id,</if>
            <if test="paymentDate != null">payment_date,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="paymentId != null">#{paymentId},</if>
            <if test="paymentPlanId != null">#{paymentPlanId},</if>
            <if test="paymentDate != null">#{paymentDate},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateCrmBusinessPaymentDetails" parameterType="CrmBusinessPaymentDetails">
        update crm_business_payment_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="paymentId != null">payment_id = #{paymentId},</if>
            <if test="paymentPlanId != null">payment_plan_id = #{paymentPlanId},</if>
            <if test="paymentDate != null">payment_date = #{paymentDate},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmBusinessPaymentDetailsById" parameterType="Long">
        delete from crm_business_payment_details where id = #{id}
    </delete>

    <delete id="deleteCrmBusinessPaymentDetailsByIds" parameterType="String">
        delete from crm_business_payment_details where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>