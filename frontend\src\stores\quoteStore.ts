import { defineStore } from 'pinia';
import { ref } from 'vue';
import { TableRow } from '@/types/printing/quoteTypes';

export interface QuoteData {
    quoteNo: string;
    customerName: string;
    date: string;
    items: TableRow[];
    totalAmount: number;
    sprayOptions: string[];
    insertOptions: string[];
}

export const useQuoteStore = defineStore('quote', () => {
    // State
    const currentQuote = ref<QuoteData | null>(null);
    
    // Actions
    const setQuoteData = (data: QuoteData) => {
        currentQuote.value = data;
    };
    
    const clearQuoteData = () => {
        currentQuote.value = null;
    };
    
    const getQuoteData = () => {
        return currentQuote.value;
    };
    
    return {
        currentQuote,
        setQuoteData,
        clearQuoteData,
        getQuoteData
    };
});