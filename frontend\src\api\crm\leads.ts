import request from '@/utils/request'
import { ApiResponse } from '~/types'

export interface CrmLeads {
  id: number
  name: string
  source: string
  status: string
  phone: string
  email: string
  industry: string
  remarks?: string
  createTime?: string
  lastFollowUpTime?: string
  nextFollowUpTime?: string
  owner?: string
  attachmentsCount?: number
}

export interface CrmLeadsQuery {
  pageNum?: number
  pageSize?: number
  name?: string
  source?: string
  status?: string
  industry?: string
  owner?: string
}

export interface TableDataInfo<T> {
  rows: T[]
  total: number
}

/**
 * 获取我负责的线索列表
 * @param query 查询参数
 * @returns 线索列表
 */
export function getMyLeadsList(query: CrmLeadsQuery): Promise<ApiResponse<TableDataInfo<CrmLeads>>> {
  return request({
    url: '/front/crm/leads/my',
    method: 'get',
    params: query
  })
}

/**
 * 获取下属的线索列表
 * @param query 查询参数
 * @returns 线索列表
 */
export function getSubordinateLeadsList(query: CrmLeadsQuery): Promise<ApiResponse<TableDataInfo<CrmLeads>>> {
  return request({
    url: '/front/crm/leads/subordinate',
    method: 'get',
    params: query
  })
}

/**
 * 获取我关注的线索列表
 * @param query 查询参数
 * @returns 线索列表
 */
export function getFollowedLeadsList(query: CrmLeadsQuery): Promise<ApiResponse<TableDataInfo<CrmLeads>>> {
  return request({
    url: '/front/crm/leads/followed',
    method: 'get',
    params: query
  })
} 