<template>
  <div class="contract-target-chart">
    <div class="chart-content">
      <div class="chart-main" ref="chartRef"></div>
      <div class="chart-summary">
        <div class="summary-item">
          <div class="summary-label">实际完成金额</div>
          <div class="summary-value primary">{{ actualAmount }}</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">目标金额</div>
          <div class="summary-value">{{ targetAmount }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
const actualAmount = ref('0 元')
const targetAmount = ref('0 元')

let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  
  const months = ['2025-01', '2025-03', '2025-05', '2025-07', '2025-09', '2025-11']
  const actualData = [0, 0, 0, 0, 0, 0]
  const targetData = [0, 0, 0, 0, 0, 0]
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value}元<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['实际完成金额'],
      top: 10,
      right: 10,
      textStyle: {
        fontSize: 12,
        color: '#666'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: months,
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#86909c',
        fontSize: 11
      }
    },
    yAxis: {
      type: 'value',
      name: '金额(元)',
      nameTextStyle: {
        color: '#86909c',
        fontSize: 11
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#86909c',
        fontSize: 11,
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + '万'
          }
          return value.toString()
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '实际完成金额',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#1677ff',
          width: 3
        },
        itemStyle: {
          color: '#1677ff',
          borderColor: '#fff',
          borderWidth: 2
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(22, 119, 255, 0.3)'
            },
            {
              offset: 1,
              color: 'rgba(22, 119, 255, 0.05)'
            }
          ])
        },
        data: actualData
      }
    ]
  }
  
  chartInstance.setOption(option)
  
  // 响应式调整
  const resizeObserver = new ResizeObserver(() => {
    chartInstance?.resize()
  })
  resizeObserver.observe(chartRef.value)
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style lang="scss" scoped>
.contract-target-chart {
  padding: 24px;
  height: 100%;
  
  .chart-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .chart-main {
      flex: 1;
      min-height: 300px;
    }
    
    .chart-summary {
      display: flex;
      justify-content: center;
      gap: 48px;
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
      
      .summary-item {
        text-align: center;
        
        .summary-label {
          font-size: 12px;
          color: #86909c;
          margin-bottom: 8px;
        }
        
        .summary-value {
          font-size: 20px;
          font-weight: 600;
          color: #1f2329;
          
          &.primary {
            color: #1677ff;
          }
        }
      }
    }
  }
}
</style> 