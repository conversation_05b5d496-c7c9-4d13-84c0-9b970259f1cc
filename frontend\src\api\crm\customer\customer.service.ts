import request from '@/utils/request'
import { type CreateCustomerDTO, type Customer, type CustomerQueryParams, type UpdateCustomerDTO } from './types'

export class CustomerService {
  // 获取客户列表
  public async getCustomers(params: CustomerQueryParams): Promise<Customer[]> {
    return request.get('/crm/customers', { params })
  }

  // 获取单个客户详情
  public async getCustomerById(id: number): Promise<Customer> {
    return request.get(`/crm/customers/${id}`)
  }

  // 创建新客户
  public async createCustomer(customer: CreateCustomerDTO): Promise<Customer> {
    return request.post('/crm/customers', customer)
  }

  // 更新客户信息
  public async updateCustomer(id: number, customer: UpdateCustomerDTO): Promise<Customer> {
    return request.put(`/crm/customers/${id}`, customer)
  }

  // 删除客户
  public async deleteCustomer(id: number): Promise<void> {
    return request.delete(`/crm/customers/${id}`)
  }

  // 批量删除客户
  public async batchDeleteCustomers(ids: number[]): Promise<void> {
    return request.delete('/crm/customers/batch', { data: { ids } })
  }

  // 导出客户数据
  public async exportCustomers(params: CustomerQueryParams): Promise<Blob> {
    return request.get('/crm/customers/export', { 
      params,
      responseType: 'blob'
    })
  }
} 