<template>
    <div>
        <el-row :gutter="20">
            <el-col :span="6">
                <el-card shadow="always">
                    <el-row>
                        <el-col :span="12">
                            <div>
                                <div>新增客户</div>
                                <div style="font-size: 24px; font-weight: bold;">{{ reportData.newCustomers.count }} 人
                                </div>
                            </div>
                            <div>较上月 <i
                                    :class="{ 'el-icon-arrow-down': reportData.newCustomers.change < 0, 'el-icon-arrow-up': reportData.newCustomers.change > 0 }"></i>{{
                                        reportData.newCustomers.change }}%</div>
                        </el-col>
                        <el-col :span="12">
                            <line-chart :chartData="reportData.newCustomers.chartData" />
                        </el-col>
                    </el-row>
                </el-card>
            </el-col>

            <!-- 新增联系人 -->
            <el-col :span="6">
                <el-card shadow="always">
                    <el-row>
                        <el-col :span="12">
                            <div>
                                <div>新增联系人</div>
                                <div style="font-size: 24px; font-weight: bold;">{{ reportData.newContacts.count }} 人
                                </div>
                            </div>
                            <div>较上月 <i
                                    :class="{ 'el-icon-arrow-down': reportData.newContacts.change < 0, 'el-icon-arrow-up': reportData.newContacts.change > 0 }"></i>{{
                                        reportData.newContacts.change }}%</div>
                        </el-col>
                        <el-col :span="12">
                            <line-chart :chartData="reportData.newContacts.chartData" />
                        </el-col>
                    </el-row>
                </el-card>
            </el-col>

            <!-- 新增机会 -->
            <el-col :span="6">
                <el-card shadow="always">
                    <el-row>
                        <el-col :span="12">
                            <div>
                                <div>新增机会</div>
                                <div style="font-size: 24px; font-weight: bold;">{{ reportData.newOpportunities.count }}
                                    个</div>
                            </div>
                            <div>较上月 <i
                                    :class="{ 'el-icon-arrow-down': reportData.newOpportunities.change < 0, 'el-icon-arrow-up': reportData.newOpportunities.change > 0 }"></i>{{
                                        reportData.newOpportunities.change }}%</div>
                        </el-col>
                        <el-col :span="12">
                            <line-chart :chartData="reportData.newOpportunities.chartData" />
                        </el-col>
                    </el-row>
                </el-card>
            </el-col>

            <!-- 合同金额 -->
            <el-col :span="6">
                <el-card shadow="always">
                    <el-row>
                        <el-col :span="12">
                            <div>
                                <div>合同金额</div>
                                <div style="font-size: 24px; font-weight: bold;">￥{{ reportData.contractAmount.count }}
                                </div>
                            </div>
                            <div>较上月 <i
                                    :class="{ 'el-icon-arrow-down': reportData.contractAmount.change < 0, 'el-icon-arrow-up': reportData.contractAmount.change > 0 }"></i>{{
                                        reportData.contractAmount.change }}%</div>
                        </el-col>
                        <el-col :span="12">
                            <line-chart :chartData="reportData.contractAmount.chartData" />
                        </el-col>
                    </el-row>
                </el-card>
            </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="6">
                <el-card shadow="always">
                    <el-row>
                        <el-col :span="12">
                            <div>
                                <div>机会金额</div>
                                <div style="font-size: 24px; font-weight: bold;">￥{{ reportData.opportunityAmount.count
                                    }}</div>
                            </div>
                            <div>较上月 <i
                                    :class="{ 'el-icon-arrow-down': reportData.opportunityAmount.change < 0, 'el-icon-arrow-up': reportData.opportunityAmount.change > 0 }"></i>{{
                                        reportData.opportunityAmount.change }}%</div>
                        </el-col>
                        <el-col :span="12">
                            <line-chart :chartData="reportData.opportunityAmount.chartData" />
                        </el-col>
                    </el-row>
                </el-card>
            </el-col>

            <!-- 回款金额 -->
            <el-col :span="6">
                <el-card shadow="always">
                    <el-row>
                        <el-col :span="12">
                            <div>
                                <div>回款金额</div>
                                <div style="font-size: 24px; font-weight: bold;">￥{{ reportData.receivedAmount.count }}
                                </div>
                            </div>
                            <div>较上月 <i
                                    :class="{ 'el-icon-arrow-down': reportData.receivedAmount.change < 0, 'el-icon-arrow-up': reportData.receivedAmount.change > 0 }"></i>{{
                                        reportData.receivedAmount.change }}%</div>
                        </el-col>
                        <el-col :span="12">
                            <line-chart :chartData="reportData.receivedAmount.chartData" />
                        </el-col>
                    </el-row>
                </el-card>
            </el-col>

            <!-- 新增合同 -->
            <el-col :span="6">
                <el-card shadow="always">
                    <el-row>
                        <el-col :span="12">
                            <div>
                                <div>新增合同</div>
                                <div style="font-size: 24px; font-weight: bold;">{{ reportData.newContracts.count }} 份
                                </div>
                            </div>
                            <div>较上月 <i
                                    :class="{ 'el-icon-arrow-down': reportData.newContracts.change < 0, 'el-icon-arrow-up': reportData.newContracts.change > 0 }"></i>{{
                                        reportData.newContracts.change }}%</div>
                        </el-col>
                        <el-col :span="12">
                            <line-chart :chartData="reportData.newContracts.chartData" />
                        </el-col>
                    </el-row>
                </el-card>
            </el-col>

            <!-- 新增跟进 -->
            <el-col :span="6">
                <el-card shadow="always">
                    <el-row>
                        <el-col :span="12">
                            <div>
                                <div>新增跟进</div>
                                <div style="font-size: 24px; font-weight: bold;">{{ reportData.newFollowUps.count }} 次
                                </div>
                            </div>
                            <div>较上月 <i
                                    :class="{ 'el-icon-arrow-down': reportData.newFollowUps.change < 0, 'el-icon-arrow-up': reportData.newFollowUps.change > 0 }"></i>{{
                                        reportData.newFollowUps.change }}%</div>
                        </el-col>
                        <el-col :span="12">
                            <line-chart :chartData="reportData.newFollowUps.chartData" />
                        </el-col>
                    </el-row>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import LineChart from './LineChart.vue';

export default {
    components: {
        LineChart
    },
    data() {
        return {
            reportData: {
                newCustomers: {
                    count: 0,
                    change: 0,
                    chartData: this.generateRandomData(),
                },
                newContacts: {
                    count: 0,
                    change: 0,
                    chartData: this.generateRandomData(),
                },
                newOpportunities: {
                    count: 0,
                    change: 0,
                    chartData: this.generateRandomData(),
                },
                contractAmount: {
                    count: 0,
                    change: 0,
                    chartData: this.generateRandomData(),
                },
                opportunityAmount: {
                    count: 0,
                    change: 0,
                    chartData: this.generateRandomData(),
                },
                receivedAmount: {
                    count: 0,
                    change: 0,
                    chartData: this.generateRandomData(),
                },
                newContracts: {
                    count: 0,
                    change: 0,
                    chartData: this.generateRandomData(),
                },
                newFollowUps: {
                    count: 0,
                    change: 0,
                    chartData: this.generateRandomData(),
                }
            }
        };
    },
    methods: {
        generateRandomData() {
            return Array.from({ length: 6 }, () => Math.floor(Math.random() * 100));
        }
    }
};
</script>

<style scoped>
/* Add any required styles here */
</style>