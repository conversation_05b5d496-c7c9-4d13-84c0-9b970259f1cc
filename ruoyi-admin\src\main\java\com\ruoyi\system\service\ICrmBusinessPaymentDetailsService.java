package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.CrmBusinessPaymentDetails;

/**
 * 回款明细Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
public interface ICrmBusinessPaymentDetailsService 
{
    /**
     * 查询回款明细
     * 
     * @param id 回款明细主键
     * @return 回款明细
     */
    public CrmBusinessPaymentDetails selectCrmBusinessPaymentDetailsById(Long id);

    /**
     * 查询回款明细列表
     * 
     * @param crmBusinessPaymentDetails 回款明细
     * @return 回款明细集合
     */
    public List<CrmBusinessPaymentDetails> selectCrmBusinessPaymentDetailsList(CrmBusinessPaymentDetails crmBusinessPaymentDetails);

    /**
     * 新增回款明细
     * 
     * @param crmBusinessPaymentDetails 回款明细
     * @return 结果
     */
    public int insertCrmBusinessPaymentDetails(CrmBusinessPaymentDetails crmBusinessPaymentDetails);

    /**
     * 修改回款明细
     * 
     * @param crmBusinessPaymentDetails 回款明细
     * @return 结果
     */
    public int updateCrmBusinessPaymentDetails(CrmBusinessPaymentDetails crmBusinessPaymentDetails);

    /**
     * 批量删除回款明细
     * 
     * @param ids 需要删除的回款明细主键集合
     * @return 结果
     */
    public int deleteCrmBusinessPaymentDetailsByIds(Long[] ids);

    /**
     * 删除回款明细信息
     * 
     * @param id 回款明细主键
     * @return 结果
     */
    public int deleteCrmBusinessPaymentDetailsById(Long id);
}
